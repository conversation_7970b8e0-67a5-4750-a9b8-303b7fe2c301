package vip.xiaonuo.choiceway.modular.process.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.choiceway.modular.process.entity.MesProcess;
import vip.xiaonuo.choiceway.modular.process.param.*;
import vip.xiaonuo.choiceway.modular.process.service.MesProcessService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * MES流程控制器
 *
 * <AUTHOR>
 * @date 2025/07/23
 */
@Tag(name = "MES流程控制器")
@RestController
@Validated
public class MesProcessController {

    @Resource
    private MesProcessService mesProcessService;

    /**
     * 获取流程分页
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取流程分页")
    @SaCheckPermission("/choiceway/process/page")
    @GetMapping("/choiceway/process/page")
    public CommonResult<Page<MesProcess>> page(MesProcessPageParam mesProcessPageParam) {
        return CommonResult.data(mesProcessService.page(mesProcessPageParam));
    }

    /**
     * 添加流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "添加流程")
    @CommonLog("添加流程")
    @SaCheckPermission("/choiceway/process/add")
    @PostMapping("/choiceway/process/add")
    public CommonResult<String> add(@RequestBody @Valid MesProcessAddParam mesProcessAddParam) {
        mesProcessService.add(mesProcessAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "编辑流程")
    @CommonLog("编辑流程")
    @SaCheckPermission("/choiceway/process/edit")
    @PostMapping("/choiceway/process/edit")
    public CommonResult<String> edit(@RequestBody @Valid MesProcessEditParam mesProcessEditParam) {
        mesProcessService.edit(mesProcessEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "删除流程")
    @CommonLog("删除流程")
    @SaCheckPermission("/choiceway/process/delete")
    @PostMapping("/choiceway/process/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                                List<MesProcessIdParam> mesProcessIdParamList) {
        mesProcessService.delete(mesProcessIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取流程详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取流程详情")
    @SaCheckPermission("/choiceway/process/detail")
    @GetMapping("/choiceway/process/detail")
    public CommonResult<MesProcess> detail(@Valid MesProcessIdParam mesProcessIdParam) {
        return CommonResult.data(mesProcessService.detail(mesProcessIdParam));
    }

    /**
     * 创建新版本流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "创建新版本流程")
    @CommonLog("创建新版本流程")
    @SaCheckPermission("/choiceway/process/version")
    @PostMapping("/choiceway/process/createNewVersion")
    public CommonResult<String> createNewVersion(@RequestBody @Valid MesProcessVersionParam mesProcessVersionParam) {
        mesProcessService.createNewVersion(mesProcessVersionParam);
        return CommonResult.ok();
    }

    /**
     * 启用流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "启用流程")
    @CommonLog("启用流程")
    @SaCheckPermission("/choiceway/process/enable")
    @PostMapping("/choiceway/process/enable")
    public CommonResult<String> enableProcess(@RequestBody @Valid MesProcessIdParam mesProcessIdParam) {
        mesProcessService.enableProcess(mesProcessIdParam);
        return CommonResult.ok();
    }

    /**
     * 禁用流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "禁用流程")
    @CommonLog("禁用流程")
    @SaCheckPermission("/choiceway/process/disable")
    @PostMapping("/choiceway/process/disable")
    public CommonResult<String> disableProcess(@RequestBody @Valid MesProcessIdParam mesProcessIdParam) {
        mesProcessService.disableProcess(mesProcessIdParam);
        return CommonResult.ok();
    }

    /**
     * 获取启用的流程列表
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "获取启用的流程列表")
    @SaCheckPermission("/choiceway/process/enabledList")
    @GetMapping("/choiceway/process/enabledList")
    public CommonResult<List<MesProcess>> getEnabledProcessList() {
        return CommonResult.data(mesProcessService.getEnabledProcessList());
    }

    /**
     * 保存流程配置（包含节点信息）
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    @Operation(summary = "保存流程配置（包含节点信息）")
    @CommonLog("保存流程配置")
    @SaCheckPermission("/choiceway/process/saveWithNodes")
    @PostMapping("/choiceway/process/saveWithNodes")
    public CommonResult<String> saveWithNodes(@RequestBody @Valid MesProcessWithNodesParam param) {
        if(param.getId() != null) {
            // 编辑模式
            MesProcessEditParam editParam = new MesProcessEditParam();
            editParam.setId(param.getId());
            editParam.setProcessName(param.getProcessName());
            editParam.setDescription(param.getDescription());
            editParam.setProcessConfig(param.getProcessConfig());
            editParam.setEnabled(param.getEnabled());
            editParam.setStatus(param.getStatus());
            editParam.setSortCode(param.getSortCode());
            mesProcessService.updateProcessWithNodes(editParam, param.getNodeList());
        } else {
            // 新增模式
            MesProcessAddParam addParam = new MesProcessAddParam();
            addParam.setProcessName(param.getProcessName());
            addParam.setDescription(param.getDescription());
            addParam.setProcessConfig(param.getProcessConfig());
            addParam.setEnabled(param.getEnabled());
            addParam.setStatus(param.getStatus());
            addParam.setSortCode(param.getSortCode());
            mesProcessService.saveProcessWithNodes(addParam, param.getNodeList());
        }
        return CommonResult.ok();
    }
}
