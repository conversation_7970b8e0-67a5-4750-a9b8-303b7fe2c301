package vip.xiaonuo.core.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 连接配置启动监听器
 * 
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
@Component
public class ConnectionStartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("=== Connection Configuration Status ===");
        log.info("Application started successfully with optimized connection settings");
        log.info("- Max Connections: 8192");
        log.info("- Max Threads: 200");
        log.info("- Connection Timeout: 30000ms");
        log.info("- Keep-Alive Timeout: 60000ms");
        log.info("- NIO Endpoint error logging level: WARN");
        log.info("=====================================");
        
        // 输出监控端点信息
        log.info("Connection monitoring endpoints available:");
        log.info("- GET /monitor/connections - View current connection status");
        log.info("- GET /monitor/health-advice - Get connection health recommendations");
        
        // 输出问题解决建议
        log.info("If you still see 'remote host forcibly closed connection' errors:");
        log.info("1. Check client-side connection pooling and timeout settings");
        log.info("2. Monitor network stability between clients and server");
        log.info("3. Consider implementing client-side retry mechanisms");
        log.info("4. Review load balancer or proxy configurations if applicable");
    }
}
