package vip.xiaonuo.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.apache.coyote.http11.Http11NioProtocol;

/**
 * 连接优化配置 - 专门解决NIO连接问题
 * 
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
@Configuration
public class ConnectionOptimizationConfig {

    /**
     * Tomcat连接优化配置
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> connectionOptimizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                if (connector.getProtocolHandler() instanceof Http11NioProtocol) {
                    Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
                    
                    // 基础连接配置
                    protocol.setMaxConnections(8192);
                    protocol.setAcceptCount(1000);
                    protocol.setMaxThreads(200);
                    protocol.setMinSpareThreads(10);
                    
                    // 关键超时配置 - 解决连接被强制关闭的问题
                    protocol.setConnectionTimeout(30000); // 30秒连接超时
                    protocol.setKeepAliveTimeout(60000);   // 60秒保持连接
                    protocol.setMaxKeepAliveRequests(100); // 最大保持连接请求数
                    
                    // Socket配置优化
                    protocol.setTcpNoDelay(true);          // 禁用Nagle算法，减少延迟
                    protocol.setSoTimeout(30000);          // Socket超时30秒
                    
                    // 处理器缓存配置
                    protocol.setProcessorCache(200);
                    
                    log.info("Connection optimization applied: MaxConnections={}, ConnectionTimeout={}ms, KeepAliveTimeout={}ms", 
                            protocol.getMaxConnections(), protocol.getConnectionTimeout(), protocol.getKeepAliveTimeout());
                }
            });
        };
    }
}
