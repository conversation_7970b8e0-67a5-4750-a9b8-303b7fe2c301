package vip.xiaonuo.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.apache.coyote.http11.Http11NioProtocol;

/**
 * 简化的连接配置 - 只使用确定存在的方法
 * 
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
@Configuration
public class SimpleConnectionConfig {

    /**
     * 简化的Tomcat连接优化配置
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> simpleConnectionOptimizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                if (connector.getProtocolHandler() instanceof Http11NioProtocol) {
                    Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
                    
                    // 只使用确定存在的基础配置方法
                    protocol.setMaxConnections(8192);        // 最大连接数
                    protocol.setAcceptCount(1000);           // 等待队列长度
                    protocol.setMaxThreads(200);             // 最大线程数
                    protocol.setMinSpareThreads(10);         // 最小空闲线程数
                    protocol.setConnectionTimeout(30000);    // 连接超时30秒
                    protocol.setKeepAliveTimeout(60000);     // Keep-Alive超时60秒
                    protocol.setMaxKeepAliveRequests(100);   // 最大Keep-Alive请求数
                    protocol.setTcpNoDelay(true);            // 禁用Nagle算法
                    protocol.setProcessorCache(200);         // 处理器缓存
                    
                    log.info("Simple connection optimization applied successfully");
                    log.info("MaxConnections: {}, MaxThreads: {}, ConnectionTimeout: {}ms", 
                            protocol.getMaxConnections(), protocol.getMaxThreads(), protocol.getConnectionTimeout());
                }
            });
        };
    }
}
