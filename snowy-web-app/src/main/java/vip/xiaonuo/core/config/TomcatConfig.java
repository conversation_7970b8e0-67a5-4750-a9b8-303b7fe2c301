package vip.xiaonuo.core.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.apache.coyote.http11.Http11NioProtocol;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Tomcat配置优化
 * 
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
@Configuration
public class TomcatConfig {

    /**
     * 自定义Tomcat配置
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
                
                // 连接池配置
                protocol.setMaxConnections(8192);
                protocol.setAcceptCount(1000);
                protocol.setMaxThreads(200);
                protocol.setMinSpareThreads(10);
                
                // 超时配置
                protocol.setConnectionTimeout(20000);
                protocol.setKeepAliveTimeout(60000);
                protocol.setMaxKeepAliveRequests(100);
                
                // NIO优化配置
                protocol.setProcessorCache(200);
                protocol.setMaxHttpFormPostSize(2 * 1024 * 1024); // 2MB
                protocol.setMaxSwallowSize(2 * 1024 * 1024); // 2MB
                
                // 启用压缩
                protocol.setCompression("on");
                protocol.setCompressionMinSize(2048);
                protocol.setCompressibleMimeType("text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml");
                
                // 禁用不安全的HTTP方法
                protocol.setAllowedRequestAttributesPattern(".*");
                
                log.info("Tomcat connector customized successfully");
            });
            
            // 设置错误页面
            factory.addErrorPages();
        };
    }
    
    /**
     * 创建额外的连接器用于内部通信（可选）
     */
    @Bean
    public Connector createInternalConnector() {
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
        
        // 内部连接器配置
        connector.setPort(8082); // 内部端口
        protocol.setMaxConnections(1000);
        protocol.setMaxThreads(50);
        protocol.setConnectionTimeout(30000);
        
        return connector;
    }
}
