package vip.xiaonuo.core.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 连接测试控制器
 * 
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class ConnectionTestController {

    /**
     * 简单的连接测试端点
     */
    @GetMapping("/connection")
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        result.put("message", "Connection test successful");
        result.put("timestamp", System.currentTimeMillis());
        result.put("serverInfo", "Tomcat with optimized NIO configuration");
        
        log.debug("Connection test endpoint accessed successfully");
        return result;
    }

    /**
     * 长连接测试端点
     */
    @GetMapping("/long-connection")
    public Map<String, Object> testLongConnection() throws InterruptedException {
        Map<String, Object> result = new HashMap<>();
        
        // 模拟长时间处理
        Thread.sleep(5000); // 5秒
        
        result.put("status", "success");
        result.put("message", "Long connection test completed");
        result.put("processingTime", "5000ms");
        result.put("timestamp", System.currentTimeMillis());
        
        log.debug("Long connection test completed successfully");
        return result;
    }

    /**
     * 获取服务器状态
     */
    @GetMapping("/server-status")
    public Map<String, Object> getServerStatus() {
        Map<String, Object> status = new HashMap<>();
        
        Runtime runtime = Runtime.getRuntime();
        status.put("availableProcessors", runtime.availableProcessors());
        status.put("maxMemory", runtime.maxMemory());
        status.put("totalMemory", runtime.totalMemory());
        status.put("freeMemory", runtime.freeMemory());
        status.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        
        // 计算内存使用率
        double memoryUsage = (double)(runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory() * 100;
        status.put("memoryUsagePercent", String.format("%.2f%%", memoryUsage));
        
        status.put("timestamp", System.currentTimeMillis());
        status.put("uptime", System.currentTimeMillis());
        
        return status;
    }
}
