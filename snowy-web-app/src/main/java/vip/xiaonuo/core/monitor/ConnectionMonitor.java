package vip.xiaonuo.core.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;
import java.util.HashMap;
import java.util.Map;

/**
 * 连接监控器
 * 
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
@Component
@RestController
@RequestMapping("/monitor")
public class ConnectionMonitor implements HealthIndicator {

    private final MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();

    @Override
    public Health health() {
        try {
            Map<String, Object> details = getTomcatConnectionInfo();
            return Health.up().withDetails(details).build();
        } catch (Exception e) {
            return Health.down().withException(e).build();
        }
    }

    /**
     * 获取连接状态信息的REST接口
     */
    @GetMapping("/connections")
    public Map<String, Object> getConnectionStatus() {
        try {
            return getTomcatConnectionInfo();
        } catch (Exception e) {
            log.error("Failed to get connection info", e);
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            return errorInfo;
        }
    }

    /**
     * 获取Tomcat连接信息
     */
    private Map<String, Object> getTomcatConnectionInfo() throws Exception {
        Map<String, Object> info = new HashMap<>();
        
        try {
            // 获取Tomcat连接器信息
            ObjectName connectorName = new ObjectName("Tomcat:type=Connector,port=82");
            if (mBeanServer.isRegistered(connectorName)) {
                // 基本连接信息
                info.put("maxConnections", mBeanServer.getAttribute(connectorName, "maxConnections"));
                info.put("connectionCount", mBeanServer.getAttribute(connectorName, "connectionCount"));
                info.put("acceptCount", mBeanServer.getAttribute(connectorName, "acceptCount"));
            }
            
            // 获取线程池信息
            ObjectName threadPoolName = new ObjectName("Tomcat:type=ThreadPool,name=\"http-nio-82\"");
            if (mBeanServer.isRegistered(threadPoolName)) {
                info.put("currentThreadCount", mBeanServer.getAttribute(threadPoolName, "currentThreadCount"));
                info.put("currentThreadsBusy", mBeanServer.getAttribute(threadPoolName, "currentThreadsBusy"));
                info.put("maxThreads", mBeanServer.getAttribute(threadPoolName, "maxThreads"));
            }
            
            // 获取请求处理器信息
            ObjectName requestProcessorName = new ObjectName("Tomcat:type=GlobalRequestProcessor,name=\"http-nio-82\"");
            if (mBeanServer.isRegistered(requestProcessorName)) {
                info.put("requestCount", mBeanServer.getAttribute(requestProcessorName, "requestCount"));
                info.put("errorCount", mBeanServer.getAttribute(requestProcessorName, "errorCount"));
                info.put("processingTime", mBeanServer.getAttribute(requestProcessorName, "processingTime"));
            }
            
        } catch (Exception e) {
            log.warn("Could not retrieve some Tomcat metrics: {}", e.getMessage());
            info.put("warning", "Some metrics unavailable: " + e.getMessage());
        }
        
        // 添加系统信息
        info.put("timestamp", System.currentTimeMillis());
        info.put("availableProcessors", Runtime.getRuntime().availableProcessors());
        info.put("maxMemory", Runtime.getRuntime().maxMemory());
        info.put("freeMemory", Runtime.getRuntime().freeMemory());
        
        return info;
    }

    /**
     * 获取连接健康建议
     */
    @GetMapping("/health-advice")
    public Map<String, Object> getHealthAdvice() {
        Map<String, Object> advice = new HashMap<>();
        
        try {
            Map<String, Object> connectionInfo = getTomcatConnectionInfo();
            
            // 分析连接状态并给出建议
            if (connectionInfo.containsKey("connectionCount") && connectionInfo.containsKey("maxConnections")) {
                int connectionCount = (Integer) connectionInfo.get("connectionCount");
                int maxConnections = (Integer) connectionInfo.get("maxConnections");
                double usage = (double) connectionCount / maxConnections;
                
                if (usage > 0.8) {
                    advice.put("connectionUsage", "HIGH");
                    advice.put("recommendation", "Consider increasing maxConnections or optimizing client connection handling");
                } else if (usage > 0.6) {
                    advice.put("connectionUsage", "MEDIUM");
                    advice.put("recommendation", "Monitor connection patterns and consider optimization");
                } else {
                    advice.put("connectionUsage", "LOW");
                    advice.put("recommendation", "Connection usage is healthy");
                }
            }
            
            advice.put("generalAdvice", "Monitor for frequent 'remote host forcibly closed connection' errors and consider client-side connection pooling");
            
        } catch (Exception e) {
            advice.put("error", "Could not analyze connection health: " + e.getMessage());
        }
        
        return advice;
    }
}
