// vite.config.mjs
import { resolve } from "path";
import { defineConfig, loadEnv } from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/vite/dist/node/index.js";
import vue from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import Components from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/unplugin-vue-components/dist/vite.js";
import VueJSX from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import AutoImport from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/unplugin-auto-import/dist/vite.js";
import vueSetupExtend from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
import { visualizer } from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import Less2CssVariablePlugin from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/antd-less-to-css-variable/dist/index.js";
import viteCompression from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/vite-plugin-compression/dist/index.mjs";
import { theme } from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/ant-design-vue/lib/index.js";
import convertLegacyToken from "file:///F:/snowy-master/snowy-master/snowy-admin-web/node_modules/ant-design-vue/lib/theme/convertLegacyToken.js";
var __vite_injected_original_dirname = "F:\\snowy-master\\snowy-master\\snowy-admin-web";
var { defaultAlgorithm, defaultSeed } = theme;
var mapToken = defaultAlgorithm(defaultSeed);
var v3Token = convertLegacyToken.default(mapToken);
var r = (...args) => resolve(__vite_injected_original_dirname, ".", ...args);
var vite_config_default = defineConfig(({ command, mode }) => {
  const envConfig = loadEnv(mode, "./");
  const alias = {
    "~": `${resolve(__vite_injected_original_dirname, "./")}`,
    "@/": `${resolve(__vite_injected_original_dirname, "src")}/`
  };
  return {
    server: {
      port: envConfig.VITE_PORT,
      proxy: {
        "/api": {
          target: envConfig.VITE_API_BASEURL,
          ws: false,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, "")
        }
      }
    },
    resolve: {
      alias
    },
    // 解决警告You are running the esm-bundler build of vue-i18n.
    define: {
      __VUE_I18N_FULL_INSTALL__: true,
      __VUE_I18N_LEGACY_API__: true,
      __VUE_I18N_PROD_DEVTOOLS__: true,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: true
    },
    build: {
      // sourcemap: true,
      manifest: true,
      brotliSize: false,
      rollupOptions: {
        output: {
          manualChunks: {
            echarts: ["echarts"],
            "ant-design-vue": ["ant-design-vue"],
            vue: ["vue", "vue-router", "pinia", "vue-i18n"]
          }
        }
      },
      chunkSizeWarningLimit: 1e3
    },
    plugins: [
      vue({
        script: {
          refTransform: true
        }
      }),
      viteCompression(),
      vueSetupExtend(),
      VueJSX(),
      AutoImport({
        imports: ["vue"],
        dirs: ["./src/utils/permission"],
        dts: r("src/auto-imports.d.ts")
      }),
      // 组件按需引入
      Components({
        dirs: [r("src/components")],
        dts: false,
        resolvers: []
      }),
      visualizer()
    ],
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          plugins: [new Less2CssVariablePlugin({
            // TODO：有必要用的情况下，是否需要传入 variables，可能会造成重复引用
            variables: { ...v3Token }
          })],
          modifyVars: v3Token
        }
      }
    },
    optimizeDeps: {}
  };
});
export {
  vite_config_default as default,
  r
};
//# sourceMappingURL=data:application/json;base64,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
