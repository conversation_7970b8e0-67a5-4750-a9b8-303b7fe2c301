<template>
    <%
    var searchCount = 0;
    var row = 0;
    %>
    <% for(var i = 0; i < configList.~size; i++) { %>
       <% if(!configList[i].needTableId && configList[i].needPage) { searchCount ++; }%>
    <% } %>
    <uv-popup ref="popRef" mode="bottom" bg-color="null" z-index="99">
        <view class="container">
            <view class="close">
                <icon type="clear" :size="20" color="#5677fc" @click="close"></icon>
            </view>
            <% if (searchCount > 0) { %>
            <uv-form ref="formRef" :model="searchFormState" label-position="top" labelWidth="auto" :labelStyle="{marginBottom: '25rpx', fontSize: '27rpx', color: '#606266'}">
                <% for(var i = 0; i < configList.~size; i++) { %>
                <% if(!configList[i].needTableId && configList[i].needPage) { %>
                <% if(configList[i].effectType == 'input' || configList[i].effectType == 'textarea') { %>
                <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}">
                    <uv-input v-model="searchFormState.${configList[i].fieldNameCamelCase}" placeholder="请输入${configList[i].fieldRemark}"></uv-input>
                </uv-form-item>
                <% } else if (configList[i].effectType == 'select' || configList[i].effectType == 'radio' || configList[i].effectType == 'checkbox') {%>
                <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}">
                    <snowy-sel-picker :map="{key: 'value', label: 'text'}" v-model="searchFormState.${configList[i].fieldNameCamelCase}" :rangeData="${configList[i].fieldNameCamelCase}Options" placeholder="请选择${configList[i].fieldRemark}"></snowy-sel-picker>
                </uv-form-item>
                <% } else {%>
                <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}">
                    <uv-input v-model="searchFormState.${configList[i].fieldNameCamelCase}" placeholder="请输入${configList[i].fieldRemark}"></uv-input>
                </uv-form-item>
                <% } %>
                <% } %>
                <% } %>
            </uv-form>
            <% } %>
            <uv-row :gutter="10">
                <uv-col :span="6">
                    <tui-button height="90rpx" type="gray" @click="reset">重置</tui-button>
                </uv-col>
                <uv-col :span="6">
                    <tui-button height="90rpx" type="primary" @click="confirm">确认</tui-button>
                </uv-col>
            </uv-row>
        </view>
    </uv-popup>
</template>
<script setup name="${classNameFirstLower}Search">
    import { ref } from "vue"
    const emits = defineEmits(['reset', 'confirm'])
    const props = defineProps({
    	searchFormState: {
            type: Object,
            required: true
    	},
    })
    <% if (searchCount > 0) { %>
    <% for(var i = 0; i < configList.~size; i++) { %>
    <% if(!configList[i].needTableId && configList[i].needPage) { %>
    <% if (configList[i].effectType == 'select' || configList[i].effectType == 'radio' || configList[i].effectType == 'checkbox') { %>
    const ${configList[i].fieldNameCamelCase}Options = uni.$snowy.tool.dictList('${configList[i].dictTypeCode}')
    <% } %>
    <% } %>
    <% } %>
    <% } %>
    const popRef = ref()
    const open = () => {
    	popRef.value.open()
    }
    const reset = () =>{
        // 重置数据
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId && configList[i].needPage) { %>
        props.searchFormState.${configList[i].fieldNameCamelCase} = ''
        <% } %>
        <% } %>
    	emits('reset', props.searchFormState)
    }
    const confirm = () => {
    	popRef.value.close()
    	emits('confirm', props.searchFormState)
    }
    const close = () => {
        popRef.value.close()
    }
    defineExpose({
    	open
    })
</script>
<style lang="scss" scoped>
    .container {
        margin: 5rpx;
        border-radius: 10rpx;
        padding: 20rpx;
        background-color: white;
        .close {
            display: flex;
            justify-content: flex-end;
        }
    }
</style>
