<template>
    <view class="container snowy-shadow">
        <uv-form ref="formRef" :model="formData" :rules="rules" label-position="top" labelWidth="auto" :labelStyle="{marginBottom: '25rpx', fontSize: '27rpx', color: '#606266'}">
            <% for(var i = 0; i < configList.~size; i++) { %>
            <% if(!configList[i].needTableId && configList[i].whetherAddUpdate && configList[i].fieldNameCamelCase != 'tenantId') { %>
            <% if(configList[i].effectType == 'input') { %>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
            	<uv-input v-model="formData.${configList[i].fieldNameCamelCase}" placeholder="请输入${configList[i].fieldRemark}"></uv-input>
            </uv-form-item>
            <% } else if (configList[i].effectType == 'textarea') {%>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
            	<uv-textarea v-model="formData.${configList[i].fieldNameCamelCase}" placeholder="请输入${configList[i].fieldRemark}"></uv-textarea>
            </uv-form-item>
            <% } else if (configList[i].effectType == 'select') {%>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
                <snowy-sel-picker :map="{key: 'value', label: 'text'}" v-model="formData.${configList[i].fieldNameCamelCase}" :rangeData="${configList[i].fieldNameCamelCase}Options" placeholder="请选择${configList[i].fieldRemark}"></snowy-sel-picker>
            </uv-form-item>
            <% } else if (configList[i].effectType == 'radio') {%>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
                <uv-radio-group v-model="formData.${configList[i].fieldNameCamelCase}">
                    <uv-radio :customStyle="{marginRight: '50rpx'}" v-for="(item, index) in ${configList[i].fieldNameCamelCase}Options" :key="index" :name="item.value" :label="item.text"></uv-radio>
                </uv-radio-group>
            </uv-form-item>
            <% } else if (configList[i].effectType == 'checkbox') {%>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
                <uv-checkbox-group v-model="formData.${configList[i].fieldNameCamelCase}">
                    <uv-checkbox :customStyle="{marginRight: '50rpx'}" v-for="(item, index) in ${configList[i].fieldNameCamelCase}Options" :key="index"  :name="item.value" :label="item.text"></uv-checkbox>
                </uv-checkbox-group>
            </uv-form-item>
            <% } else if (configList[i].effectType == 'datepicker') {%>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
                <snowy-calendar v-model="formData.${configList[i].fieldNameCamelCase}" placeholder="请输入${configList[i].fieldRemark}"></snowy-calendar>
            </uv-form-item>
            <% } else if (configList[i].effectType == 'timepicker') {%>

            <% } else if (configList[i].effectType == 'inputNumber') {%>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
                <uv-number-box v-model="formData.${configList[i].fieldNameCamelCase}" :min="1" :max="100"></uv-number-box>
            </uv-form-item>
            <% } else if (configList[i].effectType == 'slider') {%>
            <uv-form-item label="${configList[i].fieldRemark}" prop="${configList[i].fieldNameCamelCase}" :required="${configList[i].required}">
                <slider :value="formData.${configList[i].fieldNameCamelCase}" :min="1" :max="100" :step="1" @change="(e)=>{formData.${configList[i].fieldNameCamelCase} = e.detail.value}"></slider>
            </uv-form-item>
            <% } %>
            <% } %>
            <% } %>
        </uv-form>
        <tui-button margin="50rpx 0" :preventClick="true" :shadow="true" @click="submit">提交</tui-button>
    </view>
</template>
<script setup name="${classNameFirstLower}Form">
    import { onLoad } from "@dcloudio/uni-app"
    import { ${classNameFirstLower}Detail, ${classNameFirstLower}SubmitForm } from '@/api/${moduleName}/${classNameFirstLower}Api'
    import { reactive, ref, getCurrentInstance } from "vue"

    const { proxy } = getCurrentInstance()
    const formRef = ref()
    const formData = ref({
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId && configList[i].whetherAddUpdate && configList[i].fieldNameCamelCase != 'tenantId') { %>
        ${configList[i].fieldNameCamelCase}: '',
        <% } %>
        <% } %>
    })
    // https://www.uvui.cn/components/form.html
    // 去pages/biz/user/form.vue中寻找示例
    const rules = reactive({
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId && configList[i].whetherAddUpdate && configList[i].fieldNameCamelCase != 'tenantId' && configList[i].required) { %>
        ${configList[i].fieldNameCamelCase}: [{
            type: 'string',
            required: true,
            message: '请输入${configList[i].fieldRemark}',
            trigger: ['blur', 'change']
        }],
        <% } %>
        <% } %>
    })
    <% for(var i = 0; i < configList.~size; i++) { %>
    <% if(!configList[i].needTableId) { %>
    <% if(configList[i].effectType == 'select' || configList[i].effectType == 'radio' || configList[i].effectType == 'checkbox') { %>
    const ${configList[i].fieldNameCamelCase}Options = ref([])
    <% } %>
    <% } %>
    <% } %>
    onLoad((option) => {
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId) { %>
        <% if(configList[i].effectType == 'select' || configList[i].effectType == 'radio' || configList[i].effectType == 'checkbox') { %>
        ${configList[i].fieldNameCamelCase}Options.value = uni.$snowy.tool.dictList('${configList[i].dictTypeCode}')
        <% } %>
        <% } %>
        <% } %>
        if(!option.id){
            return
        }
        ${classNameFirstLower}Detail({
            id: option.id
        }).then(res => {
            formData.value = res?.data
        })
    })
    const submit = () => {
        formRef.value.validate().then(res => {
            ${classNameFirstLower}SubmitForm(formData.value, !formData.value.id).then(respond => {
                uni.$emit('formBack', {
                    data: respond.data
                })
                uni.navigateBack({
                    delta: 1
                })
            })
        })
    }
</script>
<style lang="scss" scoped>
    .container {
        margin: 80rpx 0 0;
        padding: 30rpx;
    }
</style>
