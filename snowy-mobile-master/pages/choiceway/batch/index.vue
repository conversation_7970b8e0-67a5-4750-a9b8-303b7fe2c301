<!--批次管理-->
<template>
    <view class="batch-container">
        <!-- 页面标题 -->
        <!-- <view class="page-header">
            <view class="header-left" @tap="goBack">
                <uv-icon name="arrow-left" size="24" color="#333"></uv-icon>
            </view>
            <view class="header-center">
                <uv-icon name="plus-circle-fill" size="24" color="#5677fc"></uv-icon>
                <text class="page-title">新建批次</text>
            </view>
            <view class="header-right"></view>
        </view> -->

        <!-- 表单区域 -->
        <view class="form-content">
            <!-- 批次ID -->
            <view class="input-item">
                <view class="input-label">
                    <text class="required">*</text>
                    <text>批次ID</text>
                </view>
                <view class="input-wrapper">
                    <uv-icon class="icon" name="scan" size="20" color="#999"></uv-icon>
                    <input
                        v-model="formData.lotId"
                        class="input"
                        type="text"
                        placeholder="请输入批次ID"
                        maxlength="50"
                        @confirm="getInventoryInformation"
                    />
                    <view class="scan-btn" @tap="scanCode">
                        <uv-icon name="scan" size="20" color="#5677fc"></uv-icon>
                    </view>
                </view>
                <view class="error-msg" v-if="errors.lotId">{{ errors.lotId }}</view>
            </view>

            <!-- 产品名称 -->
            <view class="input-item">
                <view class="input-label">
                    <text class="required">*</text>
                    <text>产品名称</text>
                </view>
                <view class="input-wrapper">
                  <uv-icon class="icon" name="file-text-fill" size="20" color="#999"></uv-icon>
                    <input
                        v-model="formData.productName"
                        class="input"
                        type="text"
                        placeholder="请输入产品名称"
                        maxlength="100"
                    />
                </view>
                <view class="error-msg" v-if="errors.productName">{{ errors.productName }}</view>
            </view>

            <!-- 流程和站点选择 -->
            <view class="input-item">
                <view class="input-label">
                    <text class="required">*</text>
                    <text>流程和站点</text>
                </view>
                <view class="input-wrapper" @tap="openProcessStationPicker">
                    <uv-icon class="icon" name="list" size="20" color="#999"></uv-icon>
                    <text class="input select-input" :class="{ placeholder: !getProcessStationDisplay() }">
                        {{ getProcessStationDisplay() || '请选择流程和站点' }}
                    </text>
                    <uv-icon name="arrow-down" size="16" color="#c0c4cc"></uv-icon>
                </view>
                <view class="error-msg" v-if="errors.processId">{{ errors.processId }}</view>
                <view class="error-msg" v-if="errors.currentStationId">{{ errors.currentStationId }}</view>
            </view>

            <!-- 颗粒数 -->
            <view class="input-item">
                <view class="input-label">
                    <text class="required">*</text>
                    <text>颗粒数</text>
                </view>
                <view class="number-input-wrapper">
                    <view class="number-controls">
                        <button
                            class="number-btn decrease"
                            @tap="decreaseCount"
                            :disabled="formData.particleCount <= 0"
                        >-</button>
                        <input
                            class="number-input"
                            v-model.number="formData.particleCount"
                            type="number"
                            min="0"
                            max="999999"
                        />
                        <button
                            class="number-btn increase"
                            @tap="increaseCount"
                        >+</button>
                    </view>
                </view>
                <view class="error-msg" v-if="errors.particleCount">{{ errors.particleCount }}</view>
            </view>

            <!-- 备注 -->
            <view class="input-item">
                <view class="input-label">
                    <text>备注</text>
                </view>
                <view class="textarea-wrapper">
                    <uv-icon class="icon" name="edit-pen-fill" size="20" color="#999"></uv-icon>
                    <textarea
                        v-model="formData.remarks"
                        class="textarea"
                        placeholder="请输入备注"
                        maxlength="500"
                    ></textarea>
                </view>
                <view class="char-count">{{ formData.remarks.length }}/500</view>
            </view>
        </view>

        <!-- 操作按钮 -->
        <view class="button-section">
            <button
                class="btn btn-cancel"
                @tap="resetForm"
                :disabled="submitting"
            >
                重置
            </button>
            <button
                class="btn btn-submit"
                @tap="submitFormData"
                :disabled="submitting"
            >
                {{ submitting ? '提交中...' : '确定' }}
            </button>
        </view>

        <!-- 加载提示 -->
        <uv-loading-page :loading="submitting" loadingText="提交中..."></uv-loading-page>
    </view>

    <!-- 流程和站点联动选择器 -->
    <uv-picker
        ref="processStationPicker"
        :columns="columns"
        @confirm="onProcessStationConfirm"
        @cancel="onProcessStationCancel"
        @change="onProcessStationChange"
    ></uv-picker>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { submitForm } from '@/api/choiceway/mesBatchApi.js'
import { getEnabledProcessList, getNodesByProcessId } from '@/api/choiceway/mesProcessApi.js'

const { proxy } = getCurrentInstance()

// 响应式数据
const formData = reactive({
    lotId: '',
    productName: '',
    processId: '',
    processName: '',
    currentStationId: '',
    currentStationName: '',
    particleCount: 200,
    remarks: ''
})

// 错误信息
const errors = reactive({
    lotId: '',
    productName: '',
    processId: '',
    currentStationId: '',
    particleCount: ''
})

const submitting = ref(false)

// 流程相关
const processList = ref([])
const selectedProcessName = ref('')

// 站点相关
const stationList = ref([])
const selectedStationName = ref('')

// 多列联动选择器相关
const processStationPicker = ref(null)
const columns = ref([[], []])
const columnData = ref([]) // 存储每个流程对应的站点数据

// 页面加载时执行
onMounted(() => {
    console.log('批次管理页面加载完成')
    loadProcessList()
})

// 返回上一页
const goBack = () => {
    uni.navigateBack()
}

// 扫码功能
const scanCode = () => {
    console.log('开始扫码')
    uni.scanCode({
        success: (res) => {
            console.log('扫码成功:', res)
            formData.lotId = res.result
            // 可以在这里添加根据扫码结果获取批次基本信息的逻辑
            getBatchInfoByCode(res.result)
        },
        fail: (err) => {
            console.error('扫码失败:', err)
            uni.$snowy.modal.msgError('扫码失败')
        }
    })
}

// 根据扫码结果获取批次基本信息
const getBatchInfoByCode = async (code) => {
    try {
        console.log('根据扫码结果获取批次信息:', code)
        // 这里可以调用API获取批次的基本信息
        // 暂时只设置批次ID
        uni.$snowy.modal.msgSuccess('扫码成功，已填入批次ID')
		getInventoryInformation()
    } catch (error) {
        console.error('获取批次信息失败:', error)
        uni.$snowy.modal.msgError('获取批次信息失败')
    }
}

// 获取流程和站点显示文本
const getProcessStationDisplay = () => {
    if (selectedProcessName.value && selectedStationName.value) {
        return `${selectedProcessName.value} - ${selectedStationName.value}`
    }
    return ''
}

// 打开流程和站点联动选择器
const openProcessStationPicker = () => {
    if (processStationPicker.value) {
        processStationPicker.value.open()
    }
}

// 表单验证
const validateForm = () => {
    // 清空之前的错误信息
    Object.keys(errors).forEach(key => {
        errors[key] = ''
    })

    let isValid = true

    // 验证批次ID
    if (!formData.lotId.trim()) {
        errors.lotId = '请输入批次ID'
        isValid = false
    } else if (formData.lotId.length > 50) {
        errors.lotId = '批次ID不能超过50个字符'
        isValid = false
    }

    // 验证产品名称
    if (!formData.productName.trim()) {
        errors.productName = '请输入产品名称'
        isValid = false
    } else if (formData.productName.length > 100) {
        errors.productName = '产品名称不能超过100个字符'
        isValid = false
    }

    // 验证流程
    if (!formData.processId) {
        errors.processId = '请选择流程'
        isValid = false
    }

    // 验证站点
    if (!formData.currentStationId) {
        errors.currentStationId = '请选择站点'
        isValid = false
    }

    // 验证颗粒数
    if (formData.particleCount < 0) {
        errors.particleCount = '颗粒数不能小于0'
        isValid = false
    }

    return isValid
}

// 颗粒数增减
const increaseCount = () => {
    if (formData.particleCount < 999999) {
        formData.particleCount++
    }
}

const decreaseCount = () => {
    if (formData.particleCount > 0) {
        formData.particleCount--
    }
}

// 加载流程列表
const loadProcessList = async () => {
    try {
        console.log('开始加载流程列表...')
        const res = await getEnabledProcessList()
        console.log('流程列表响应:', res)
        if (res.code === 200) {
            processList.value = res.data || []
            // 初始化多列选择器数据
            await initializePickerData()
            console.log('流程列表加载成功:', processList.value)
        } else {
            console.error('流程列表加载失败:', res.message)
            uni.$snowy.modal.msgError(res.message || '加载流程列表失败')
        }
    } catch (error) {
        console.error('加载流程列表失败:', error)
        uni.$snowy.modal.msgError('加载流程列表失败')
    }
}

// 初始化多列选择器数据
const initializePickerData = async () => {
    try {
        // 第一列：流程名称
        const processNames = processList.value.map(item => item.processName)
        columns.value[0] = processNames

        // 为每个流程加载对应的站点数据
        columnData.value = []
        for (let i = 0; i < processList.value.length; i++) {
            const process = processList.value[i]
            const stationRes = await getNodesByProcessId(process.id)
            if (stationRes.code === 200) {
                // 只获取站点类型的节点
                const stations = (stationRes.data || []).filter(node => node.nodeType === 'station')
                const stationNames = stations.map(station => station.nodeName)
                columnData.value.push(stationNames)
            } else {
                columnData.value.push([])
            }
        }

        // 设置第二列默认数据（第一个流程的站点）
        if (columnData.value.length > 0) {
            columns.value[1] = columnData.value[0] || []
        }

        console.log('多列选择器数据初始化完成:', columns.value)
        console.log('站点数据:', columnData.value)
    } catch (error) {
        console.error('初始化选择器数据失败:', error)
    }
}

// 流程和站点联动选择器变化事件
const onProcessStationChange = (e) => {
    console.log('选择器变化:', e)
    const { columnIndex, index } = e

    // 当第一列（流程）发生变化时，更新第二列（站点）的数据
    if (columnIndex === 0) {
        if (processStationPicker.value && columnData.value[index]) {
            processStationPicker.value.setColumnValues(1, columnData.value[index])
        }
    }
}

// 流程和站点联动选择器确认事件
const onProcessStationConfirm = async (e) => {
    console.log('流程和站点选择确认:', e)
    const processIndex = e.indexs[0]
    const stationIndex = e.indexs[1]
    const processName = e.value[0]
    const stationName = e.value[1]

    console.log('选择的流程索引:', processIndex, '站点索引:', stationIndex)
    console.log('选择的流程名称:', processName, '站点名称:', stationName)

    // 获取选择的流程对象
    const selectedProcess = processList.value[processIndex]
    if (!selectedProcess) {
        console.error('未找到对应的流程对象')
        return
    }

    // 获取选择的站点对象
    const stationRes = await getNodesByProcessId(selectedProcess.id)
    if (stationRes.code === 200) {
        const stations = (stationRes.data || []).filter(node => node.nodeType === 'station')
        const selectedStation = stations[stationIndex]

        if (selectedStation) {
            // 更新表单数据
            formData.processId = selectedProcess.id
            formData.processName = selectedProcess.processName
            formData.currentStationId = selectedStation.id
            formData.currentStationName = selectedStation.nodeName

            // 更新显示名称
            selectedProcessName.value = selectedProcess.processName
            selectedStationName.value = selectedStation.nodeName

            // 清空相关错误信息
            errors.processId = ''
            errors.currentStationId = ''

            console.log('流程和站点选择完成:', {
                processId: formData.processId,
                processName: formData.processName,
                stationId: formData.currentStationId,
                stationName: formData.currentStationName
            })
        } else {
            console.error('未找到对应的站点对象')
        }
    }
}

// 流程和站点联动选择器取消事件
const onProcessStationCancel = () => {
    console.log('流程和站点选择取消')
}

// 重置表单
const resetForm = () => {
    console.log('重置表单')

    uni.$snowy.modal.confirm('确定要重置表单吗？').then(() => {
        // 重置表单数据
        Object.assign(formData, {
            lotId: '',
            productName: '',
            processId: '',
            processName: '',
            currentStationId: '',
            currentStationName: '',
            particleCount: 1,
            remarks: ''
        })

        // 清空选择器显示
        selectedProcessName.value = ''
        selectedStationName.value = ''

        // 清空站点列表
        stationList.value = []

        // 清空错误信息
        Object.keys(errors).forEach(key => {
            errors[key] = ''
        })

        uni.$snowy.modal.msgSuccess('表单已重置')
    }).catch(() => {
        console.log('取消重置')
    })
}

// 提交表单
const submitFormData = async () => {
    try {
        console.log('开始提交表单:', formData)

        // 表单验证
        if (!validateForm()) {
            console.log('表单验证失败')
            uni.$snowy.modal.msgError('请检查表单输入')
            return
        }

        console.log('表单验证通过')
        submitting.value = true

        const res = await submitForm(formData, true)
        console.log('提交响应:', res)

        if (res.code === 200) {
            console.log('批次创建成功')
            uni.$snowy.modal.msgSuccess('批次创建成功')

            // 重置表单
            setTimeout(() => {
                Object.assign(formData, {
                    lotId: '',
                    productName: '',
                    processId: '',
                    processName: '',
                    currentStationId: '',
                    currentStationName: '',
                    particleCount: 1,
                    remarks: ''
                })
                selectedProcessName.value = ''
                selectedStationName.value = ''
                stationList.value = []

                // 清空错误信息
                Object.keys(errors).forEach(key => {
                    errors[key] = ''
                })
            }, 1000)
        } else {
            console.error('批次创建失败:', res.message)
            uni.$snowy.modal.msgError(res.message || '批次创建失败')
        }
    } catch (error) {
        console.error('提交失败:', error)
        if (error.message) {
            uni.$snowy.modal.msgError(error.message)
        } else {
            uni.$snowy.modal.msgError('批次创建失败')
        }
    } finally {
        submitting.value = false
    }
}

// 用于获取批次产品名称和颗粒数
const getInventoryInformation = async () => {
  try {
    console.log('=== 获取批次资料开始 ===')
    console.log("批次ID:"+formData.lotId)

  //   请求链接获取资料（目前没有接口，使用模拟数据进行填充fom表单中的产品名称和颗粒数）
    formData.productName = "测试产品名称"
    formData.particleCount = 200
    console.log('=== 获取批次资料结束 ===')

  } catch (error) {
    console.log("获取批次："+formData.lotId+"异常，异常原因为："+error)
  }
}
</script>

<style lang="scss" scoped>
page {
    background-color: #f5f5f5;
}

.batch-container {
    width: 100%;
    min-height: 100vh;
    background: #f5f5f5;
}

.page-header {
    background: #fff;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.header-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-right {
    width: 60rpx;
    height: 60rpx;
}

.page-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-left: 15rpx;
}

.form-content {
    background: #fff;
    margin: 20rpx;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.input-item {
    margin-bottom: 40rpx;
}

.input-label {
    display: flex;
    align-items: center;
    margin-bottom: 15rpx;
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.required {
    color: #ff4757;
    margin-right: 8rpx;
    font-size: 28rpx;
}

.input-wrapper, .number-input-wrapper, .textarea-wrapper {
    background-color: #f5f6f7;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    min-height: 90rpx;
}

.icon {
    margin-right: 20rpx;
    color: #999;
}

.input {
    flex: 1;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
}

.select-input {
    flex: 1;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #333;
}

.select-input.placeholder {
    color: #999;
}

.scan-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f2ff;
    border-radius: 12rpx;
    margin-left: 20rpx;
}

.number-controls {
    display: flex;
    align-items: center;
    flex: 1;
}

.number-btn {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: #5677fc;
    color: #fff;
    border: none;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.number-btn:disabled {
    background: #ccc;
}

.number-input {
    flex: 1;
    text-align: center;
    font-size: 28rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
    margin: 0 20rpx;
}

.textarea {
    flex: 1;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
    min-height: 120rpx;
    resize: none;
}

.char-count {
    font-size: 24rpx;
    color: #999;
    text-align: right;
    margin-top: 10rpx;
}

.error-msg {
    color: #ff4757;
    font-size: 24rpx;
    margin-top: 10rpx;
}

.button-section {
    background: #fff;
    margin: 20rpx;
    border-radius: 12rpx;
    padding: 30rpx;
    display: flex;
    gap: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn {
    flex: 1;
    height: 90rpx;
    border-radius: 45rpx;
    font-size: 32rpx;
    border: none;
    outline: none;
}

.btn-cancel {
    background: #f5f6f7;
    color: #666;
}

.btn-submit {
    background: #5677fc;
    color: #fff;
}

.btn:disabled {
    opacity: 0.6;
}
</style>
