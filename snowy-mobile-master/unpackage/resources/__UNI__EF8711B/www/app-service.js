if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16<PERSON>rray,Int32Array=e.Int32Array,Uint32Array=e.Uint32<PERSON>rray,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>nt64<PERSON>rray,BigUint64Array=e.<PERSON>int64<PERSON>rray}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e,t){var n,o,r,i,a,s,l,c,u,d,h,p,f,m,g,y,v,b,w,k,x,S,C,_,N,D,E,T,B,V,I,A,P,$,O,F,M,L,z,R,U,q,j,H,W,K,Y,G,J,Z;const X={};function Q(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}function ee(e,n){return t.isString(e)?n:e}const te=t=>(n,o=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,n,o)},ne=te("onShow"),oe=te("onLoad"),re=te("onReachBottom"),ie=te("onPullDownRefresh");function ae(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function se(e){switch(typeof e){case"undefined":return!0;case"string":if(0==e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(0===e||isNaN(e))return!0;break;case"object":if(null===e||0===e.length)return!0;for(const t in e)return!1;return!0}return!1}function le(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===Object.prototype.toString.call(e)}function ce(e){return"[object Object]"===Object.prototype.toString.call(e)}function ue(e){return"function"==typeof e}function de(e){const t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)}const he=Object.freeze(Object.defineProperty({__proto__:null,amount:function(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)},array:le,carNo:function(e){const t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,n=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return 7===e.length?n.test(e):8===e.length&&t.test(e)},chinese:function(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)},code:function(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)},contains:function(e,t){return e.indexOf(t)>=0},date:function(e){return!!e&&(ae(e)&&(e=+e),!/Invalid|NaN/.test(new Date(e).toString()))},dateISO:function(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)},digits:function(e){return/^\d+$/.test(e)},email:function(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)},empty:se,enOrNum:function(e){return/^[0-9a-zA-Z]*$/g.test(e)},func:ue,idCard:function(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)},image:de,jsonString:function(e){if("string"==typeof e)try{const t=JSON.parse(e);return!("object"!=typeof t||!t)}catch(Er){return!1}return!1},landline:function(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)},letter:function(e){return/^[a-zA-Z]*$/.test(e)},mobile:function(e){return/^1([3589]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/.test(e)},number:ae,object:ce,promise:function(e){return ce(e)&&ue(e.then)&&ue(e.catch)},range:function(e,t){return e>=t[0]&&e<=t[1]},rangeLength:function(e,t){return e.length>=t[0]&&e.length<=t[1]},regExp:function(e){return e&&"[object RegExp]"===Object.prototype.toString.call(e)},string:function(e){return"string"==typeof e},url:function(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)},video:function(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}},Symbol.toStringTag,{value:"Module"}));function pe(e,t=15){return+parseFloat(Number(e).toPrecision(t))}function fe(e){const t=e.toString().split(/[eE]/),n=(t[0].split(".")[1]||"").length-+(t[1]||0);return n>0?n:0}function me(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));const t=fe(e);return t>0?pe(Number(e)*Math.pow(10,t)):Number(e)}function ge(e){(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&Q("warn","at uni_modules/uv-ui-tools/libs/function/digit.js:45",`${e} 超出了精度限制，结果可能不正确`)}function ye(e,t){const[n,o,...r]=e;let i=t(n,o);return r.forEach((e=>{i=t(i,e)})),i}function ve(...e){if(e.length>2)return ye(e,ve);const[t,n]=e,o=me(t),r=me(n),i=fe(t)+fe(n),a=o*r;return ge(a),a/Math.pow(10,i)}function be(...e){if(e.length>2)return ye(e,be);const[t,n]=e,o=me(t),r=me(n);return ge(o),ge(r),ve(o/r,pe(Math.pow(10,fe(n)-fe(t))))}function we(e){let t=this.$parent;for(;t;){if(!t.$options||t.$options.name===e)return t;t=t.$parent}return!1}function ke(e,t=new WeakMap){if(null===e||"object"!=typeof e)return e;if(t.has(e))return t.get(e);let n;if(e instanceof Date)n=new Date(e.getTime());else if(e instanceof RegExp)n=new RegExp(e);else if(e instanceof Map)n=new Map(Array.from(e,(([e,n])=>[e,ke(n,t)])));else if(e instanceof Set)n=new Set(Array.from(e,(e=>ke(e,t))));else if(Array.isArray(e))n=e.map((e=>ke(e,t)));else if("[object Object]"===Object.prototype.toString.call(e)){n=Object.create(Object.getPrototypeOf(e)),t.set(e,n);for(const[o,r]of Object.entries(e))n[o]=ke(r,t)}else n=Object.assign({},e);return t.set(e,n),n}function xe(e={},t={}){if("object"!=typeof(e=ke(e))||null===e||"object"!=typeof t||null===t)return e;const n=Array.isArray(e)?e.slice():Object.assign({},e);for(const o in t){if(!t.hasOwnProperty(o))continue;const e=t[o],r=n[o];e instanceof Date?n[o]=new Date(e):e instanceof RegExp?n[o]=new RegExp(e):e instanceof Map?n[o]=new Map(e):e instanceof Set?n[o]=new Set(e):n[o]="object"==typeof e&&null!==e?xe(r,e):e}return n}function Se(e=null,t="yyyy-mm-dd"){let n;n=e?/^\d{10}$/.test(null==e?void 0:e.toString().trim())?new Date(1e3*e):"string"==typeof e&&/^\d+$/.test(e.trim())?new Date(Number(e)):"string"==typeof e&&e.includes("-")&&!e.includes("T")?new Date(e.replace(/-/g,"/")):new Date(e):new Date;const o={y:n.getFullYear().toString(),m:(n.getMonth()+1).toString().padStart(2,"0"),d:n.getDate().toString().padStart(2,"0"),h:n.getHours().toString().padStart(2,"0"),M:n.getMinutes().toString().padStart(2,"0"),s:n.getSeconds().toString().padStart(2,"0")};for(const r in o){const[e]=new RegExp(`${r}+`).exec(t)||[];if(e){const n="y"===r&&2===e.length?2:0;t=t.replace(e,o[r].slice(n))}}return t}function Ce(e,t="both"){return e=String(e),"both"==t?e.replace(/^\s+|\s+$/g,""):"left"==t?e.replace(/^\s*/,""):"right"==t?e.replace(/(\s*$)/g,""):"all"==t?e.replace(/\s+/g,""):e}function _e(e={},t=!0,n="brackets"){const o=t?"?":"",r=[];-1==["indices","brackets","repeat","comma"].indexOf(n)&&(n="brackets");for(const i in e){const t=e[i];if(!(["",void 0,null].indexOf(t)>=0))if(t.constructor===Array)switch(n){case"indices":for(let n=0;n<t.length;n++)r.push(`${i}[${n}]=${t[n]}`);break;case"brackets":default:t.forEach((e=>{r.push(`${i}[]=${e}`)}));break;case"repeat":t.forEach((e=>{r.push(`${i}=${e}`)}));break;case"comma":let e="";t.forEach((t=>{e+=(e?",":"")+t})),r.push(`${i}=${e}`)}else r.push(`${i}=${t}`)}return r.length?o+r.join("&"):""}function Ne(){var e;const t=getCurrentPages(),n=null==(e=t[t.length-1])?void 0:e.route;return`/${n||""}`}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if("[object String]"!==Object.prototype.toString.call(t))throw new TypeError("fillString must be String");const n=this;if(n.length>=e)return String(n);const o=e-n.length;let r=Math.ceil(o/t.length);for(;r>>=1;)t+=t,1===r&&(t+=t);return t.slice(0,o)+n});const De=Object.freeze(Object.defineProperty({__proto__:null,$parent:we,addStyle:function(e,t="object"){if(se(e)||"object"==typeof e&&"object"===t||"string"===t&&"string"==typeof e)return e;if("object"===t){const t=(e=Ce(e)).split(";"),n={};for(let e=0;e<t.length;e++)if(t[e]){const o=t[e].split(":");n[Ce(o[0])]=Ce(o[1])}return n}let n="";for(const o in e){n+=`${o.replace(/([A-Z])/g,"-$1").toLowerCase()}:${e[o]};`}return Ce(n)},addUnit:function(e="auto",t=((e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})()?(e=>{return null==(e=null==(t=null==uni?void 0:uni.$uv)?void 0:t.config)?void 0:e.unit;var t})():"px")){return ae(e=String(e))?`${e}${t}`:e},deepClone:ke,deepMerge:xe,error:function(e){},formValidate:function(e,t){const n=we.call(e,"uv-form-item"),o=we.call(e,"uv-form");n&&o&&o.validateField(n.prop,(()=>{}),t)},getDuration:function(e,t=!0){const n=parseInt(e);return t?/s$/.test(e)?e:e>30?`${e}ms`:`${e}s`:/ms$/.test(e)?n:/s$/.test(e)?n>30?n:1e3*n:n},getHistoryPage:function(e=0){const t=getCurrentPages();return t[t.length-1+e]},getProperty:function(e,t){if(e){if("string"!=typeof t||""===t)return"";if(-1!==t.indexOf(".")){const n=t.split(".");let o=e[n[0]]||{};for(let e=1;e<n.length;e++)o&&(o=o[n[e]]);return o}return e[t]}},getPx:function(e,t=!1){return ae(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${uni.upx2px(parseInt(e))}px`:Number(uni.upx2px(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)},guid:function(e=32,t=!0,n=null){const o="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),r=[];if(n=n||o.length,e)for(let i=0;i<e;i++)r[i]=o[0|Math.random()*n];else{let e;r[8]=r[13]=r[18]=r[23]="-",r[14]="4";for(let t=0;t<36;t++)r[t]||(e=0|16*Math.random(),r[t]=o[19==t?3&e|8:e])}return t?(r.shift(),`u${r.join("")}`):r.join("")},os:function(){return uni.getSystemInfoSync().platform.toLowerCase()},padZero:function(e){return`00${e}`.slice(-2)},page:Ne,pages:function(){return getCurrentPages()},priceFormat:function(e,t=0,n=".",o=","){e=`${e}`.replace(/[^0-9+-Ee.]/g,"");const r=isFinite(+e)?+e:0,i=isFinite(+t)?Math.abs(t):0,a=void 0===o?",":o,s=void 0===n?".":n;let l="";l=(i?function(e,t){const n=Math.pow(10,t);let o=be(Math.round(Math.abs(ve(e,n))),n);return e<0&&0!==o&&(o=ve(o,-1)),o}(r,i)+"":`${Math.round(r)}`).split(".");const c=/(-?\d+)(\d{3})/;for(;c.test(l[0]);)l[0]=l[0].replace(c,`$1${a}$2`);return(l[1]||"").length<i&&(l[1]=l[1]||"",l[1]+=new Array(i-l[1].length+1).join("0")),l.join(s)},queryParams:_e,random:function(e,t){if(e>=0&&t>0&&t>=e){const n=t-e+1;return Math.floor(Math.random()*n+e)}return 0},randomArray:function(e=[]){return e.sort((()=>Math.random()-.5))},range:function(e=0,t=0,n=0){return Math.max(e,Math.min(t,Number(n)))},setConfig:function({props:e={},config:t={},color:n={},zIndex:o={}}){const{deepMerge:r}=uni.$uv;uni.$uv.config=r(uni.$uv.config,t),uni.$uv.props=r(uni.$uv.props,e),uni.$uv.color=r(uni.$uv.color,n),uni.$uv.zIndex=r(uni.$uv.zIndex,o)},setProperty:function(e,t,n){if(!e)return;const o=function(e,t,n){if(1!==t.length)for(;t.length>1;){const r=t[0];e[r]&&"object"==typeof e[r]||(e[r]={}),t.shift(),o(e[r],t,n)}else e[t[0]]=n};if("string"!=typeof t||""===t);else if(-1!==t.indexOf(".")){const r=t.split(".");o(e,r,n)}else e[t]=n},sleep:function(e=30){return new Promise((t=>{setTimeout((()=>{t()}),e)}))},sys:function(){return uni.getSystemInfoSync()},timeFormat:Se,timeFrom:function(e=null,t="yyyy-mm-dd"){null==e&&(e=Number(new Date)),10==(e=parseInt(e)).toString().length&&(e*=1e3);let n=(new Date).getTime()-e;n=parseInt(n/1e3);let o="";switch(!0){case n<300:o="刚刚";break;case n>=300&&n<3600:o=`${parseInt(n/60)}分钟前`;break;case n>=3600&&n<86400:o=`${parseInt(n/3600)}小时前`;break;case n>=86400&&n<2592e3:o=`${parseInt(n/86400)}天前`;break;default:o=!1===t?n>=2592e3&&n<31536e3?`${parseInt(n/2592e3)}个月前`:`${parseInt(n/31536e3)}年前`:Se(e,t)}return o},toast:function(e,t=2e3){uni.showToast({title:String(e),icon:"none",duration:t})},trim:Ce,type2icon:function(e="success",t=!1){-1==["primary","info","error","warning","success"].indexOf(e)&&(e="success");let n="";switch(e){case"primary":case"info":n="info-circle";break;case"error":n="close-circle";break;case"warning":n="error-circle";break;default:n="checkmark-circle"}return t&&(n+="-fill"),n}},Symbol.toStringTag,{value:"Module"})),Ee={props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data:()=>({}),onLoad(){this.$uv.getRect=this.$uvGetRect},created(){this.$uv.getRect=this.$uvGetRect},computed:{$uv(){var e,t;return{...De,test:he,unit:null==(t=null==(e=null==uni?void 0:uni.$uv)?void 0:e.config)?void 0:t.unit}},bem:()=>function(e,t,n){const o=`uv-${e}--`,r={};return t&&t.map((e=>{r[o+this[e]]=!0})),n&&n.map((e=>{this[e]?r[o+e]=this[e]:delete r[o+e]})),Object.keys(r)}},methods:{openPage(e="url"){const t=this[e];t&&uni[this.linkType]({url:t})},$uvGetRect(e,t){return new Promise((n=>{uni.createSelectorQuery().in(this)[t?"selectAll":"select"](e).boundingClientRect((e=>{t&&Array.isArray(e)&&e.length&&n(e),!t&&e&&n(e)})).exec()}))},getParentData(e=""){this.parent||(this.parent={}),this.parent=this.$uv.$parent.call(this,e),this.parent.children&&-1===this.parent.children.indexOf(this)&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map((e=>{this.parentData[e]=this.parent[e]}))},preventEvent(e){e&&"function"==typeof e.stopPropagation&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){uni.$emit("uvOnReachBottom")},beforeDestroy(){if(this.parent&&le(this.parent.children)){const e=this.parent.children;e.map(((t,n)=>{t===this&&e.splice(n,1)}))}}},Te={"uvicon-level":"e68f","uvicon-checkbox-mark":"e659","uvicon-folder":"e694","uvicon-movie":"e67c","uvicon-star-fill":"e61e","uvicon-star":"e618","uvicon-phone-fill":"e6ac","uvicon-phone":"e6ba","uvicon-apple-fill":"e635","uvicon-backspace":"e64d","uvicon-attach":"e640","uvicon-empty-data":"e671","uvicon-empty-address":"e68a","uvicon-empty-favor":"e662","uvicon-empty-car":"e657","uvicon-empty-order":"e66b","uvicon-empty-list":"e672","uvicon-empty-search":"e677","uvicon-empty-permission":"e67d","uvicon-empty-news":"e67e","uvicon-empty-history":"e685","uvicon-empty-coupon":"e69b","uvicon-empty-page":"e60e","uvicon-empty-wifi-off":"e6cc","uvicon-reload":"e627","uvicon-order":"e695","uvicon-server-man":"e601","uvicon-search":"e632","uvicon-more-dot-fill":"e66f","uvicon-scan":"e631","uvicon-map":"e665","uvicon-map-fill":"e6a8","uvicon-tags":"e621","uvicon-tags-fill":"e613","uvicon-eye":"e664","uvicon-eye-fill":"e697","uvicon-eye-off":"e69c","uvicon-eye-off-outline":"e688","uvicon-mic":"e66d","uvicon-mic-off":"e691","uvicon-calendar":"e65c","uvicon-trash":"e623","uvicon-trash-fill":"e6ce","uvicon-play-left":"e6bf","uvicon-play-right":"e6b3","uvicon-minus":"e614","uvicon-plus":"e625","uvicon-info-circle":"e69f","uvicon-info-circle-fill":"e6a7","uvicon-question-circle":"e622","uvicon-question-circle-fill":"e6bc","uvicon-close":"e65a","uvicon-checkmark":"e64a","uvicon-checkmark-circle":"e643","uvicon-checkmark-circle-fill":"e668","uvicon-setting":"e602","uvicon-setting-fill":"e6d0","uvicon-heart":"e6a2","uvicon-heart-fill":"e68b","uvicon-camera":"e642","uvicon-camera-fill":"e650","uvicon-more-circle":"e69e","uvicon-more-circle-fill":"e684","uvicon-chat":"e656","uvicon-chat-fill":"e63f","uvicon-bag":"e647","uvicon-error-circle":"e66e","uvicon-error-circle-fill":"e655","uvicon-close-circle":"e64e","uvicon-close-circle-fill":"e666","uvicon-share":"e629","uvicon-share-fill":"e6bb","uvicon-share-square":"e6c4","uvicon-shopping-cart":"e6cb","uvicon-shopping-cart-fill":"e630","uvicon-bell":"e651","uvicon-bell-fill":"e604","uvicon-list":"e690","uvicon-list-dot":"e6a9","uvicon-zhifubao-circle-fill":"e617","uvicon-weixin-circle-fill":"e6cd","uvicon-weixin-fill":"e620","uvicon-qq-fill":"e608","uvicon-qq-circle-fill":"e6b9","uvicon-moments-circel-fill":"e6c2","uvicon-moments":"e6a0","uvicon-car":"e64f","uvicon-car-fill":"e648","uvicon-warning-fill":"e6c7","uvicon-warning":"e6c1","uvicon-clock-fill":"e64b","uvicon-clock":"e66c","uvicon-edit-pen":"e65d","uvicon-edit-pen-fill":"e679","uvicon-email":"e673","uvicon-email-fill":"e683","uvicon-minus-circle":"e6a5","uvicon-plus-circle":"e603","uvicon-plus-circle-fill":"e611","uvicon-file-text":"e687","uvicon-file-text-fill":"e67f","uvicon-pushpin":"e6d1","uvicon-pushpin-fill":"e6b6","uvicon-grid":"e68c","uvicon-grid-fill":"e698","uvicon-play-circle":"e6af","uvicon-play-circle-fill":"e62a","uvicon-pause-circle-fill":"e60c","uvicon-pause":"e61c","uvicon-pause-circle":"e696","uvicon-gift-fill":"e6b0","uvicon-gift":"e680","uvicon-kefu-ermai":"e660","uvicon-server-fill":"e610","uvicon-coupon-fill":"e64c","uvicon-coupon":"e65f","uvicon-integral":"e693","uvicon-integral-fill":"e6b1","uvicon-home-fill":"e68e","uvicon-home":"e67b","uvicon-account":"e63a","uvicon-account-fill":"e653","uvicon-thumb-down-fill":"e628","uvicon-thumb-down":"e60a","uvicon-thumb-up":"e612","uvicon-thumb-up-fill":"e62c","uvicon-lock-fill":"e6a6","uvicon-lock-open":"e68d","uvicon-lock-opened-fill":"e6a1","uvicon-lock":"e69d","uvicon-red-packet":"e6c3","uvicon-photo-fill":"e6b4","uvicon-photo":"e60d","uvicon-volume-off-fill":"e6c8","uvicon-volume-off":"e6bd","uvicon-volume-fill":"e624","uvicon-volume":"e605","uvicon-download":"e670","uvicon-arrow-up-fill":"e636","uvicon-arrow-down-fill":"e638","uvicon-play-left-fill":"e6ae","uvicon-play-right-fill":"e6ad","uvicon-arrow-downward":"e634","uvicon-arrow-leftward":"e63b","uvicon-arrow-rightward":"e644","uvicon-arrow-upward":"e641","uvicon-arrow-down":"e63e","uvicon-arrow-right":"e63c","uvicon-arrow-left":"e646","uvicon-arrow-up":"e633","uvicon-skip-back-left":"e6c5","uvicon-skip-forward-right":"e61f","uvicon-arrow-left-double":"e637","uvicon-man":"e675","uvicon-woman":"e626","uvicon-en":"e6b8","uvicon-twitte":"e607","uvicon-twitter-circle-fill":"e6cf"},Be={props:{name:{type:String,default:""},color:{type:String,default:"#606266"},size:{type:[String,Number],default:"16px"},bold:{type:Boolean,default:!1},index:{type:[String,Number],default:null},hoverClass:{type:String,default:""},customPrefix:{type:String,default:"uvicon"},label:{type:[String,Number],default:""},labelPos:{type:String,default:"right"},labelSize:{type:[String,Number],default:"15px"},labelColor:{type:String,default:"#606266"},space:{type:[String,Number],default:"3px"},imgMode:{type:String,default:""},width:{type:[String,Number],default:""},height:{type:[String,Number],default:""},top:{type:[String,Number],default:0},stop:{type:Boolean,default:!1},...null==(o=null==(n=uni.$uv)?void 0:n.props)?void 0:o.icon}},Ve=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},Ie={name:"uv-icon",emits:["click"],mixins:[X,Ee,Be],data:()=>({colorType:["primary","success","info","error","warning"]}),computed:{uClasses(){let e=[];return e.push(this.customPrefix),e.push(this.customPrefix+"-"+this.name),this.color&&this.colorType.includes(this.color)&&e.push("uv-icon__icon--"+this.color),e},iconStyle(){let e={};return e={fontSize:this.$uv.addUnit(this.size),lineHeight:this.$uv.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:this.$uv.addUnit(this.top)},this.color&&!this.colorType.includes(this.color)&&(e.color=this.color),e},isImg(){const e=this.name.indexOf("data:")>-1&&this.name.indexOf("base64")>-1;return-1!==this.name.indexOf("/")||e},imgStyle(){let e={};return e.width=this.width?this.$uv.addUnit(this.width):this.$uv.addUnit(this.size),e.height=this.height?this.$uv.addUnit(this.height):this.$uv.addUnit(this.size),e},icon(){const e=Te["uvicon-"+this.name];return e?unescape(`%u${e}`):["uvicon"].indexOf(this.customPrefix)>-1?this.name:""}},methods:{clickHandler(e){this.$emit("click",this.index),this.stop&&this.preventEvent(e)}}};const Ae=Ve(Ie,[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-icon",["uv-icon--"+t.labelPos]]),onClick:n[0]||(n[0]=(...e)=>a.clickHandler&&a.clickHandler(...e))},[a.isImg?(e.openBlock(),e.createElementBlock("image",{key:0,class:"uv-icon__img",src:t.name,mode:t.imgMode,style:e.normalizeStyle([a.imgStyle,t.$uv.addStyle(t.customStyle)])},null,12,["src","mode"])):(e.openBlock(),e.createElementBlock("text",{key:1,class:e.normalizeClass(["uv-icon__icon",a.uClasses]),style:e.normalizeStyle([a.iconStyle,t.$uv.addStyle(t.customStyle)]),"hover-class":t.hoverClass},e.toDisplayString(a.icon),15,["hover-class"])),""!==t.label?(e.openBlock(),e.createElementBlock("text",{key:2,class:"uv-icon__label",style:e.normalizeStyle({color:t.labelColor,fontSize:t.$uv.addUnit(t.labelSize),marginLeft:"right"==t.labelPos?t.$uv.addUnit(t.space):0,marginTop:"bottom"==t.labelPos?t.$uv.addUnit(t.space):0,marginRight:"left"==t.labelPos?t.$uv.addUnit(t.space):0,marginBottom:"top"==t.labelPos?t.$uv.addUnit(t.space):0})},e.toDisplayString(t.label),5)):e.createCommentVNode("",!0)],2)}],["__scopeId","data-v-c9bc859c"]]);function Pe(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}const $e="function"==typeof Proxy;class Oe{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const i in e.settings){const t=e.settings[i];n[i]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let r={...n};try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(r,t)}catch(Er){}this.fallbacks={getSettings:()=>r,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(Er){}r=e}},t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function Fe(e,t){const n=Pe(),o=Pe().__VUE_DEVTOOLS_GLOBAL_HOOK__,r=$e&&e.enableEarlyProxy;if(!o||!n.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&r){const i=r?new Oe(e,o):null;(n.__VUE_DEVTOOLS_PLUGINS__=n.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:e,setupFn:t,proxy:i}),i&&t(i.proxiedTarget)}else o.emit("devtools-plugin:setup",e,t)}
/*!
   * vuex v4.1.0
   * (c) 2022 Evan You
   * @license MIT
   */function Me(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function Le(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function ze(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;Ue(e,n,[],e._modules.root,!0),Re(e,n,t)}function Re(t,n,o){var r=t._state,i=t._scope;t.getters={},t._makeLocalGettersCache=Object.create(null);var a=t._wrappedGetters,s={},l={},c=e.effectScope(!0);c.run((function(){Me(a,(function(n,o){s[o]=function(e,t){return function(){return e(t)}}(n,t),l[o]=e.computed((function(){return s[o]()})),Object.defineProperty(t.getters,o,{get:function(){return l[o].value},enumerable:!0})}))})),t._state=e.reactive({data:n}),t._scope=c,t.strict&&function(t){e.watch((function(){return t._state.data}),(function(){}),{deep:!0,flush:"sync"})}(t),r&&o&&t._withCommit((function(){r.data=null})),i&&i.stop()}function Ue(e,t,n,o,r){var i=!n.length,a=e._modules.getNamespace(n);if(o.namespaced&&(e._modulesNamespaceMap[a],e._modulesNamespaceMap[a]=o),!i&&!r){var s=je(t,n.slice(0,-1)),l=n[n.length-1];e._withCommit((function(){s[l]=o.state}))}var c=o.context=function(e,t,n){var o=""===t,r={dispatch:o?e.dispatch:function(n,o,r){var i=He(n,o,r),a=i.payload,s=i.options,l=i.type;return s&&s.root||(l=t+l),e.dispatch(l,a)},commit:o?e.commit:function(n,o,r){var i=He(n,o,r),a=i.payload,s=i.options,l=i.type;s&&s.root||(l=t+l),e.commit(l,a,s)}};return Object.defineProperties(r,{getters:{get:o?function(){return e.getters}:function(){return qe(e,t)}},state:{get:function(){return je(e.state,n)}}}),r}(e,a,n);o.forEachMutation((function(t,n){!function(e,t,n,o){(e._mutations[t]||(e._mutations[t]=[])).push((function(t){n.call(e,o.state,t)}))}(e,a+n,t,c)})),o.forEachAction((function(t,n){var o=t.root?n:a+n,r=t.handler||t;!function(e,t,n,o){(e._actions[t]||(e._actions[t]=[])).push((function(t){var r,i=n.call(e,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:e.getters,rootState:e.state},t);return(r=i)&&"function"==typeof r.then||(i=Promise.resolve(i)),e._devtoolHook?i.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):i}))}(e,o,r,c)})),o.forEachGetter((function(t,n){!function(e,t,n,o){if(e._wrappedGetters[t])return;e._wrappedGetters[t]=function(e){return n(o.state,o.getters,e.state,e.getters)}}(e,a+n,t,c)})),o.forEachChild((function(o,i){Ue(e,t,n.concat(i),o,r)}))}function qe(e,t){if(!e._makeLocalGettersCache[t]){var n={},o=t.length;Object.keys(e.getters).forEach((function(r){if(r.slice(0,o)===t){var i=r.slice(o);Object.defineProperty(n,i,{get:function(){return e.getters[r]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function je(e,t){return t.reduce((function(e,t){return e[t]}),e)}function He(e,t,n){var o;return null!==(o=e)&&"object"==typeof o&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var We="vuex:mutations",Ke="vuex:actions",Ye="vuex",Ge=0;function Je(e,t){Fe({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:["vuex bindings"]},(function(n){n.addTimelineLayer({id:We,label:"Vuex Mutations",color:Ze}),n.addTimelineLayer({id:Ke,label:"Vuex Actions",color:Ze}),n.addInspector({id:Ye,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree((function(n){if(n.app===e&&n.inspectorId===Ye)if(n.filter){var o=[];tt(o,t._modules.root,n.filter,""),n.rootNodes=o}else n.rootNodes=[et(t._modules.root,"")]})),n.on.getInspectorState((function(n){if(n.app===e&&n.inspectorId===Ye){var o=n.nodeId;qe(t,o),n.state=function(e,t,n){t="root"===n?t:t[n];var o=Object.keys(t),r={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(o.length){var i=function(e){var t={};return Object.keys(e).forEach((function(n){var o=n.split("/");if(o.length>1){var r=t,i=o.pop();o.forEach((function(e){r[e]||(r[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),r=r[e]._custom.value})),r[i]=nt((function(){return e[n]}))}else t[n]=nt((function(){return e[n]}))})),t}(t);r.getters=Object.keys(i).map((function(e){return{key:e.endsWith("/")?Qe(e):e,editable:!1,value:nt((function(){return i[e]}))}}))}return r}((r=t._modules,(a=(i=o).split("/").filter((function(e){return e}))).reduce((function(e,t,n){var o=e[t];if(!o)throw new Error('Missing module "'+t+'" for path "'+i+'".');return n===a.length-1?o:o._children}),"root"===i?r:r.root._children)),"root"===o?t.getters:t._makeLocalGettersCache,o)}var r,i,a})),n.on.editInspectorState((function(n){if(n.app===e&&n.inspectorId===Ye){var o=n.nodeId,r=n.path;"root"!==o&&(r=o.split("/").filter(Boolean).concat(r)),t._withCommit((function(){n.set(t._state.data,r,n.state.value)}))}})),t.subscribe((function(e,t){var o={};e.payload&&(o.payload=e.payload),o.state=t,n.notifyComponentUpdate(),n.sendInspectorTree(Ye),n.sendInspectorState(Ye),n.addTimelineEvent({layerId:We,event:{time:Date.now(),title:e.type,data:o}})})),t.subscribeAction({before:function(e,t){var o={};e.payload&&(o.payload=e.payload),e._id=Ge++,e._time=Date.now(),o.state=t,n.addTimelineEvent({layerId:Ke,event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:o}})},after:function(e,t){var o={},r=Date.now()-e._time;o.duration={_custom:{type:"duration",display:r+"ms",tooltip:"Action duration",value:r}},e.payload&&(o.payload=e.payload),o.state=t,n.addTimelineEvent({layerId:Ke,event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:o}})}})}))}var Ze=8702998,Xe={label:"namespaced",textColor:16777215,backgroundColor:6710886};function Qe(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function et(e,t){return{id:t||"root",label:Qe(t),tags:e.namespaced?[Xe]:[],children:Object.keys(e._children).map((function(n){return et(e._children[n],t+n+"/")}))}}function tt(e,t,n,o){o.includes(n)&&e.push({id:o||"root",label:o.endsWith("/")?o.slice(0,o.length-1):o||"Root",tags:t.namespaced?[Xe]:[]}),Object.keys(t._children).forEach((function(r){tt(e,t._children[r],n,o+r+"/")}))}function nt(e){try{return e()}catch(Er){return Er}}var ot=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"==typeof n?n():n)||{}},rt={namespaced:{configurable:!0}};rt.namespaced.get=function(){return!!this._rawModule.namespaced},ot.prototype.addChild=function(e,t){this._children[e]=t},ot.prototype.removeChild=function(e){delete this._children[e]},ot.prototype.getChild=function(e){return this._children[e]},ot.prototype.hasChild=function(e){return e in this._children},ot.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},ot.prototype.forEachChild=function(e){Me(this._children,e)},ot.prototype.forEachGetter=function(e){this._rawModule.getters&&Me(this._rawModule.getters,e)},ot.prototype.forEachAction=function(e){this._rawModule.actions&&Me(this._rawModule.actions,e)},ot.prototype.forEachMutation=function(e){this._rawModule.mutations&&Me(this._rawModule.mutations,e)},Object.defineProperties(ot.prototype,rt);var it=function(e){this.register([],e,!1)};function at(e,t,n){if(t.update(n),n.modules)for(var o in n.modules){if(!t.getChild(o))return;at(e.concat(o),t.getChild(o),n.modules[o])}}it.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},it.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return e+((t=t.getChild(n)).namespaced?n+"/":"")}),"")},it.prototype.update=function(e){at([],this.root,e)},it.prototype.register=function(e,t,n){var o=this;void 0===n&&(n=!0);var r=new ot(t,n);0===e.length?this.root=r:this.get(e.slice(0,-1)).addChild(e[e.length-1],r);t.modules&&Me(t.modules,(function(t,r){o.register(e.concat(r),t,n)}))},it.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],o=t.getChild(n);o&&o.runtime&&t.removeChild(n)},it.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};var st=function(e){var t=this;void 0===e&&(e={});var n=e.plugins;void 0===n&&(n=[]);var o=e.strict;void 0===o&&(o=!1);var r=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new it(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=r;var i=this,a=this.dispatch,s=this.commit;this.dispatch=function(e,t){return a.call(i,e,t)},this.commit=function(e,t,n){return s.call(i,e,t,n)},this.strict=o;var l=this._modules.root.state;Ue(this,l,[],this._modules.root),Re(this,l),n.forEach((function(e){return e(t)}))},lt={state:{configurable:!0}};st.prototype.install=function(e,t){e.provide(t||"store",this),e.config.globalProperties.$store=this,void 0!==this._devtools&&this._devtools&&Je(e,this)},lt.state.get=function(){return this._state.data},lt.state.set=function(e){},st.prototype.commit=function(e,t,n){var o=this,r=He(e,t,n),i=r.type,a=r.payload,s={type:i,payload:a},l=this._mutations[i];l&&(this._withCommit((function(){l.forEach((function(e){e(a)}))})),this._subscribers.slice().forEach((function(e){return e(s,o.state)})))},st.prototype.dispatch=function(e,t){var n=this,o=He(e,t),r=o.type,i=o.payload,a={type:r,payload:i},s=this._actions[r];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(a,n.state)}))}catch(Er){}var l=s.length>1?Promise.all(s.map((function(e){return e(i)}))):s[0](i);return new Promise((function(e,t){l.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(a,n.state)}))}catch(Er){}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(a,n.state,e)}))}catch(Er){}t(e)}))}))}},st.prototype.subscribe=function(e,t){return Le(e,this._subscribers,t)},st.prototype.subscribeAction=function(e,t){return Le("function"==typeof e?{before:e}:e,this._actionSubscribers,t)},st.prototype.watch=function(t,n,o){var r=this;return e.watch((function(){return t(r.state,r.getters)}),n,Object.assign({},o))},st.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},st.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"==typeof e&&(e=[e]),this._modules.register(e,t),Ue(this,this.state,e,this._modules.get(e),n.preserveState),Re(this,this.state)},st.prototype.unregisterModule=function(e){var t=this;"string"==typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){delete je(t.state,e.slice(0,-1))[e[e.length-1]]})),ze(this)},st.prototype.hasModule=function(e){return"string"==typeof e&&(e=[e]),this._modules.isRegistered(e)},st.prototype.hotUpdate=function(e){this._modules.update(e),ze(this,!0)},st.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(st.prototype,lt);const ct="/pages/login",ut="/pages/home/<USER>",dt={SERVER_TYPE:"SNOWY",TIMEOUT:1e4,TOKEN_NAME:"token",TOKEN_PREFIX:"",SYS_BASE_CONFIG:{SNOWY_SYS_LOGO:"/static/logo.png",SNOWY_SYS_BACK_IMAGE:"",SNOWY_SYS_NAME:"Snowy",SNOWY_SYS_VERSION:"2.0",SNOWY_SYS_COPYRIGHT:"Snowy ©2022 Created by xiaonuo.vip",SNOWY_SYS_COPYRIGHT_URL:"https://www.xiaonuo.vip",SNOWY_SYS_DEFAULT_FILE_ENGINE:"LOCAL",SNOWY_SYS_DEFAULT_CAPTCHA_OPEN:"false",SNOWY_SYS_DEFAULT_PASSWORD:"123456"},HOME_CONFIGS:[{name:"轮播",code:"swiper",isShow:!0},{name:"图表",code:"chart",isShow:!0},{name:"日程",code:"schedule",isShow:!0}],NO_TOKEN_BACK_URL:ct,NO_TOKEN_WHITE_LIST:[ct,"/","/pages/config/index","/pages/config/form","/pages/common/webview/index"],HAS_TOKEN_BACK_URL:ut,HAS_TOKEN_WHITE_LIST:[ut,"/pages/msg/index","/pages/msg/detail","/pages/work/index","/pages/mine/index","/pages/mine/info/edit","/pages/mine/home-config/index","/pages/mine/pwd/index","/pages/mine/info/index"]},ht="envKey",pt="allEnv",ft="sysBaseConfig",mt="homeConfigs",gt="userInfo",yt="userMobileMenus",vt="dictTypeTreeData";let bt="storage_data",wt=[ht,pt,ft,mt,gt,yt,vt],kt=uni.getStorageSync(bt)||{};const xt=function(e,t){if(-1!=wt.indexOf(e)){let n=uni.getStorageSync(bt);n=n||{},n[e]=t,uni.setStorageSync(bt,n)}},St=function(e){return kt[e]||""},Ct=function(e){delete kt[e],uni.setStorageSync(bt,kt)};var _t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Nt=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,62,0,62,0,63,52,53,54,55,56,57,58,59,60,61,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,0,0,0,0,63,0,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51];const Dt={getRandomValues(e){if(!(e instanceof Int8Array||e instanceof Uint8Array||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8ClampedArray))throw new Error("Expected an integer array");if(e.byteLength>65536)throw new Error("Can only request a maximum of 65536 bytes");var t;return function(e,t){for(var n,o=e.length,r="="===e[o-2]?2:"="===e[o-1]?1:0,i=0,a=o-r&4294967292,s=0;s<a;s+=4)n=Nt[e.charCodeAt(s)]<<18|Nt[e.charCodeAt(s+1)]<<12|Nt[e.charCodeAt(s+2)]<<6|Nt[e.charCodeAt(s+3)],t[i++]=n>>16&255,t[i++]=n>>8&255,t[i++]=255&n;1===r&&(n=Nt[e.charCodeAt(s)]<<10|Nt[e.charCodeAt(s+1)]<<4|Nt[e.charCodeAt(s+2)]>>2,t[i++]=n>>8&255,t[i++]=255&n),2===r&&(n=Nt[e.charCodeAt(s)]<<2|Nt[e.charCodeAt(s+1)]>>4,t[i++]=255&n)}((t="DCloud-Crypto",weex.requireModule(t)).getRandomValues(e.byteLength),new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e}};var Et,Tt={};Et={get exports(){return Tt},set exports(e){Tt=e}},function(){var e;function t(e,t,n){null!=e&&("number"==typeof e?this.fromNumber(e,t,n):null==t&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}function n(){return new t(null)}var o="undefined"!=typeof navigator;o&&"Microsoft Internet Explorer"==navigator.appName?(t.prototype.am=function(e,t,n,o,r,i){for(var a=32767&t,s=t>>15;--i>=0;){var l=32767&this[e],c=this[e++]>>15,u=s*l+c*a;r=((l=a*l+((32767&u)<<15)+n[o]+(1073741823&r))>>>30)+(u>>>15)+s*c+(r>>>30),n[o++]=1073741823&l}return r},e=30):o&&"Netscape"!=navigator.appName?(t.prototype.am=function(e,t,n,o,r,i){for(;--i>=0;){var a=t*this[e++]+n[o]+r;r=Math.floor(a/67108864),n[o++]=67108863&a}return r},e=26):(t.prototype.am=function(e,t,n,o,r,i){for(var a=16383&t,s=t>>14;--i>=0;){var l=16383&this[e],c=this[e++]>>14,u=s*l+c*a;r=((l=a*l+((16383&u)<<14)+n[o]+r)>>28)+(u>>14)+s*c,n[o++]=268435455&l}return r},e=28),t.prototype.DB=e,t.prototype.DM=(1<<e)-1,t.prototype.DV=1<<e,t.prototype.FV=Math.pow(2,52),t.prototype.F1=52-e,t.prototype.F2=2*e-52;var r,i,a=new Array;for(r="0".charCodeAt(0),i=0;i<=9;++i)a[r++]=i;for(r="a".charCodeAt(0),i=10;i<36;++i)a[r++]=i;for(r="A".charCodeAt(0),i=10;i<36;++i)a[r++]=i;function s(e){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(e)}function l(e,t){var n=a[e.charCodeAt(t)];return null==n?-1:n}function c(e){var t=n();return t.fromInt(e),t}function u(e){var t,n=1;return 0!=(t=e>>>16)&&(e=t,n+=16),0!=(t=e>>8)&&(e=t,n+=8),0!=(t=e>>4)&&(e=t,n+=4),0!=(t=e>>2)&&(e=t,n+=2),0!=(t=e>>1)&&(e=t,n+=1),n}function d(e){this.m=e}function h(e){this.m=e,this.mp=e.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<e.DB-15)-1,this.mt2=2*e.t}function p(e,t){return e&t}function f(e,t){return e|t}function m(e,t){return e^t}function g(e,t){return e&~t}function y(e){if(0==e)return-1;var t=0;return 0==(65535&e)&&(e>>=16,t+=16),0==(255&e)&&(e>>=8,t+=8),0==(15&e)&&(e>>=4,t+=4),0==(3&e)&&(e>>=2,t+=2),0==(1&e)&&++t,t}function v(e){for(var t=0;0!=e;)e&=e-1,++t;return t}function b(){}function w(e){return e}function k(e){this.r2=n(),this.q3=n(),t.ONE.dlShiftTo(2*e.t,this.r2),this.mu=this.r2.divide(e),this.m=e}d.prototype.convert=function(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e},d.prototype.revert=function(e){return e},d.prototype.reduce=function(e){e.divRemTo(this.m,null,e)},d.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},d.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},h.prototype.convert=function(e){var o=n();return e.abs().dlShiftTo(this.m.t,o),o.divRemTo(this.m,null,o),e.s<0&&o.compareTo(t.ZERO)>0&&this.m.subTo(o,o),o},h.prototype.revert=function(e){var t=n();return e.copyTo(t),this.reduce(t),t},h.prototype.reduce=function(e){for(;e.t<=this.mt2;)e[e.t++]=0;for(var t=0;t<this.m.t;++t){var n=32767&e[t],o=n*this.mpl+((n*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM;for(e[n=t+this.m.t]+=this.m.am(0,o,e,t,0,this.m.t);e[n]>=e.DV;)e[n]-=e.DV,e[++n]++}e.clamp(),e.drShiftTo(this.m.t,e),e.compareTo(this.m)>=0&&e.subTo(this.m,e)},h.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},h.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)},t.prototype.copyTo=function(e){for(var t=this.t-1;t>=0;--t)e[t]=this[t];e.t=this.t,e.s=this.s},t.prototype.fromInt=function(e){this.t=1,this.s=e<0?-1:0,e>0?this[0]=e:e<-1?this[0]=e+this.DV:this.t=0},t.prototype.fromString=function(e,n){var o;if(16==n)o=4;else if(8==n)o=3;else if(256==n)o=8;else if(2==n)o=1;else if(32==n)o=5;else{if(4!=n)return void this.fromRadix(e,n);o=2}this.t=0,this.s=0;for(var r=e.length,i=!1,a=0;--r>=0;){var s=8==o?255&e[r]:l(e,r);s<0?"-"==e.charAt(r)&&(i=!0):(i=!1,0==a?this[this.t++]=s:a+o>this.DB?(this[this.t-1]|=(s&(1<<this.DB-a)-1)<<a,this[this.t++]=s>>this.DB-a):this[this.t-1]|=s<<a,(a+=o)>=this.DB&&(a-=this.DB))}8==o&&0!=(128&e[0])&&(this.s=-1,a>0&&(this[this.t-1]|=(1<<this.DB-a)-1<<a)),this.clamp(),i&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;)--this.t},t.prototype.dlShiftTo=function(e,t){var n;for(n=this.t-1;n>=0;--n)t[n+e]=this[n];for(n=e-1;n>=0;--n)t[n]=0;t.t=this.t+e,t.s=this.s},t.prototype.drShiftTo=function(e,t){for(var n=e;n<this.t;++n)t[n-e]=this[n];t.t=Math.max(this.t-e,0),t.s=this.s},t.prototype.lShiftTo=function(e,t){var n,o=e%this.DB,r=this.DB-o,i=(1<<r)-1,a=Math.floor(e/this.DB),s=this.s<<o&this.DM;for(n=this.t-1;n>=0;--n)t[n+a+1]=this[n]>>r|s,s=(this[n]&i)<<o;for(n=a-1;n>=0;--n)t[n]=0;t[a]=s,t.t=this.t+a+1,t.s=this.s,t.clamp()},t.prototype.rShiftTo=function(e,t){t.s=this.s;var n=Math.floor(e/this.DB);if(n>=this.t)t.t=0;else{var o=e%this.DB,r=this.DB-o,i=(1<<o)-1;t[0]=this[n]>>o;for(var a=n+1;a<this.t;++a)t[a-n-1]|=(this[a]&i)<<r,t[a-n]=this[a]>>o;o>0&&(t[this.t-n-1]|=(this.s&i)<<r),t.t=this.t-n,t.clamp()}},t.prototype.subTo=function(e,t){for(var n=0,o=0,r=Math.min(e.t,this.t);n<r;)o+=this[n]-e[n],t[n++]=o&this.DM,o>>=this.DB;if(e.t<this.t){for(o-=e.s;n<this.t;)o+=this[n],t[n++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;n<e.t;)o-=e[n],t[n++]=o&this.DM,o>>=this.DB;o-=e.s}t.s=o<0?-1:0,o<-1?t[n++]=this.DV+o:o>0&&(t[n++]=o),t.t=n,t.clamp()},t.prototype.multiplyTo=function(e,n){var o=this.abs(),r=e.abs(),i=o.t;for(n.t=i+r.t;--i>=0;)n[i]=0;for(i=0;i<r.t;++i)n[i+o.t]=o.am(0,r[i],n,i,0,o.t);n.s=0,n.clamp(),this.s!=e.s&&t.ZERO.subTo(n,n)},t.prototype.squareTo=function(e){for(var t=this.abs(),n=e.t=2*t.t;--n>=0;)e[n]=0;for(n=0;n<t.t-1;++n){var o=t.am(n,t[n],e,2*n,0,1);(e[n+t.t]+=t.am(n+1,2*t[n],e,2*n+1,o,t.t-n-1))>=t.DV&&(e[n+t.t]-=t.DV,e[n+t.t+1]=1)}e.t>0&&(e[e.t-1]+=t.am(n,t[n],e,2*n,0,1)),e.s=0,e.clamp()},t.prototype.divRemTo=function(e,o,r){var i=e.abs();if(!(i.t<=0)){var a=this.abs();if(a.t<i.t)return null!=o&&o.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=n());var s=n(),l=this.s,c=e.s,d=this.DB-u(i[i.t-1]);d>0?(i.lShiftTo(d,s),a.lShiftTo(d,r)):(i.copyTo(s),a.copyTo(r));var h=s.t,p=s[h-1];if(0!=p){var f=p*(1<<this.F1)+(h>1?s[h-2]>>this.F2:0),m=this.FV/f,g=(1<<this.F1)/f,y=1<<this.F2,v=r.t,b=v-h,w=null==o?n():o;for(s.dlShiftTo(b,w),r.compareTo(w)>=0&&(r[r.t++]=1,r.subTo(w,r)),t.ONE.dlShiftTo(h,w),w.subTo(s,s);s.t<h;)s[s.t++]=0;for(;--b>=0;){var k=r[--v]==p?this.DM:Math.floor(r[v]*m+(r[v-1]+y)*g);if((r[v]+=s.am(0,k,r,b,0,h))<k)for(s.dlShiftTo(b,w),r.subTo(w,r);r[v]<--k;)r.subTo(w,r)}null!=o&&(r.drShiftTo(h,o),l!=c&&t.ZERO.subTo(o,o)),r.t=h,r.clamp(),d>0&&r.rShiftTo(d,r),l<0&&t.ZERO.subTo(r,r)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var e=this[0];if(0==(1&e))return 0;var t=3&e;return(t=(t=(t=(t=t*(2-(15&e)*t)&15)*(2-(255&e)*t)&255)*(2-((65535&e)*t&65535))&65535)*(2-e*t%this.DV)%this.DV)>0?this.DV-t:-t},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,o){if(e>4294967295||e<1)return t.ONE;var r=n(),i=n(),a=o.convert(this),s=u(e)-1;for(a.copyTo(r);--s>=0;)if(o.sqrTo(r,i),(e&1<<s)>0)o.mulTo(i,a,r);else{var l=r;r=i,i=l}return o.revert(r)},t.prototype.toString=function(e){if(this.s<0)return"-"+this.negate().toString(e);var t;if(16==e)t=4;else if(8==e)t=3;else if(2==e)t=1;else if(32==e)t=5;else{if(4!=e)return this.toRadix(e);t=2}var n,o=(1<<t)-1,r=!1,i="",a=this.t,l=this.DB-a*this.DB%t;if(a-- >0)for(l<this.DB&&(n=this[a]>>l)>0&&(r=!0,i=s(n));a>=0;)l<t?(n=(this[a]&(1<<l)-1)<<t-l,n|=this[--a]>>(l+=this.DB-t)):(n=this[a]>>(l-=t)&o,l<=0&&(l+=this.DB,--a)),n>0&&(r=!0),r&&(i+=s(n));return r?i:"0"},t.prototype.negate=function(){var e=n();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(e){var t=this.s-e.s;if(0!=t)return t;var n=this.t;if(0!=(t=n-e.t))return this.s<0?-t:t;for(;--n>=0;)if(0!=(t=this[n]-e[n]))return t;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+u(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var o=n();return this.abs().divRemTo(e,null,o),this.s<0&&o.compareTo(t.ZERO)>0&&e.subTo(o,o),o},t.prototype.modPowInt=function(e,t){var n;return n=e<256||t.isEven()?new d(t):new h(t),this.exp(e,n)},t.ZERO=c(0),t.ONE=c(1),b.prototype.convert=w,b.prototype.revert=w,b.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n)},b.prototype.sqrTo=function(e,t){e.squareTo(t)},k.prototype.convert=function(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);if(e.compareTo(this.m)<0)return e;var t=n();return e.copyTo(t),this.reduce(t),t},k.prototype.revert=function(e){return e},k.prototype.reduce=function(e){for(e.drShiftTo(this.m.t-1,this.r2),e.t>this.m.t+1&&(e.t=this.m.t+1,e.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;)e.dAddOffset(1,this.m.t+1);for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;)e.subTo(this.m,e)},k.prototype.mulTo=function(e,t,n){e.multiplyTo(t,n),this.reduce(n)},k.prototype.sqrTo=function(e,t){e.squareTo(t),this.reduce(t)};var x,S,C,_=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],N=(1<<26)/_[_.length-1];function D(){var e;e=(new Date).getTime(),S[C++]^=255&e,S[C++]^=e>>8&255,S[C++]^=e>>16&255,S[C++]^=e>>24&255,C>=P&&(C-=P)}if(t.prototype.chunkSize=function(e){return Math.floor(Math.LN2*this.DB/Math.log(e))},t.prototype.toRadix=function(e){if(null==e&&(e=10),0==this.signum()||e<2||e>36)return"0";var t=this.chunkSize(e),o=Math.pow(e,t),r=c(o),i=n(),a=n(),s="";for(this.divRemTo(r,i,a);i.signum()>0;)s=(o+a.intValue()).toString(e).substr(1)+s,i.divRemTo(r,i,a);return a.intValue().toString(e)+s},t.prototype.fromRadix=function(e,n){this.fromInt(0),null==n&&(n=10);for(var o=this.chunkSize(n),r=Math.pow(n,o),i=!1,a=0,s=0,c=0;c<e.length;++c){var u=l(e,c);u<0?"-"==e.charAt(c)&&0==this.signum()&&(i=!0):(s=n*s+u,++a>=o&&(this.dMultiply(r),this.dAddOffset(s,0),a=0,s=0))}a>0&&(this.dMultiply(Math.pow(n,a)),this.dAddOffset(s,0)),i&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,n,o){if("number"==typeof n)if(e<2)this.fromInt(1);else for(this.fromNumber(e,o),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),f,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(n);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var r=new Array,i=7&e;r.length=1+(e>>3),n.nextBytes(r),i>0?r[0]&=(1<<i)-1:r[0]=0,this.fromString(r,256)}},t.prototype.bitwiseTo=function(e,t,n){var o,r,i=Math.min(e.t,this.t);for(o=0;o<i;++o)n[o]=t(this[o],e[o]);if(e.t<this.t){for(r=e.s&this.DM,o=i;o<this.t;++o)n[o]=t(this[o],r);n.t=this.t}else{for(r=this.s&this.DM,o=i;o<e.t;++o)n[o]=t(r,e[o]);n.t=e.t}n.s=t(this.s,e.s),n.clamp()},t.prototype.changeBit=function(e,n){var o=t.ONE.shiftLeft(e);return this.bitwiseTo(o,n,o),o},t.prototype.addTo=function(e,t){for(var n=0,o=0,r=Math.min(e.t,this.t);n<r;)o+=this[n]+e[n],t[n++]=o&this.DM,o>>=this.DB;if(e.t<this.t){for(o+=e.s;n<this.t;)o+=this[n],t[n++]=o&this.DM,o>>=this.DB;o+=this.s}else{for(o+=this.s;n<e.t;)o+=e[n],t[n++]=o&this.DM,o>>=this.DB;o+=e.s}t.s=o<0?-1:0,o>0?t[n++]=o:o<-1&&(t[n++]=this.DV+o),t.t=n,t.clamp()},t.prototype.dMultiply=function(e){this[this.t]=this.am(0,e-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(e,t){if(0!=e){for(;this.t<=t;)this[this.t++]=0;for(this[t]+=e;this[t]>=this.DV;)this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}},t.prototype.multiplyLowerTo=function(e,t,n){var o,r=Math.min(this.t+e.t,t);for(n.s=0,n.t=r;r>0;)n[--r]=0;for(o=n.t-this.t;r<o;++r)n[r+this.t]=this.am(0,e[r],n,r,0,this.t);for(o=Math.min(e.t,t);r<o;++r)this.am(0,e[r],n,r,0,t-r);n.clamp()},t.prototype.multiplyUpperTo=function(e,t,n){--t;var o=n.t=this.t+e.t-t;for(n.s=0;--o>=0;)n[o]=0;for(o=Math.max(t-this.t,0);o<e.t;++o)n[this.t+o-t]=this.am(t-o,e[o],n,0,0,this.t+o-t);n.clamp(),n.drShiftTo(1,n)},t.prototype.modInt=function(e){if(e<=0)return 0;var t=this.DV%e,n=this.s<0?e-1:0;if(this.t>0)if(0==t)n=this[0]%e;else for(var o=this.t-1;o>=0;--o)n=(t*n+this[o])%e;return n},t.prototype.millerRabin=function(e){var o=this.subtract(t.ONE),r=o.getLowestSetBit();if(r<=0)return!1;var i=o.shiftRight(r);(e=e+1>>1)>_.length&&(e=_.length);for(var a=n(),s=0;s<e;++s){a.fromInt(_[Math.floor(Math.random()*_.length)]);var l=a.modPow(i,this);if(0!=l.compareTo(t.ONE)&&0!=l.compareTo(o)){for(var c=1;c++<r&&0!=l.compareTo(o);)if(0==(l=l.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=l.compareTo(o))return!1}}return!0},t.prototype.clone=function(){var e=n();return this.copyTo(e),e},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var e=this.t,t=new Array;t[0]=this.s;var n,o=this.DB-e*this.DB%8,r=0;if(e-- >0)for(o<this.DB&&(n=this[e]>>o)!=(this.s&this.DM)>>o&&(t[r++]=n|this.s<<this.DB-o);e>=0;)o<8?(n=(this[e]&(1<<o)-1)<<8-o,n|=this[--e]>>(o+=this.DB-8)):(n=this[e]>>(o-=8)&255,o<=0&&(o+=this.DB,--e)),0!=(128&n)&&(n|=-256),0==r&&(128&this.s)!=(128&n)&&++r,(r>0||n!=this.s)&&(t[r++]=n);return t},t.prototype.equals=function(e){return 0==this.compareTo(e)},t.prototype.min=function(e){return this.compareTo(e)<0?this:e},t.prototype.max=function(e){return this.compareTo(e)>0?this:e},t.prototype.and=function(e){var t=n();return this.bitwiseTo(e,p,t),t},t.prototype.or=function(e){var t=n();return this.bitwiseTo(e,f,t),t},t.prototype.xor=function(e){var t=n();return this.bitwiseTo(e,m,t),t},t.prototype.andNot=function(e){var t=n();return this.bitwiseTo(e,g,t),t},t.prototype.not=function(){for(var e=n(),t=0;t<this.t;++t)e[t]=this.DM&~this[t];return e.t=this.t,e.s=~this.s,e},t.prototype.shiftLeft=function(e){var t=n();return e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t),t},t.prototype.shiftRight=function(e){var t=n();return e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t),t},t.prototype.getLowestSetBit=function(){for(var e=0;e<this.t;++e)if(0!=this[e])return e*this.DB+y(this[e]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var e=0,t=this.s&this.DM,n=0;n<this.t;++n)e+=v(this[n]^t);return e},t.prototype.testBit=function(e){var t=Math.floor(e/this.DB);return t>=this.t?0!=this.s:0!=(this[t]&1<<e%this.DB)},t.prototype.setBit=function(e){return this.changeBit(e,f)},t.prototype.clearBit=function(e){return this.changeBit(e,g)},t.prototype.flipBit=function(e){return this.changeBit(e,m)},t.prototype.add=function(e){var t=n();return this.addTo(e,t),t},t.prototype.subtract=function(e){var t=n();return this.subTo(e,t),t},t.prototype.multiply=function(e){var t=n();return this.multiplyTo(e,t),t},t.prototype.divide=function(e){var t=n();return this.divRemTo(e,t,null),t},t.prototype.remainder=function(e){var t=n();return this.divRemTo(e,null,t),t},t.prototype.divideAndRemainder=function(e){var t=n(),o=n();return this.divRemTo(e,t,o),new Array(t,o)},t.prototype.modPow=function(e,t){var o,r,i=e.bitLength(),a=c(1);if(i<=0)return a;o=i<18?1:i<48?3:i<144?4:i<768?5:6,r=i<8?new d(t):t.isEven()?new k(t):new h(t);var s=new Array,l=3,p=o-1,f=(1<<o)-1;if(s[1]=r.convert(this),o>1){var m=n();for(r.sqrTo(s[1],m);l<=f;)s[l]=n(),r.mulTo(m,s[l-2],s[l]),l+=2}var g,y,v=e.t-1,b=!0,w=n();for(i=u(e[v])-1;v>=0;){for(i>=p?g=e[v]>>i-p&f:(g=(e[v]&(1<<i+1)-1)<<p-i,v>0&&(g|=e[v-1]>>this.DB+i-p)),l=o;0==(1&g);)g>>=1,--l;if((i-=l)<0&&(i+=this.DB,--v),b)s[g].copyTo(a),b=!1;else{for(;l>1;)r.sqrTo(a,w),r.sqrTo(w,a),l-=2;l>0?r.sqrTo(a,w):(y=a,a=w,w=y),r.mulTo(w,s[g],a)}for(;v>=0&&0==(e[v]&1<<i);)r.sqrTo(a,w),y=a,a=w,w=y,--i<0&&(i=this.DB-1,--v)}return r.revert(a)},t.prototype.modInverse=function(e){var n=e.isEven();if(this.isEven()&&n||0==e.signum())return t.ZERO;for(var o=e.clone(),r=this.clone(),i=c(1),a=c(0),s=c(0),l=c(1);0!=o.signum();){for(;o.isEven();)o.rShiftTo(1,o),n?(i.isEven()&&a.isEven()||(i.addTo(this,i),a.subTo(e,a)),i.rShiftTo(1,i)):a.isEven()||a.subTo(e,a),a.rShiftTo(1,a);for(;r.isEven();)r.rShiftTo(1,r),n?(s.isEven()&&l.isEven()||(s.addTo(this,s),l.subTo(e,l)),s.rShiftTo(1,s)):l.isEven()||l.subTo(e,l),l.rShiftTo(1,l);o.compareTo(r)>=0?(o.subTo(r,o),n&&i.subTo(s,i),a.subTo(l,a)):(r.subTo(o,r),n&&s.subTo(i,s),l.subTo(a,l))}return 0!=r.compareTo(t.ONE)?t.ZERO:l.compareTo(e)>=0?l.subtract(e):l.signum()<0?(l.addTo(e,l),l.signum()<0?l.add(e):l):l},t.prototype.pow=function(e){return this.exp(e,new b)},t.prototype.gcd=function(e){var t=this.s<0?this.negate():this.clone(),n=e.s<0?e.negate():e.clone();if(t.compareTo(n)<0){var o=t;t=n,n=o}var r=t.getLowestSetBit(),i=n.getLowestSetBit();if(i<0)return t;for(r<i&&(i=r),i>0&&(t.rShiftTo(i,t),n.rShiftTo(i,n));t.signum()>0;)(r=t.getLowestSetBit())>0&&t.rShiftTo(r,t),(r=n.getLowestSetBit())>0&&n.rShiftTo(r,n),t.compareTo(n)>=0?(t.subTo(n,t),t.rShiftTo(1,t)):(n.subTo(t,n),n.rShiftTo(1,n));return i>0&&n.lShiftTo(i,n),n},t.prototype.isProbablePrime=function(e){var t,n=this.abs();if(1==n.t&&n[0]<=_[_.length-1]){for(t=0;t<_.length;++t)if(n[0]==_[t])return!0;return!1}if(n.isEven())return!1;for(t=1;t<_.length;){for(var o=_[t],r=t+1;r<_.length&&o<N;)o*=_[r++];for(o=n.modInt(o);t<r;)if(o%_[t++]==0)return!1}return n.millerRabin(e)},t.prototype.square=function(){var e=n();return this.squareTo(e),e},t.prototype.Barrett=k,null==S){var E;if(S=new Array,C=0,"undefined"!=typeof window&&Dt)if(Dt.getRandomValues){var T=new Uint8Array(32);for(Dt.getRandomValues(T),E=0;E<32;++E)S[C++]=T[E]}else if("Netscape"==navigator.appName&&navigator.appVersion<"5"){var B=Dt.random(32);for(E=0;E<B.length;++E)S[C++]=255&B.charCodeAt(E)}for(;C<P;)E=Math.floor(65536*Math.random()),S[C++]=E>>>8,S[C++]=255&E;C=0,D()}function V(){if(null==x){for(D(),(x=new A).init(S),C=0;C<S.length;++C)S[C]=0;C=0}return x.next()}function I(){}function A(){this.i=0,this.j=0,this.S=new Array}I.prototype.nextBytes=function(e){var t;for(t=0;t<e.length;++t)e[t]=V()},A.prototype.init=function(e){var t,n,o;for(t=0;t<256;++t)this.S[t]=t;for(n=0,t=0;t<256;++t)n=n+this.S[t]+e[t%e.length]&255,o=this.S[t],this.S[t]=this.S[n],this.S[n]=o;this.i=0,this.j=0},A.prototype.next=function(){var e;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,e=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=e,this.S[e+this.S[this.i]&255]};var P=256;Et.exports={default:t,BigInteger:t,SecureRandom:I}}.call(_t);const{BigInteger:Bt}=Tt;class Vt{constructor(){this.tlv=null,this.t="00",this.l="00",this.v=""}getEncodedHex(){return this.tlv||(this.v=this.getValue(),this.l=this.getLength(),this.tlv=this.t+this.l+this.v),this.tlv}getLength(){const e=this.v.length/2;let t=e.toString(16);if(t.length%2==1&&(t="0"+t),e<128)return t;return(128+t.length/2).toString(16)+t}getValue(){return""}}class It extends Vt{constructor(e){super(),this.t="02",e&&(this.v=function(e){let t=e.toString(16);if("-"!==t[0])t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{t=t.substr(1);let n=t.length;n%2==1?n+=1:t.match(/^[0-7]/)||(n+=2);let o="";for(let e=0;e<n;e++)o+="f";o=new Bt(o,16),t=o.xor(e).add(Bt.ONE),t=t.toString(16).replace(/^-/,"")}return t}(e))}getValue(){return this.v}}class At extends Vt{constructor(e){super(),this.t="30",this.asn1Array=e}getValue(){return this.v=this.asn1Array.map((e=>e.getEncodedHex())).join(""),this.v}}function Pt(e,t){return+e[t+2]<8?1:128&+e.substr(t+2,2)}function $t(e,t){const n=Pt(e,t),o=e.substr(t+2,2*n);if(!o)return-1;return(+o[0]<8?new Bt(o,16):new Bt(o.substr(2),16)).intValue()}function Ot(e,t){return t+2*(Pt(e,t)+1)}var Ft={encodeDer(e,t){const n=new It(e),o=new It(t);return new At([n,o]).getEncodedHex()},decodeDer(e){const t=Ot(e,0),n=Ot(e,t),o=$t(e,t),r=e.substr(n,2*o),i=n+r.length,a=Ot(e,i),s=$t(e,i),l=e.substr(a,2*s);return{r:new Bt(r,16),s:new Bt(l,16)}}};const{BigInteger:Mt}=Tt,Lt=new Mt("2"),zt=new Mt("3");class Rt{constructor(e,t){this.x=t,this.q=e}equals(e){return e===this||this.q.equals(e.q)&&this.x.equals(e.x)}toBigInteger(){return this.x}negate(){return new Rt(this.q,this.x.negate().mod(this.q))}add(e){return new Rt(this.q,this.x.add(e.toBigInteger()).mod(this.q))}subtract(e){return new Rt(this.q,this.x.subtract(e.toBigInteger()).mod(this.q))}multiply(e){return new Rt(this.q,this.x.multiply(e.toBigInteger()).mod(this.q))}divide(e){return new Rt(this.q,this.x.multiply(e.toBigInteger().modInverse(this.q)).mod(this.q))}square(){return new Rt(this.q,this.x.square().mod(this.q))}}class Ut{constructor(e,t,n,o){this.curve=e,this.x=t,this.y=n,this.z=null==o?Mt.ONE:o,this.zinv=null}getX(){return null===this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}getY(){return null===this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}equals(e){if(e===this)return!0;if(this.isInfinity())return e.isInfinity();if(e.isInfinity())return this.isInfinity();if(!e.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(e.z)).mod(this.curve.q).equals(Mt.ZERO))return!1;return e.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(e.z)).mod(this.curve.q).equals(Mt.ZERO)}isInfinity(){return null===this.x&&null===this.y||this.z.equals(Mt.ZERO)&&!this.y.toBigInteger().equals(Mt.ZERO)}negate(){return new Ut(this.curve,this.x,this.y.negate(),this.z)}add(e){if(this.isInfinity())return e;if(e.isInfinity())return this;const t=this.x.toBigInteger(),n=this.y.toBigInteger(),o=this.z,r=e.x.toBigInteger(),i=e.y.toBigInteger(),a=e.z,s=this.curve.q,l=t.multiply(a).mod(s),c=r.multiply(o).mod(s),u=l.subtract(c),d=n.multiply(a).mod(s),h=i.multiply(o).mod(s),p=d.subtract(h);if(Mt.ZERO.equals(u))return Mt.ZERO.equals(p)?this.twice():this.curve.infinity;const f=l.add(c),m=o.multiply(a).mod(s),g=u.square().mod(s),y=u.multiply(g).mod(s),v=m.multiply(p.square()).subtract(f.multiply(g)).mod(s),b=u.multiply(v).mod(s),w=p.multiply(g.multiply(l).subtract(v)).subtract(d.multiply(y)).mod(s),k=y.multiply(m).mod(s);return new Ut(this.curve,this.curve.fromBigInteger(b),this.curve.fromBigInteger(w),k)}twice(){if(this.isInfinity())return this;if(!this.y.toBigInteger().signum())return this.curve.infinity;const e=this.x.toBigInteger(),t=this.y.toBigInteger(),n=this.z,o=this.curve.q,r=this.curve.a.toBigInteger(),i=e.square().multiply(zt).add(r.multiply(n.square())).mod(o),a=t.shiftLeft(1).multiply(n).mod(o),s=t.square().mod(o),l=s.multiply(e).multiply(n).mod(o),c=a.square().mod(o),u=i.square().subtract(l.shiftLeft(3)).mod(o),d=a.multiply(u).mod(o),h=i.multiply(l.shiftLeft(2).subtract(u)).subtract(c.shiftLeft(1).multiply(s)).mod(o),p=a.multiply(c).mod(o);return new Ut(this.curve,this.curve.fromBigInteger(d),this.curve.fromBigInteger(h),p)}multiply(e){if(this.isInfinity())return this;if(!e.signum())return this.curve.infinity;const t=e.multiply(zt),n=this.negate();let o=this;for(let r=t.bitLength()-2;r>0;r--){o=o.twice();const i=t.testBit(r);i!==e.testBit(r)&&(o=o.add(i?this:n))}return o}}var qt={ECPointFp:Ut,ECCurveFp:class{constructor(e,t,n){this.q=e,this.a=this.fromBigInteger(t),this.b=this.fromBigInteger(n),this.infinity=new Ut(this,null,null)}equals(e){return e===this||this.q.equals(e.q)&&this.a.equals(e.a)&&this.b.equals(e.b)}fromBigInteger(e){return new Rt(this.q,e)}decodePointHex(e){switch(parseInt(e.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:const t=this.fromBigInteger(new Mt(e.substr(2),16));let n=this.fromBigInteger(t.multiply(t.square()).add(t.multiply(this.a)).add(this.b).toBigInteger().modPow(this.q.divide(new Mt("4")).add(Mt.ONE),this.q));return n.toBigInteger().mod(Lt).equals(new Mt(e.substr(0,2),16).subtract(Lt))||(n=n.negate()),new Ut(this,t,n);case 4:case 6:case 7:const o=(e.length-2)/2,r=e.substr(2,o),i=e.substr(o+2,o);return new Ut(this,this.fromBigInteger(new Mt(r,16)),this.fromBigInteger(new Mt(i,16)));default:return null}}}};const{BigInteger:jt,SecureRandom:Ht}=Tt,{ECCurveFp:Wt}=qt,Kt=new Ht,{curve:Yt,G:Gt,n:Jt}=Zt();function Zt(){const e=new jt("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),t=new jt("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),n=new jt("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16),o=new Wt(e,t,n),r=o.decodePointHex("0432C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0");return{curve:o,G:r,n:new jt("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16)}}function Xt(e,t){return e.length>=t?e:new Array(t-e.length+1).join("0")+e}var Qt={getGlobalCurve:function(){return Yt},generateEcparam:Zt,generateKeyPairHex:function(e,t,n){const o=(e?new jt(e,t,n):new jt(Jt.bitLength(),Kt)).mod(Jt.subtract(jt.ONE)).add(jt.ONE),r=Xt(o.toString(16),64),i=Gt.multiply(o);return{privateKey:r,publicKey:"04"+Xt(i.getX().toBigInteger().toString(16),64)+Xt(i.getY().toBigInteger().toString(16),64)}},compressPublicKeyHex:function(e){if(130!==e.length)throw new Error("Invalid public key to compress");const t=(e.length-2)/2,n=e.substr(2,t);let o="03";return new jt(e.substr(t+2,t),16).mod(new jt("2")).equals(jt.ZERO)&&(o="02"),o+n},utf8ToHex:function(e){const t=(e=unescape(encodeURIComponent(e))).length,n=[];for(let r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;const o=[];for(let r=0;r<t;r++){const e=n[r>>>2]>>>24-r%4*8&255;o.push((e>>>4).toString(16)),o.push((15&e).toString(16))}return o.join("")},leftPad:Xt,arrayToHex:function(e){return e.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join("")},arrayToUtf8:function(e){const t=[];let n=0;for(let o=0;o<2*e.length;o+=2)t[o>>>3]|=parseInt(e[n],10)<<24-o%8*4,n++;try{const n=[];for(let o=0;o<e.length;o++){const e=t[o>>>2]>>>24-o%4*8&255;n.push(String.fromCharCode(e))}return decodeURIComponent(escape(n.join("")))}catch(Er){throw new Error("Malformed UTF-8 data")}},hexToArray:function(e){const t=[];let n=e.length;n%2!=0&&(e=Xt(e,n+1)),n=e.length;for(let o=0;o<n;o+=2)t.push(parseInt(e.substr(o,2),16));return t},verifyPublicKey:function(e){const t=Yt.decodePointHex(e);if(!t)return!1;const n=t.getX();return t.getY().square().equals(n.multiply(n.square()).add(n.multiply(Yt.a)).add(Yt.b))},comparePublicKeyHex:function(e,t){const n=Yt.decodePointHex(e);if(!n)return!1;const o=Yt.decodePointHex(t);return!!o&&n.equals(o)}};const en=new Uint32Array(68),tn=new Uint32Array(64);function nn(e,t){const n=31&t;return e<<n|e>>>32-n}function on(e,t){const n=[];for(let o=e.length-1;o>=0;o--)n[o]=255&(e[o]^t[o]);return n}function rn(e){return e^nn(e,9)^nn(e,17)}function an(e){let t=8*e.length,n=t%512;n=n>=448?512-n%448-1:448-n-1;const o=new Array((n-7)/8),r=new Array(8);for(let d=0,h=o.length;d<h;d++)o[d]=0;for(let d=0,h=r.length;d<h;d++)r[d]=0;t=t.toString(2);for(let d=7;d>=0;d--)if(t.length>8){const e=t.length-8;r[d]=parseInt(t.substr(e),2),t=t.substr(0,e)}else t.length>0&&(r[d]=parseInt(t,2),t="");const i=new Uint8Array([...e,128,...o,...r]),a=new DataView(i.buffer,0),s=i.length/64,l=new Uint32Array([1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214]);for(let d=0;d<s;d++){en.fill(0),tn.fill(0);const e=16*d;for(let l=0;l<16;l++)en[l]=a.getUint32(4*(e+l),!1);for(let a=16;a<68;a++)en[a]=(c=en[a-16]^en[a-9]^nn(en[a-3],15))^nn(c,15)^nn(c,23)^nn(en[a-13],7)^en[a-6];for(let a=0;a<64;a++)tn[a]=en[a]^en[a+4];const t=2043430169,n=2055708042;let o,r,i,s,u,h=l[0],p=l[1],f=l[2],m=l[3],g=l[4],y=l[5],v=l[6],b=l[7];for(let a=0;a<64;a++)u=a>=0&&a<=15?t:n,o=nn(nn(h,12)+g+nn(u,a),7),r=o^nn(h,12),i=(a>=0&&a<=15?h^p^f:h&p|h&f|p&f)+m+r+tn[a],s=(a>=0&&a<=15?g^y^v:g&y|~g&v)+b+o+en[a],m=f,f=nn(p,9),p=h,h=i,b=v,v=nn(y,19),y=g,g=rn(s);l[0]^=h,l[1]^=p,l[2]^=f,l[3]^=m,l[4]^=g,l[5]^=y,l[6]^=v,l[7]^=b}var c;const u=[];for(let d=0,h=l.length;d<h;d++){const e=l[d];u.push((4278190080&e)>>>24,(16711680&e)>>>16,(65280&e)>>>8,255&e)}return u}const sn=new Uint8Array(64),ln=new Uint8Array(64);for(let df=0;df<64;df++)sn[df]=54,ln[df]=92;var cn={sm3:an,hmac:function(e,t){for(t.length>64&&(t=an(t));t.length<64;)t.push(0);const n=on(t,sn),o=on(t,ln),r=an([...n,...e]);return an([...o,...r])}};const{BigInteger:un}=Tt,{encodeDer:dn,decodeDer:hn}=Ft,pn=Qt,fn=cn.sm3,{G:mn,curve:gn,n:yn}=pn.generateEcparam();function vn(e,t,n="1234567812345678"){n=pn.utf8ToHex(n);const o=pn.leftPad(mn.curve.a.toBigInteger().toRadix(16),64),r=pn.leftPad(mn.curve.b.toBigInteger().toRadix(16),64),i=pn.leftPad(mn.getX().toBigInteger().toRadix(16),64),a=pn.leftPad(mn.getY().toBigInteger().toRadix(16),64);let s,l;if(128===t.length)s=t.substr(0,64),l=t.substr(64,64);else{const e=mn.curve.decodePointHex(t);s=pn.leftPad(e.getX().toBigInteger().toRadix(16),64),l=pn.leftPad(e.getY().toBigInteger().toRadix(16),64)}const c=pn.hexToArray(n+o+r+i+a+s+l),u=4*n.length;c.unshift(255&u),c.unshift(u>>8&255);const d=fn(c);return pn.arrayToHex(fn(d.concat(pn.hexToArray(e))))}function bn(e){const t=mn.multiply(new un(e,16));return"04"+pn.leftPad(t.getX().toBigInteger().toString(16),64)+pn.leftPad(t.getY().toBigInteger().toString(16),64)}function wn(){const e=pn.generateKeyPairHex(),t=gn.decodePointHex(e.publicKey);return e.k=new un(e.privateKey,16),e.x1=t.getX().toBigInteger(),e}var kn={generateKeyPairHex:pn.generateKeyPairHex,compressPublicKeyHex:pn.compressPublicKeyHex,comparePublicKeyHex:pn.comparePublicKeyHex,doEncrypt:function(e,t,n=1){e="string"==typeof e?pn.hexToArray(pn.utf8ToHex(e)):Array.prototype.slice.call(e),t=pn.getGlobalCurve().decodePointHex(t);const o=pn.generateKeyPairHex(),r=new un(o.privateKey,16);let i=o.publicKey;i.length>128&&(i=i.substr(i.length-128));const a=t.multiply(r),s=pn.hexToArray(pn.leftPad(a.getX().toBigInteger().toRadix(16),64)),l=pn.hexToArray(pn.leftPad(a.getY().toBigInteger().toRadix(16),64)),c=pn.arrayToHex(fn([].concat(s,e,l)));let u=1,d=0,h=[];const p=[].concat(s,l),f=()=>{h=fn([...p,u>>24&255,u>>16&255,u>>8&255,255&u]),u++,d=0};f();for(let g=0,y=e.length;g<y;g++)d===h.length&&f(),e[g]^=255&h[d++];const m=pn.arrayToHex(e);return 0===n?i+m+c:i+c+m},doDecrypt:function(e,t,n=1,{output:o="string"}={}){t=new un(t,16);let r=e.substr(128,64),i=e.substr(192);0===n&&(r=e.substr(e.length-64),i=e.substr(128,e.length-128-64));const a=pn.hexToArray(i),s=pn.getGlobalCurve().decodePointHex("04"+e.substr(0,128)).multiply(t),l=pn.hexToArray(pn.leftPad(s.getX().toBigInteger().toRadix(16),64)),c=pn.hexToArray(pn.leftPad(s.getY().toBigInteger().toRadix(16),64));let u=1,d=0,h=[];const p=[].concat(l,c),f=()=>{h=fn([...p,u>>24&255,u>>16&255,u>>8&255,255&u]),u++,d=0};f();for(let m=0,g=a.length;m<g;m++)d===h.length&&f(),a[m]^=255&h[d++];return pn.arrayToHex(fn([].concat(l,a,c)))===r.toLowerCase()?"array"===o?a:pn.arrayToUtf8(a):"array"===o?[]:""},doSignature:function(e,t,{pointPool:n,der:o,hash:r,publicKey:i,userId:a}={}){let s="string"==typeof e?pn.utf8ToHex(e):pn.arrayToHex(e);r&&(s=vn(s,i=i||bn(t),a));const l=new un(t,16),c=new un(s,16);let u=null,d=null,h=null;do{do{let e;e=n&&n.length?n.pop():wn(),u=e.k,d=c.add(e.x1).mod(yn)}while(d.equals(un.ZERO)||d.add(u).equals(yn));h=l.add(un.ONE).modInverse(yn).multiply(u.subtract(d.multiply(l))).mod(yn)}while(h.equals(un.ZERO));return o?dn(d,h):pn.leftPad(d.toString(16),64)+pn.leftPad(h.toString(16),64)},doVerifySignature:function(e,t,n,{der:o,hash:r,userId:i}={}){let a,s,l="string"==typeof e?pn.utf8ToHex(e):pn.arrayToHex(e);if(r&&(l=vn(l,n,i)),o){const e=hn(t);a=e.r,s=e.s}else a=new un(t.substring(0,64),16),s=new un(t.substring(64),16);const c=gn.decodePointHex(n),u=new un(l,16),d=a.add(s).mod(yn);if(d.equals(un.ZERO))return!1;const h=mn.multiply(s).add(c.multiply(d)),p=u.add(h.getX().toBigInteger()).mod(yn);return a.equals(p)},getPublicKeyFromPrivateKey:bn,getPoint:wn,verifyPublicKey:pn.verifyPublicKey};const{sm3:xn,hmac:Sn}=cn;function Cn(e){return e.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join("")}function _n(e){const t=[];let n=e.length;var o,r;n%2!=0&&(r=n+1,e=(o=e).length>=r?o:new Array(r-o.length+1).join("0")+o),n=e.length;for(let i=0;i<n;i+=2)t.push(parseInt(e.substr(i,2),16));return t}const Nn=16,Dn=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],En=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function Tn(e){const t=[];for(let n=0,o=e.length;n<o;n+=2)t.push(parseInt(e.substr(n,2),16));return t}function Bn(e,t){const n=31&t;return e<<n|e>>>32-n}function Vn(e){return(255&Dn[e>>>24&255])<<24|(255&Dn[e>>>16&255])<<16|(255&Dn[e>>>8&255])<<8|255&Dn[255&e]}function In(e){return e^Bn(e,2)^Bn(e,10)^Bn(e,18)^Bn(e,24)}function An(e){return e^Bn(e,13)^Bn(e,23)}function Pn(e,t,n){const o=new Array(4),r=new Array(4);for(let i=0;i<4;i++)r[0]=255&e[4*i],r[1]=255&e[4*i+1],r[2]=255&e[4*i+2],r[3]=255&e[4*i+3],o[i]=r[0]<<24|r[1]<<16|r[2]<<8|r[3];for(let i,a=0;a<32;a+=4)i=o[1]^o[2]^o[3]^n[a+0],o[0]^=In(Vn(i)),i=o[2]^o[3]^o[0]^n[a+1],o[1]^=In(Vn(i)),i=o[3]^o[0]^o[1]^n[a+2],o[2]^=In(Vn(i)),i=o[0]^o[1]^o[2]^n[a+3],o[3]^=In(Vn(i));for(let i=0;i<16;i+=4)t[i]=o[3-i/4]>>>24&255,t[i+1]=o[3-i/4]>>>16&255,t[i+2]=o[3-i/4]>>>8&255,t[i+3]=255&o[3-i/4]}function $n(e,t,n,{padding:o="pkcs#7",mode:r,iv:i=[],output:a="string"}={}){if("cbc"===r&&("string"==typeof i&&(i=Tn(i)),16!==i.length))throw new Error("iv is invalid");if("string"==typeof t&&(t=Tn(t)),16!==t.length)throw new Error("key is invalid");if(e="string"==typeof e?0!==n?function(e){const t=[];for(let n=0,o=e.length;n<o;n++){const o=e.codePointAt(n);if(o<=127)t.push(o);else if(o<=2047)t.push(192|o>>>6),t.push(128|63&o);else if(o<=55295||o>=57344&&o<=65535)t.push(224|o>>>12),t.push(128|o>>>6&63),t.push(128|63&o);else{if(!(o>=65536&&o<=1114111))throw t.push(o),new Error("input is not supported");n++,t.push(240|o>>>18&28),t.push(128|o>>>12&63),t.push(128|o>>>6&63),t.push(128|63&o)}}return t}(e):Tn(e):[...e],("pkcs#5"===o||"pkcs#7"===o)&&0!==n){const t=Nn-e.length%Nn;for(let n=0;n<t;n++)e.push(t)}const s=new Array(32);!function(e,t,n){const o=new Array(4),r=new Array(4);for(let i=0;i<4;i++)r[0]=255&e[0+4*i],r[1]=255&e[1+4*i],r[2]=255&e[2+4*i],r[3]=255&e[3+4*i],o[i]=r[0]<<24|r[1]<<16|r[2]<<8|r[3];o[0]^=2746333894,o[1]^=1453994832,o[2]^=1736282519,o[3]^=2993693404;for(let i,a=0;a<32;a+=4)i=o[1]^o[2]^o[3]^En[a+0],t[a+0]=o[0]^=An(Vn(i)),i=o[2]^o[3]^o[0]^En[a+1],t[a+1]=o[1]^=An(Vn(i)),i=o[3]^o[0]^o[1]^En[a+2],t[a+2]=o[2]^=An(Vn(i)),i=o[0]^o[1]^o[2]^En[a+3],t[a+3]=o[3]^=An(Vn(i));if(0===n)for(let i,a=0;a<16;a++)i=t[a],t[a]=t[31-a],t[31-a]=i}(t,s,n);const l=[];let c=i,u=e.length,d=0;for(;u>=Nn;){const t=e.slice(d,d+16),o=new Array(16);if("cbc"===r)for(let e=0;e<Nn;e++)0!==n&&(t[e]^=c[e]);Pn(t,o,s);for(let e=0;e<Nn;e++)"cbc"===r&&0===n&&(o[e]^=c[e]),l[d+e]=o[e];"cbc"===r&&(c=0!==n?o:t),u-=Nn,d+=Nn}if(("pkcs#5"===o||"pkcs#7"===o)&&0===n){const e=l.length,t=l[e-1];for(let n=1;n<=t;n++)if(l[e-n]!==t)throw new Error("padding is invalid");l.splice(e-t,t)}return"array"!==a?0!==n?l.map((e=>1===(e=e.toString(16)).length?"0"+e:e)).join(""):function(e){const t=[];for(let n=0,o=e.length;n<o;n++)e[n]>=240&&e[n]<=247?(t.push(String.fromCodePoint(((7&e[n])<<18)+((63&e[n+1])<<12)+((63&e[n+2])<<6)+(63&e[n+3]))),n+=3):e[n]>=224&&e[n]<=239?(t.push(String.fromCodePoint(((15&e[n])<<12)+((63&e[n+1])<<6)+(63&e[n+2]))),n+=2):e[n]>=192&&e[n]<=223?(t.push(String.fromCodePoint(((31&e[n])<<6)+(63&e[n+1]))),n++):t.push(String.fromCodePoint(e[n]));return t.join("")}(l):l}var On={encrypt:(e,t,n)=>$n(e,t,1,n),decrypt:(e,t,n)=>$n(e,t,0,n)},Fn={sm2:kn,sm3:function(e,t){if(e="string"==typeof e?function(e){const t=[];for(let n=0,o=e.length;n<o;n++){const o=e.codePointAt(n);if(o<=127)t.push(o);else if(o<=2047)t.push(192|o>>>6),t.push(128|63&o);else if(o<=55295||o>=57344&&o<=65535)t.push(224|o>>>12),t.push(128|o>>>6&63),t.push(128|63&o);else{if(!(o>=65536&&o<=1114111))throw t.push(o),new Error("input is not supported");n++,t.push(240|o>>>18&28),t.push(128|o>>>12&63),t.push(128|o>>>6&63),t.push(128|63&o)}}return t}(e):Array.prototype.slice.call(e),t){if("hmac"!==(t.mode||"hmac"))throw new Error("invalid mode");let n=t.key;if(!n)throw new Error("invalid key");return n="string"==typeof n?_n(n):Array.prototype.slice.call(n),Cn(Sn(e,n))}return Cn(xn(e))},sm4:On};const Mn=Fn.sm2,Ln=Fn.sm3,zn=Fn.sm4,Rn="04298364ec840088475eae92a591e01284d1abefcda348b47eb324bb521bb03b0b2a5bc393f6b71dabb8f15c99a0050818b56b23f31743b93df9cf8948f15ddb54",Un="3037723d47292171677ec8bd7dc9af696c7472bc5f251b2cec07e65fdef22e25",qn="0123456789abcdeffedcba9876543210",jn={doSm2Encrypt:e=>Mn.doEncrypt(e,Rn,1),doSm2Decrypt:e=>Mn.doDecrypt(e,Un,1),doSm2ArrayEncrypt:e=>Mn.doEncrypt(e,Rn,1),doSm2ArrayDecrypt:e=>Mn.doDecrypt(e,Un,1,{output:"array"}),doSm3Hash:e=>Ln(e),doSm4Encrypt:e=>zn.encrypt(e,qn),doSm4CbcEncrypt:e=>zn.encrypt(e,qn,{mode:"cbc",iv:"fedcba98765432100123456789abcdef"}),doSm4Decrypt:e=>zn.decrypt(e,qn),doSm4CbcDecrypt:e=>zn.decrypt(e,qn,{mode:"cbc",iv:"fedcba98765432100123456789abcdef"})};function Hn(e){return ro({url:"/sys/userCenter/getPositionListByIdList",method:"post",data:e})}function Wn(e){return ro({url:"/sys/userCenter/getUserListByIdList",method:"post",data:e})}const Kn="App-Token";function Yn(){return uni.getStorageSync(Kn)}const Gn={DEFAULT_ENV_KEY:"local",DEFAULT_ALL_ENV:{local:{name:"本地环境",baseUrl:"http://*************:82",tenantDomain:"http://localhost:81"},pro:{name:"生产环境",baseUrl:"https://snowyapi.xiaonuo.vip",tenantDomain:"https://snowy.xiaonuo.vip"}}},Jn={state:{envKey:St(ht)||Gn.DEFAULT_ENV_KEY,allEnv:St(pt)||Gn.DEFAULT_ALL_ENV,token:Yn(),homeConfigs:St(mt)||dt.HOME_CONFIGS,userMobileMenus:St(yt),userInfo:St(gt),sysBaseConfig:St(ft)||dt.SYS_BASE_CONFIG,dictTypeTreeData:St(vt)},mutations:{SET_envKey:(e,t)=>{e.envKey=t,xt(ht,t)},SET_allEnv:(e,t)=>{e.allEnv=t,xt(pt,t)},SET_token:(e,t)=>{e.token=t,function(e){uni.setStorageSync(Kn,e)}(t)},SET_homeConfigs:(e,t)=>{e.homeConfigs=t,xt(mt,t)},SET_userMobileMenus:(e,t)=>{e.userMobileMenus=t,xt(yt,t)},SET_userInfo:(e,t)=>{e.userInfo=t,xt(gt,t)},SET_sysBaseConfig:(e,t)=>{e.sysBaseConfig=t,xt(ft,t)},SET_dictTypeTreeData:(e,t)=>{e.dictTypeTreeData=t,xt(vt,t)},CLEAR_cache:e=>{e.token="",uni.removeStorageSync(Kn),e.userMobileMenus={},Ct(yt),e.userInfo={},Ct(gt),e.dictTypeTreeData={},Ct(vt)}},actions:{Login({commit:e},t){const n={account:t.account.trim(),password:jn.doSm2Encrypt(t.password),validCode:t.validCode,validCodeReqNo:t.validCodeReqNo};return new Promise(((t,o)=>{var r;(r=n,ro({url:"/auth/b/doLogin",extConf:{isToken:!1},method:"post",data:r})).then((n=>{e("SET_token",n.data),t(n.data)})).catch((e=>{o(e)}))}))},GetUserInfo:({commit:e,state:t})=>new Promise(((t,n)=>{ro({url:"/auth/b/getLoginUser",method:"get"}).then((n=>{e("SET_userInfo",n.data),t(n.data)})).catch((e=>{n(e)}))})),GetUserLoginMenu:({commit:e,state:t})=>new Promise(((t,n)=>{ro({url:"/sys/userCenter/loginMobileMenu",method:"get"}).then((n=>{e("SET_userMobileMenus",n.data),t(n.data)})).catch((e=>{n(e)}))})),GetDictTypeTreeData:({commit:e,state:t})=>new Promise(((t,n)=>{var o;ro({url:"/dev/dict/tree",method:"get",data:o}).then((n=>{n.data&&(e("SET_dictTypeTreeData",n.data),t(n.data))})).catch((e=>{n(e)}))})),GetSysBaseConfig:({commit:e,state:t})=>new Promise(((t,n)=>{let o={};ro({url:"/dev/config/sysBaseList",extConf:{isToken:!1},method:"get",timeout:2e4}).then((n=>{n.data&&(n.data.forEach((e=>{o[e.configKey]=e.configValue})),e("SET_sysBaseConfig",o)),t(o)}))})).catch((e=>{reject(e)})),LogOut:({commit:e,state:t,dispatch:n})=>new Promise(((t,n)=>{ro({url:"/auth/b/doLogout",method:"get"}).then((()=>{e("CLEAR_cache"),t()})).catch((e=>{n(e)}))}))}},Zn=new st({modules:{global:Jn},getters:{envKey:e=>e.global.envKey,allEnv:e=>e.global.allEnv,homeConfigs:e=>e.global.homeConfigs,token:e=>e.global.token,userMobileMenus:e=>e.global.userMobileMenus,userInfo:e=>e.global.userInfo,sysBaseConfig:e=>e.global.sysBaseConfig,dictTypeTreeData:e=>e.global.dictTypeTreeData}});const Xn=[401,1011007,1011008],Qn={400:"发出的请求有错误，服务器没有进行新建或修改数据的操作。",401:"用户没有权限（令牌、用户名、密码错误）。",403:"用户得到授权，但是访问是被禁止的。",404:"发出的请求针对的是不存在的记录，服务器没有进行操作。",406:"请求的格式不可得。",410:"请求的资源被永久删除，且不会再得到的。",422:"当创建一个对象时，发生一个验证错误。",500:"服务器发生错误，请检查服务器。",502:"网关错误。",503:"服务不可用，服务器暂时过载或维护。",504:"网关超时。",default:"系统未知错误，请反馈给管理员"};const{TIMEOUT:eo,TOKEN_NAME:to,TOKEN_PREFIX:no,NO_TOKEN_BACK_URL:oo}=dt,ro=e=>{e.url=e.url;const t=!1===(e.extConf||{}).isToken;if(e.header=e.header||{},Yn()&&!t&&(e.header[to]=no+Yn()),e.header.Domain=Zn.getters.allEnv[Zn.getters.envKey].tenantDomain,e.params){let t=e.url+"?"+function(e){let t="";for(const o of Object.keys(e)){const r=e[o];var n=encodeURIComponent(o)+"=";if(null!==r&&""!==r&&void 0!==r)if("object"==typeof r)for(const e of Object.keys(r))null!==r[e]&&""!==r[e]&&void 0!==r[e]&&(t+=encodeURIComponent(o+"["+e+"]")+"="+encodeURIComponent(r[e])+"&");else t+=n+encodeURIComponent(r)+"&"}return t}(e.params);t=t.slice(0,-1),e.url=t}return new Promise(((t,n)=>{uni.$snowy.modal.loading("努力加载中"),uni.request({method:e.method||"get",timeout:e.timeout||eo,url:(e.baseUrl||Zn.getters.allEnv[Zn.getters.envKey].baseUrl)+e.url,data:e.data,header:e.header,dataType:"json"}).then((e=>{const o=e.data.code||200,r=e.data.msg||Qn[o]||Qn.default;Xn.includes(o)?(uni.$snowy.modal.confirm(r||"登录状态已过期，您可以清除缓存，重新进行登录?").then((()=>{Zn.commit("CLEAR_cache"),uni.$snowy.tab.reLaunch(oo)})),n("无效的会话，或者会话已过期，请重新登录。")):200!==o&&(uni.$snowy.modal.alert(r),n(o)),t(e.data)})).catch((e=>{let{errMsg:t}=e;"Network Error"===t?t="后端接口连接异常":t.includes("timeout")?t="系统接口请求超时":t.includes("Request failed with status code")?t="系统接口"+t.substr(t.length-3)+"异常":t.includes("request:fail")&&(t="请求失败"),uni.$snowy.modal.alert(t),n(e)})).finally((()=>{uni.$snowy.modal.closeLoading()}))}))};function io(){let e=uni.getSystemInfoSync().platform;if("ios"==e){let e=new(plus.ios.importClass("UIImpactFeedbackGenerator"));e.prepare(),e.init(1),e.impactOccurred()}"android"==e&&uni.vibrateShort()}const ao=Ve({__name:"login",setup(t){e.getCurrentInstance();const n=()=>{uni.$snowy.tab.reLaunch("/pages/config/index")},o=e.computed((()=>Zn.getters.sysBaseConfig)),r=e.ref(""),i=e.reactive({account:"superAdmin",password:"123456",validCode:"",validCodeReqNo:""}),a=()=>{ro({url:"/auth/b/getPicCaptcha",extConf:{isToken:!1},method:"get",timeout:2e4}).then((e=>{r.value=e.data.validCodeBase64,i.validCodeReqNo=e.data.validCodeReqNo}))};o.value.SNOWY_SYS_DEFAULT_CAPTCHA_OPEN&&a();const s=async()=>{""===i.account?uni.$snowy.modal.msgError("请输入账号"):""===i.password?uni.$snowy.modal.msgError("请输入密码"):""===i.validCode&&"true"===o.value.SNOWY_SYS_DEFAULT_CAPTCHA_OPEN?uni.$snowy.modal.msgError("请输入验证码"):l()},l=async()=>{Zn.dispatch("Login",i).then((()=>{Promise.all([Zn.dispatch("GetUserLoginMenu"),Zn.dispatch("GetUserInfo"),Zn.dispatch("GetDictTypeTreeData")]).then((e=>{uni.$snowy.tab.reLaunch("/pages/home/<USER>")})).catch((e=>{io(),o.value.SNOWY_SYS_DEFAULT_CAPTCHA_OPEN&&(a(),i.validCode="")}))})).catch((e=>{io(),o.value.SNOWY_SYS_DEFAULT_CAPTCHA_OPEN&&(a(),i.validCode="")}))};return(t,l)=>{const c=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"logo-content",onClick:n},[e.createElementVNode("image",{style:{width:"100rpx",height:"100rpx"},alt:e.unref(o).SNOWY_SYS_NAME,src:e.unref(o).SNOWY_SYS_LOGO,mode:"widthFix"},null,8,["alt","src"]),e.createElementVNode("text",{class:"title"},e.toDisplayString(e.unref(o).SNOWY_SYS_NAME)+" "+e.toDisplayString(e.unref(o).SNOWY_SYS_VERSION),1)]),e.createElementVNode("view",{class:"login-form-content"},[e.createElementVNode("view",{class:"input-item"},[e.createVNode(c,{class:"icon",name:"account-fill",size:"20",color:"#999"}),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":l[0]||(l[0]=e=>i.account=e),class:"input",type:"text",placeholder:"请输入账号",maxlength:"30"},null,512),[[e.vModelText,i.account]])]),e.createElementVNode("view",{class:"input-item"},[e.createVNode(c,{class:"icon",name:"lock-fill",size:"20",color:"#999"}),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":l[1]||(l[1]=e=>i.password=e),type:"password",class:"input",placeholder:"请输入密码",maxlength:"20"},null,512),[[e.vModelText,i.password]])]),"true"===e.unref(o).SNOWY_SYS_DEFAULT_CAPTCHA_OPEN?(e.openBlock(),e.createElementBlock("view",{key:0,class:"input-item"},[e.createVNode(c,{class:"icon",name:"empty-permission",size:"20",color:"#999"}),e.withDirectives(e.createElementVNode("input",{"onUpdate:modelValue":l[2]||(l[2]=e=>i.validCode=e),type:"text",class:"input",placeholder:"请输入验证码",maxlength:"4"},null,512),[[e.vModelText,i.validCode]]),e.createElementVNode("image",{src:r.value,onClick:a,class:"login-code-img"},null,8,["src"])])):e.createCommentVNode("",!0),e.createElementVNode("view",null,[e.createElementVNode("button",{onClick:s,class:"login-btn",type:"primary"},"登录")])])])}}},[["__scopeId","data-v-ca2d58ec"]]);function so(e="rgb(0, 0, 0)",t="rgb(255, 255, 255)",n=10){const o=lo(e,!1),r=o[0],i=o[1],a=o[2],s=lo(t,!1),l=(s[0]-r)/n,c=(s[1]-i)/n,u=(s[2]-a)/n,d=[];for(let h=0;h<n;h++){let o=co(`rgb(${Math.round(l*h+r)},${Math.round(c*h+i)},${Math.round(u*h+a)})`);0===h&&(o=co(e)),h===n-1&&(o=co(t)),d.push(o)}return d}function lo(e,t=!0){if((e=String(e).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e)){if(4===e.length){let t="#";for(let n=1;n<4;n+=1)t+=e.slice(n,n+1).concat(e.slice(n,n+1));e=t}const n=[];for(let t=1;t<7;t+=2)n.push(parseInt(`0x${e.slice(t,t+2)}`));return t?`rgb(${n[0]},${n[1]},${n[2]})`:n}if(/^(rgb|RGB)/.test(e)){return e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((e=>Number(e)))}return e}function co(e){const t=e;if(/^(rgb|RGB)/.test(t)){const e=t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let n="#";for(let t=0;t<e.length;t++){let o=Number(e[t]).toString(16);o=1==String(o).length?`0${o}`:o,"0"===o&&(o+=o),n+=o}return 7!==n.length&&(n=t),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t))return t;{const e=t.replace(/#/,"").split("");if(6===e.length)return t;if(3===e.length){let t="#";for(let n=0;n<e.length;n+=1)t+=e[n]+e[n];return t}}}const uo={props:{show:{type:Boolean,default:!0},color:{type:String,default:"#909193"},textColor:{type:String,default:"#909193"},vertical:{type:Boolean,default:!1},mode:{type:String,default:"spinner"},size:{type:[String,Number],default:24},textSize:{type:[String,Number],default:15},textStyle:{type:Object,default:()=>({})},text:{type:[String,Number],default:""},timingFunction:{type:String,default:"linear"},duration:{type:[String,Number],default:1200},inactiveColor:{type:String,default:""},...null==(i=null==(r=uni.$uv)?void 0:r.props)?void 0:i.loadingIcon}};const ho=Ve({name:"uv-loading-icon",mixins:[X,Ee,uo],data:()=>({array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}),computed:{otherBorderColor(){const e=so(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show(e){}},mounted(){this.init()},methods:{init(){setTimeout((()=>{this.show&&this.addEventListenerToWebview()}),20)},addEventListenerToWebview(){const e=getCurrentPages(),t=e[e.length-1].$getAppWebview();t.addEventListener("hide",(()=>{this.webviewHide=!0})),t.addEventListener("show",(()=>{this.webviewHide=!1}))}}},[["render",function(t,n,o,r,i,a){return t.show?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uv-loading-icon",[t.vertical&&"uv-loading-icon--vertical"]]),style:e.normalizeStyle([t.$uv.addStyle(t.customStyle)])},[i.webviewHide?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uv-loading-icon__spinner",[`uv-loading-icon__spinner--${t.mode}`]]),ref:"ani",style:e.normalizeStyle({color:t.color,width:t.$uv.addUnit(t.size),height:t.$uv.addUnit(t.size),borderTopColor:t.color,borderBottomColor:a.otherBorderColor,borderLeftColor:a.otherBorderColor,borderRightColor:a.otherBorderColor,"animation-duration":`${t.duration}ms`,"animation-timing-function":"semicircle"===t.mode||"circle"===t.mode?t.timingFunction:""})},["spinner"===t.mode?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:0},e.renderList(i.array12,((t,n)=>(e.openBlock(),e.createElementBlock("view",{key:n,class:"uv-loading-icon__dot"})))),128)):e.createCommentVNode("",!0)],6)),t.text?(e.openBlock(),e.createElementBlock("text",{key:1,class:"uv-loading-icon__text",style:e.normalizeStyle([{fontSize:t.$uv.addUnit(t.textSize),color:t.textColor},t.$uv.addStyle(t.textStyle)])},e.toDisplayString(t.text),5)):e.createCommentVNode("",!0)],6)):e.createCommentVNode("",!0)}],["__scopeId","data-v-9b1bef37"]]),po={props:{length:{type:[String,Number],default:0},current:{type:[String,Number],default:0},indicatorActiveColor:{type:String,default:""},indicatorInactiveColor:{type:String,default:""},indicatorMode:{type:String,default:""},...null==(s=null==(a=uni.$uv)?void 0:a.props)?void 0:s.swiperIndicator}};const fo=Ve({name:"uv-swiper-indicator",mixins:[X,Ee,po],data:()=>({lineWidth:22}),computed:{lineStyle(){let e={};return e.width=this.$uv.addUnit(this.lineWidth),e.transform=`translateX(${this.$uv.addUnit(this.current*this.lineWidth)})`,e.backgroundColor=this.indicatorActiveColor,e},dotStyle(){return e=>{let t={};return t.backgroundColor=e===this.current?this.indicatorActiveColor:this.indicatorInactiveColor,t}}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"uv-swiper-indicator"},["line"===t.indicatorMode?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uv-swiper-indicator__wrapper",[`uv-swiper-indicator__wrapper--${t.indicatorMode}`]]),style:e.normalizeStyle({width:t.$uv.addUnit(i.lineWidth*t.length),backgroundColor:t.indicatorInactiveColor})},[e.createElementVNode("view",{class:"uv-swiper-indicator__wrapper--line__bar",style:e.normalizeStyle([a.lineStyle])},null,4)],6)):e.createCommentVNode("",!0),"dot"===t.indicatorMode?(e.openBlock(),e.createElementBlock("view",{key:1,class:"uv-swiper-indicator__wrapper"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.length,((n,o)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-swiper-indicator__wrapper__dot",[o===t.current&&"uv-swiper-indicator__wrapper__dot--active"]]),key:o,style:e.normalizeStyle([a.dotStyle(o)])},null,6)))),128))])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-0ed3a9fb"]]),mo={props:{list:{type:Array,default:()=>[]},indicator:{type:Boolean,default:!1},indicatorActiveColor:{type:String,default:"#fff"},indicatorInactiveColor:{type:String,default:"rgba(255, 255, 255, 0.35)"},indicatorStyle:{type:[String,Object],default:""},indicatorMode:{type:String,default:"line"},autoplay:{type:Boolean,default:!0},current:{type:[String,Number],default:0},currentItemId:{type:String,default:""},interval:{type:[String,Number],default:3e3},duration:{type:[String,Number],default:300},circular:{type:Boolean,default:!1},vertical:{type:Boolean,default:!1},previousMargin:{type:[String,Number],default:0},nextMargin:{type:[String,Number],default:0},acceleration:{type:Boolean,default:!1},displayMultipleItems:{type:Number,default:1},easingFunction:{type:String,default:"default"},keyName:{type:String,default:"url"},imgMode:{type:String,default:"aspectFill"},height:{type:[String,Number],default:130},bgColor:{type:String,default:"#f3f4f6"},radius:{type:[String,Number],default:4},loading:{type:Boolean,default:!1},showTitle:{type:Boolean,default:!1},titleStyle:{type:Object,default:()=>{}},...null==(c=null==(l=uni.$uv)?void 0:l.props)?void 0:c.swiper}},go={name:"uv-swiper",mixins:[X,Ee,mo],emits:["click","change"],data:()=>({currentIndex:0}),watch:{current(e,t){e!==t&&(this.currentIndex=e)}},computed:{itemStyle(){return e=>{const t={};return this.nextMargin&&this.previousMargin&&(t.borderRadius=this.$uv.addUnit(this.radius),e!==this.currentIndex&&(t.transform="scale(0.92)")),t}}},methods:{getItemType(e){return"string"==typeof e?this.$uv.test.video(this.getSource(e))?"video":"image":"object"==typeof e&&this.keyName?e.type?"image"===e.type?"image":"video"===e.type?"video":"image":this.$uv.test.video(this.getSource(e))?"video":"image":void 0},getSource(e){return"string"==typeof e?e:"object"==typeof e&&this.keyName?e[this.keyName]:(this.$uv.error("请按格式传递列表参数"),"")},change(e){const{current:t}=e.detail;this.pauseVideo(this.currentIndex),this.currentIndex=t,this.$emit("change",e.detail)},pauseVideo(e){const t=this.getSource(this.list[e]);if(this.$uv.test.video(t)){uni.createVideoContext(`video-${e}`,this).pause()}},getPoster:e=>"object"==typeof e&&e.poster?e.poster:"",clickHandler(e){this.$emit("click",e)}}};const yo=Ve(go,[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-loading-icon"),ho),l=ee(e.resolveDynamicComponent("uv-swiper-indicator"),fo);return e.openBlock(),e.createElementBlock("view",{class:"uv-swiper",style:e.normalizeStyle({backgroundColor:t.bgColor,height:t.$uv.addUnit(t.height),borderRadius:t.$uv.addUnit(t.radius)})},[t.loading?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-swiper__loading"},[e.createVNode(s,{mode:"circle"})])):(e.openBlock(),e.createElementBlock("swiper",{key:1,class:"uv-swiper__wrapper",style:e.normalizeStyle({height:t.$uv.addUnit(t.height),flex:1}),onChange:n[0]||(n[0]=(...e)=>a.change&&a.change(...e)),circular:t.circular,vertical:t.vertical,interval:t.interval,duration:t.duration,autoplay:t.autoplay,current:t.current,currentItemId:t.currentItemId,previousMargin:t.$uv.addUnit(t.previousMargin),nextMargin:t.$uv.addUnit(t.nextMargin),acceleration:t.acceleration,displayMultipleItems:t.displayMultipleItems,easingFunction:t.easingFunction},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.list,((n,o)=>(e.openBlock(),e.createElementBlock("swiper-item",{class:"uv-swiper__wrapper__item",key:o},[e.createElementVNode("view",{class:"uv-swiper__wrapper__item__wrapper",style:e.normalizeStyle([a.itemStyle(o)])},["image"===a.getItemType(n)?(e.openBlock(),e.createElementBlock("image",{key:0,class:"uv-swiper__wrapper__item__wrapper__image",src:a.getSource(n),mode:t.imgMode,onClick:e=>a.clickHandler(o),style:e.normalizeStyle({height:t.$uv.addUnit(t.height),borderRadius:t.$uv.addUnit(t.radius)})},null,12,["src","mode","onClick"])):e.createCommentVNode("",!0),"video"===a.getItemType(n)?(e.openBlock(),e.createElementBlock("video",{key:1,class:"uv-swiper__wrapper__item__wrapper__video",id:`video-${o}`,"enable-progress-gesture":!1,src:a.getSource(n),poster:a.getPoster(n),title:t.showTitle&&t.$uv.test.object(n)&&n.title?n.title:"",style:e.normalizeStyle({height:t.$uv.addUnit(t.height)}),controls:"",onClick:e=>a.clickHandler(o)},null,12,["id","src","poster","title","onClick"])):e.createCommentVNode("",!0),t.showTitle&&t.$uv.test.object(n)&&n.title?(e.openBlock(),e.createElementBlock("text",{key:2,class:"uv-swiper__wrapper__item__wrapper__title uv-line-1",style:e.normalizeStyle([t.$uv.addStyle(t.titleStyle)])},e.toDisplayString(n.title),5)):e.createCommentVNode("",!0)],4)])))),128))],44,["circular","vertical","interval","duration","autoplay","current","currentItemId","previousMargin","nextMargin","acceleration","displayMultipleItems","easingFunction"])),e.createElementVNode("view",{class:"uv-swiper__indicator",style:e.normalizeStyle([t.$uv.addStyle(t.indicatorStyle)])},[e.renderSlot(t.$slots,"indicator",{},(()=>[t.loading||!t.indicator||t.showTitle?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(l,{key:0,indicatorActiveColor:t.indicatorActiveColor,indicatorInactiveColor:t.indicatorInactiveColor,length:t.list.length,current:i.currentIndex,indicatorMode:t.indicatorMode},null,8,["indicatorActiveColor","indicatorInactiveColor","length","current","indicatorMode"]))]),!0)],4)],4)}],["__scopeId","data-v-d9b86076"]]),vo={__name:"home-swiper",setup(t){const n=e.reactive([{image:`${Zn.getters.allEnv[Zn.getters.envKey].baseUrl}/mobile/swiper/swiper1.jpg`},{image:`${Zn.getters.allEnv[Zn.getters.envKey].baseUrl}/mobile/swiper/swiper2.jpg`}]);return(t,o)=>{const r=ee(e.resolveDynamicComponent("uv-swiper"),yo);return e.openBlock(),e.createBlock(r,{list:n,keyName:"image",indicator:"",indicatorMode:"line",circular:""},null,8,["list"])}}};const bo=Ve({name:"tuiSection",emits:["click"],props:{title:{type:String,default:""},size:{type:[Number,String],default:32},color:{type:String,default:"#333"},fontWeight:{type:[Number,String],default:400},descr:{type:String,default:""},descrSize:{type:[Number,String],default:24},descrColor:{type:String,default:"#7a7a7a"},descrTop:{type:[Number,String],default:16},isLine:{type:Boolean,default:!1},lineWidth:{type:[Number,String],default:8},lineColor:{type:String,default:""},lineCap:{type:String,default:"circle"},lineRight:{type:[Number,String],default:16},lineGap:{type:[Number,String],default:4},background:{type:String,default:"transparent"},padding:{type:String,default:"20rpx 30rpx"},margin:{type:String,default:"0"}},computed:{getLineColor(){return this.lineColor||uni&&uni.$tui&&uni.$tui.color.primary||"#5677fc"}},methods:{handleClick(){this.$emit("click",{title:this.title})}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"tui-section",style:e.normalizeStyle({margin:o.margin,background:o.background,padding:o.padding})},[e.createElementVNode("view",{class:"tui-section__title",onClick:n[0]||(n[0]=(...e)=>a.handleClick&&a.handleClick(...e))},[o.isLine?(e.openBlock(),e.createElementBlock("view",{key:0,class:"tui-section__decorate",style:e.normalizeStyle({background:a.getLineColor,width:o.lineWidth+"rpx",left:`-${o.lineRight}rpx`,top:o.lineGap+"rpx",bottom:o.lineGap+"rpx",borderRadius:"circle"===o.lineCap?`${o.lineWidth}rpx`:0})},null,4)):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"left",{},void 0,!0),o.title?(e.openBlock(),e.createElementBlock("text",{key:1,style:e.normalizeStyle({fontSize:o.size+"rpx",color:o.color,fontWeight:o.fontWeight})},e.toDisplayString(o.title),5)):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"default",{},void 0,!0)]),o.descr?(e.openBlock(),e.createElementBlock("view",{key:0,class:"tui-section__sub",style:e.normalizeStyle({paddingTop:o.descrTop+"rpx"})},[e.createElementVNode("text",{class:"tui-section__descr",style:e.normalizeStyle({fontSize:o.descrSize+"rpx",color:o.descrColor})},e.toDisplayString(o.descr),5)],4)):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"descr",{},void 0,!0)],4)}],["__scopeId","data-v-4adff773"]]),wo={pages:[{path:"pages/login",style:{navigationBarTitleText:"登录",enablePullDownRefresh:!1}},{path:"pages/home/<USER>",style:{navigationBarTitleText:"小诺移动端框架"}},{path:"pages/work/index",style:{navigationBarTitleText:"工作台"}},{path:"pages/msg/index",style:{navigationBarTitleText:"消息",enablePullDownRefresh:!0,onReachBottomDistance:50}},{path:"pages/msg/detail",style:{navigationBarTitleText:"消息详情"}},{path:"pages/mine/index",style:{navigationBarTitleText:"我的"}}],subPackages:[{root:"pages/config",pages:[{path:"index",style:{navigationBarTitleText:"环境设置",enablePullDownRefresh:!1}},{path:"form",style:{navigationBarTitleText:"环境管理",enablePullDownRefresh:!1}}]},{root:"pages/common",pages:[{path:"webview/index",style:{navigationBarTitleText:"浏览网页",enablePullDownRefresh:!1}}]},{root:"pages/biz/org",pages:[{path:"index",style:{navigationBarTitleText:"机构管理",enablePullDownRefresh:!1}},{path:"form",style:{navigationBarTitleText:"机构管理",enablePullDownRefresh:!1}}]},{root:"pages/biz/position",pages:[{path:"index",style:{navigationBarTitleText:"职位管理",enablePullDownRefresh:!0}},{path:"form",style:{navigationBarTitleText:"职位管理",enablePullDownRefresh:!1}}]},{root:"pages/biz/user",pages:[{path:"index",style:{navigationBarTitleText:"用户管理",enablePullDownRefresh:!0,onReachBottomDistance:50}},{path:"form",style:{navigationBarTitleText:"用户管理",enablePullDownRefresh:!1,onReachBottomDistance:50}}]},{root:"pages/mine/info",pages:[{path:"index",style:{navigationBarTitleText:"个人信息",enablePullDownRefresh:!1}},{path:"edit",style:{navigationBarTitleText:"编辑资料",enablePullDownRefresh:!1}}]},{root:"pages/mine/home-config",pages:[{path:"index",style:{navigationBarTitleText:"首页设置",enablePullDownRefresh:!1}}]},{root:"pages/mine/pwd",pages:[{path:"index",style:{navigationBarTitleText:"修改密码",enablePullDownRefresh:!1}}]}],tabBar:{color:"#000000",selectedColor:"#000000",borderStyle:"white",backgroundColor:"#ffffff",list:[{pagePath:"pages/home/<USER>",iconPath:"static/images/tabbar/home.png",selectedIconPath:"static/images/tabbar/home_.png",text:"首页"},{pagePath:"pages/work/index",iconPath:"static/images/tabbar/work.png",selectedIconPath:"static/images/tabbar/work_.png",text:"工作台"},{pagePath:"pages/msg/index",iconPath:"static/images/tabbar/msg.png",selectedIconPath:"static/images/tabbar/msg_.png",text:"消息"},{pagePath:"pages/mine/index",iconPath:"static/images/tabbar/mine.png",selectedIconPath:"static/images/tabbar/mine_.png",text:"我的"}]},easycom:{autoscan:!0,custom:{"tui-(.*)":"@/components/thorui/thorui/tui-$1/tui-$1.vue","snowy-(.*)":"@/components/snowy/snowy-$1/snowy-$1.vue"}},globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},uniIdRouter:{}};function ko(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var xo=ko((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),o={},r=o.lib={},i=r.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=r.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,n=e.words,o=this.sigBytes,r=e.sigBytes;if(this.clamp(),o%4)for(var i=0;i<r;i++){var a=n[i>>>2]>>>24-i%4*8&255;t[o+i>>>2]|=a<<24-(o+i)%4*8}else for(i=0;i<r;i+=4)t[o+i>>>2]=n[i>>>2];return this.sigBytes+=r,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,o=[],r=function(t){var n=987654321,o=4294967295;return function(){var r=((n=36969*(65535&n)+(n>>16)&o)<<16)+(t=18e3*(65535&t)+(t>>16)&o)&o;return r/=4294967296,(r+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var s=r(4294967296*(n||e.random()));n=987654071*s(),o.push(4294967296*s()|0)}return new a.init(o,t)}}),s=o.enc={},l=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;o.push((i>>>4).toString(16)),o.push((15&i).toString(16))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o+=2)n[o>>>3]|=parseInt(e.substr(o,2),16)<<24-o%8*4;return new a.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,o=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;o.push(String.fromCharCode(i))}return o.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o++)n[o>>>2]|=(255&e.charCodeAt(o))<<24-o%4*8;return new a.init(n,t)}},u=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},d=r.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=u.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,o=n.words,r=n.sigBytes,i=this.blockSize,s=r/(4*i),l=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,c=e.min(4*l,r);if(l){for(var u=0;u<l;u+=i)this._doProcessBlock(o,u);var d=o.splice(0,l);n.sigBytes-=c}return new a.init(d,c)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=d.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=o.algo={};return o}(Math),n)})),So=xo,Co=(ko((function(e,t){var n;e.exports=(n=So,function(e){var t=n,o=t.lib,r=o.WordArray,i=o.Hasher,a=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var l=a.MD5=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var o=t+n,r=e[o];e[o]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var i=this._hash.words,a=e[t+0],l=e[t+1],p=e[t+2],f=e[t+3],m=e[t+4],g=e[t+5],y=e[t+6],v=e[t+7],b=e[t+8],w=e[t+9],k=e[t+10],x=e[t+11],S=e[t+12],C=e[t+13],_=e[t+14],N=e[t+15],D=i[0],E=i[1],T=i[2],B=i[3];D=c(D,E,T,B,a,7,s[0]),B=c(B,D,E,T,l,12,s[1]),T=c(T,B,D,E,p,17,s[2]),E=c(E,T,B,D,f,22,s[3]),D=c(D,E,T,B,m,7,s[4]),B=c(B,D,E,T,g,12,s[5]),T=c(T,B,D,E,y,17,s[6]),E=c(E,T,B,D,v,22,s[7]),D=c(D,E,T,B,b,7,s[8]),B=c(B,D,E,T,w,12,s[9]),T=c(T,B,D,E,k,17,s[10]),E=c(E,T,B,D,x,22,s[11]),D=c(D,E,T,B,S,7,s[12]),B=c(B,D,E,T,C,12,s[13]),T=c(T,B,D,E,_,17,s[14]),D=u(D,E=c(E,T,B,D,N,22,s[15]),T,B,l,5,s[16]),B=u(B,D,E,T,y,9,s[17]),T=u(T,B,D,E,x,14,s[18]),E=u(E,T,B,D,a,20,s[19]),D=u(D,E,T,B,g,5,s[20]),B=u(B,D,E,T,k,9,s[21]),T=u(T,B,D,E,N,14,s[22]),E=u(E,T,B,D,m,20,s[23]),D=u(D,E,T,B,w,5,s[24]),B=u(B,D,E,T,_,9,s[25]),T=u(T,B,D,E,f,14,s[26]),E=u(E,T,B,D,b,20,s[27]),D=u(D,E,T,B,C,5,s[28]),B=u(B,D,E,T,p,9,s[29]),T=u(T,B,D,E,v,14,s[30]),D=d(D,E=u(E,T,B,D,S,20,s[31]),T,B,g,4,s[32]),B=d(B,D,E,T,b,11,s[33]),T=d(T,B,D,E,x,16,s[34]),E=d(E,T,B,D,_,23,s[35]),D=d(D,E,T,B,l,4,s[36]),B=d(B,D,E,T,m,11,s[37]),T=d(T,B,D,E,v,16,s[38]),E=d(E,T,B,D,k,23,s[39]),D=d(D,E,T,B,C,4,s[40]),B=d(B,D,E,T,a,11,s[41]),T=d(T,B,D,E,f,16,s[42]),E=d(E,T,B,D,y,23,s[43]),D=d(D,E,T,B,w,4,s[44]),B=d(B,D,E,T,S,11,s[45]),T=d(T,B,D,E,N,16,s[46]),D=h(D,E=d(E,T,B,D,p,23,s[47]),T,B,a,6,s[48]),B=h(B,D,E,T,v,10,s[49]),T=h(T,B,D,E,_,15,s[50]),E=h(E,T,B,D,g,21,s[51]),D=h(D,E,T,B,S,6,s[52]),B=h(B,D,E,T,f,10,s[53]),T=h(T,B,D,E,k,15,s[54]),E=h(E,T,B,D,l,21,s[55]),D=h(D,E,T,B,b,6,s[56]),B=h(B,D,E,T,N,10,s[57]),T=h(T,B,D,E,y,15,s[58]),E=h(E,T,B,D,C,21,s[59]),D=h(D,E,T,B,m,6,s[60]),B=h(B,D,E,T,x,10,s[61]),T=h(T,B,D,E,p,15,s[62]),E=h(E,T,B,D,w,21,s[63]),i[0]=i[0]+D|0,i[1]=i[1]+E|0,i[2]=i[2]+T|0,i[3]=i[3]+B|0},_doFinalize:function(){var t=this._data,n=t.words,o=8*this._nDataBytes,r=8*t.sigBytes;n[r>>>5]|=128<<24-r%32;var i=e.floor(o/4294967296),a=o;n[15+(r+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(r+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,l=s.words,c=0;c<4;c++){var u=l[c];l[c]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,o,r,i,a){var s=e+(t&n|~t&o)+r+a;return(s<<i|s>>>32-i)+t}function u(e,t,n,o,r,i,a){var s=e+(t&o|n&~o)+r+a;return(s<<i|s>>>32-i)+t}function d(e,t,n,o,r,i,a){var s=e+(t^n^o)+r+a;return(s<<i|s>>>32-i)+t}function h(e,t,n,o,r,i,a){var s=e+(n^(t|~o))+r+a;return(s<<i|s>>>32-i)+t}t.MD5=i._createHelper(l),t.HmacMD5=i._createHmacHelper(l)}(Math),n.MD5)})),ko((function(e,t){var n,o,r;e.exports=(o=(n=So).lib.Base,r=n.enc.Utf8,void(n.algo.HMAC=o.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),a=this._iKey=t.clone(),s=i.words,l=a.words,c=0;c<n;c++)s[c]^=1549556828,l[c]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),ko((function(e,t){e.exports=So.HmacMD5}))),_o=ko((function(e,t){e.exports=So.enc.Utf8})),No=ko((function(e,t){var n,o,r;e.exports=(r=(o=n=So).lib.WordArray,o.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,o=this._map;e.clamp();for(var r=[],i=0;i<n;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)r.push(o.charAt(a>>>6*(3-s)&63));var l=o.charAt(64);if(l)for(;r.length%4;)r.push(l);return r.join("")},parse:function(e){var t=e.length,n=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<n.length;i++)o[n.charCodeAt(i)]=i}var a=n.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return function(e,t,n){for(var o=[],i=0,a=0;a<t;a++)if(a%4){var s=n[e.charCodeAt(a-1)]<<a%4*2,l=n[e.charCodeAt(a)]>>>6-a%4*2;o[i>>>2]|=(s|l)<<24-i%4*8,i++}return r.create(o,i)}(e,t,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},n.enc.Base64)}));const Do="FUNCTION",Eo="pending",To="rejected";function Bo(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function Vo(e){return"object"===Bo(e)}function Io(e){return"function"==typeof e}function Ao(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const Po="REJECTED",$o="NOT_PENDING";class Oo{constructor({createPromise:e,retryRule:t=Po}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case Po:return this.status===To;case $o:return this.status!==Eo}}exec(){return this.needRetry?(this.status=Eo,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=To,Promise.reject(e)))),this.promise):this.promise}}function Fo(e){return e&&"string"==typeof e?JSON.parse(e):e}const Mo=Fo([]);Fo("");const Lo=Fo("[]")||[];let zo="";try{zo="__UNI__EF8711B"}catch(Er){}let Ro={};function Uo(e,t={}){var n,o;return n=Ro,o=e,Object.prototype.hasOwnProperty.call(n,o)||(Ro[e]=t),Ro[e]}Ro=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={};const qo=["invoke","success","fail","complete"],jo=Uo("_globalUniCloudInterceptor");function Ho(e,t){jo[e]||(jo[e]={}),Vo(t)&&Object.keys(t).forEach((n=>{qo.indexOf(n)>-1&&function(e,t,n){let o=jo[e][t];o||(o=jo[e][t]=[]),-1===o.indexOf(n)&&Io(n)&&o.push(n)}(e,n,t[n])}))}function Wo(e,t){jo[e]||(jo[e]={}),Vo(t)?Object.keys(t).forEach((n=>{qo.indexOf(n)>-1&&function(e,t,n){const o=jo[e][t];if(!o)return;const r=o.indexOf(n);r>-1&&o.splice(r,1)}(e,n,t[n])})):delete jo[e]}function Ko(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function Yo(e,t){return jo[e]&&jo[e][t]||[]}function Go(e){Ho("callObject",e)}const Jo=Uo("_globalUniCloudListener"),Zo="response",Xo="needLogin",Qo="refreshToken",er="clientdb",tr="cloudfunction",nr="cloudobject";function or(e){return Jo[e]||(Jo[e]=[]),Jo[e]}function rr(e,t){const n=or(e);n.includes(t)||n.push(t)}function ir(e,t){const n=or(e),o=n.indexOf(t);-1!==o&&n.splice(o,1)}function ar(e,t){const n=or(e);for(let o=0;o<n.length;o++)(0,n[o])(t)}let sr,lr=!1;function cr(){return sr||(sr=new Promise((e=>{lr&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(lr=!0,e())}lr||setTimeout((()=>{t()}),30)}()})),sr)}function ur(e){const t={};for(const n in e){const o=e[n];Io(o)&&(t[n]=Ao(o))}return t}class dr extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var hr={request:e=>uni.request(e),uploadFile:e=>uni.uploadFile(e),setStorageSync:(e,t)=>uni.setStorageSync(e,t),getStorageSync:e=>uni.getStorageSync(e),removeStorageSync:e=>uni.removeStorageSync(e),clearStorageSync:()=>uni.clearStorageSync()};function pr(e){return e&&pr(e.__v_raw)||e}function fr(){return{token:hr.getStorageSync("uni_id_token")||hr.getStorageSync("uniIdToken"),tokenExpired:hr.getStorageSync("uni_id_token_expired")}}function mr({token:e,tokenExpired:t}={}){e&&hr.setStorageSync("uni_id_token",e),t&&hr.setStorageSync("uni_id_token_expired",t)}let gr,yr;function vr(){return gr||(gr=uni.getSystemInfoSync()),gr}function br(){let e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:o}=uni.getLaunchOptionsSync();e=o,t=n}}catch(n){}return{channel:e,scene:t}}function wr(){const e=uni.getLocale&&uni.getLocale()||"en";if(yr)return{...yr,locale:e,LOCALE:e};const t=vr(),{deviceId:n,osName:o,uniPlatform:r,appId:i}=t,a=["pixelRatio","brand","model","system","language","version","platform","host","SDKVersion","swanNativeVersion","app","AppPlatform","fontSizeSetting"];for(let s=0;s<a.length;s++)delete t[a[s]];return yr={PLATFORM:r,OS:o,APPID:i,DEVICEID:n,...br(),...t},{...yr,locale:e,LOCALE:e}}var kr=function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),Co(n,t).toString()},xr=function(e,t){return new Promise(((n,o)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400)return o(new dr({code:"SYS_ERR",message:e.errMsg||"request:fail",requestId:t}));const r=e.data;if(r.error)return o(new dr({code:r.error.code,message:r.error.message,requestId:t}));r.result=r.data,r.requestId=t,delete r.data,n(r)}}))}))},Sr=function(e){return No.stringify(_o.parse(e))},Cr=class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=hr,this._getAccessTokenPromiseHub=new Oo({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new dr({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:$o})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return xr(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=kr(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),o={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,o["x-basement-token"]=this.accessToken),o["x-serverless-sign"]=kr(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:o}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(this.setupRequest(t))}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:o,fileType:r,onUploadProgress:i}){return new Promise(((a,s)=>{const l=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:o,fileType:r,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?a(e):s(new dr({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){s(new dr({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((e=>{i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:o=!1,onUploadProgress:r,config:i}){if("string"!==Bo(t))throw new dr({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new dr({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new dr({code:"INVALID_PARAM",message:"cloudPath不合法"});const a=i&&i.envType||this.config.envType;if(o&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new dr({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const s=(await this.getOSSUploadOptionsFromPath({env:a,filename:o?t.split("/").pop():t,fileId:o?t:void 0})).result,l="https://"+s.cdnDomain+"/"+s.ossPath,{securityToken:c,accessKeyId:u,signature:d,host:h,ossPath:p,id:f,policy:m,ossCallbackUrl:g}=s,y={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:u,Signature:d,host:h,id:f,key:p,policy:m,success_action_status:200};if(c&&(y["x-oss-security-token"]=c),g){const e=JSON.stringify({callbackUrl:g,callbackBody:JSON.stringify({fileId:f,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});y.callback=Sr(e)}const v={url:"https://"+s.host,formData:y,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},v,{onUploadProgress:r})),g)return{success:!0,filePath:e,fileID:l};if((await this.reportOSSUpload({id:f})).success)return{success:!0,filePath:e,fileID:l};throw new dr({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new dr({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new dr({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}},_r={init(e){const t=new Cr(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const Nr="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var Dr,Er;(Er=Dr||(Dr={})).local="local",Er.none="none",Er.session="session";var Tr=function(){},Br=ko((function(e,t){var n;e.exports=(n=So,function(e){var t=n,o=t.lib,r=o.WordArray,i=o.Hasher,a=t.algo,s=[],l=[];!function(){function t(t){for(var n=e.sqrt(t),o=2;o<=n;o++)if(!(t%o))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var o=2,r=0;r<64;)t(o)&&(r<8&&(s[r]=n(e.pow(o,.5))),l[r]=n(e.pow(o,1/3)),r++),o++}();var c=[],u=a.SHA256=i.extend({_doReset:function(){this._hash=new r.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],r=n[1],i=n[2],a=n[3],s=n[4],u=n[5],d=n[6],h=n[7],p=0;p<64;p++){if(p<16)c[p]=0|e[t+p];else{var f=c[p-15],m=(f<<25|f>>>7)^(f<<14|f>>>18)^f>>>3,g=c[p-2],y=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;c[p]=m+c[p-7]+y+c[p-16]}var v=o&r^o&i^r&i,b=(o<<30|o>>>2)^(o<<19|o>>>13)^(o<<10|o>>>22),w=h+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&u^~s&d)+l[p]+c[p];h=d,d=u,u=s,s=a+w|0,a=i,i=r,r=o,o=w+(b+v)|0}n[0]=n[0]+o|0,n[1]=n[1]+r|0,n[2]=n[2]+i|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0,n[5]=n[5]+u|0,n[6]=n[6]+d|0,n[7]=n[7]+h|0},_doFinalize:function(){var t=this._data,n=t.words,o=8*this._nDataBytes,r=8*t.sigBytes;return n[r>>>5]|=128<<24-r%32,n[14+(r+64>>>9<<4)]=e.floor(o/4294967296),n[15+(r+64>>>9<<4)]=o,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(u),t.HmacSHA256=i._createHmacHelper(u)}(Math),n.SHA256)})),Vr=Br,Ir=ko((function(e,t){e.exports=So.HmacSHA256}));const Ar=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new dr({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,o)=>e?n(e):t(o)}));return e.promise=t,e};function Pr(e){return void 0===e}function $r(e){return"[object Null]"===Object.prototype.toString.call(e)}var Or;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Or||(Or={}));const Fr={adapter:null,runtime:void 0},Mr=["anonymousUuidKey"];class Lr extends Tr{constructor(){super(),Fr.adapter.root.tcbObject||(Fr.adapter.root.tcbObject={})}setItem(e,t){Fr.adapter.root.tcbObject[e]=t}getItem(e){return Fr.adapter.root.tcbObject[e]}removeItem(e){delete Fr.adapter.root.tcbObject[e]}clear(){delete Fr.adapter.root.tcbObject}}function zr(e,t){switch(e){case"local":return t.localStorage||new Lr;case"none":return new Lr;default:return t.sessionStorage||new Lr}}class Rr{constructor(e){if(!this._storage){this._persistence=Fr.adapter.primaryStorage||e.persistence,this._storage=zr(this._persistence,Fr.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,o=`refresh_token_${e.env}`,r=`anonymous_uuid_${e.env}`,i=`login_type_${e.env}`,a=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o,anonymousUuidKey:r,loginTypeKey:i,userInfoKey:a}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=zr(e,Fr.adapter);for(const o in this.keys){const e=this.keys[o];if(t&&Mr.includes(o))continue;const r=this._storage.getItem(e);Pr(r)||$r(r)||(n.setItem(e,r),this._storage.removeItem(e))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const o={version:n||"localCachev1",content:t},r=JSON.stringify(o);try{this._storage.setItem(e,r)}catch(i){throw i}}getStore(e,t){try{if(!this._storage)return}catch(o){return""}t=t||"localCachev1";const n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}removeStore(e){this._storage.removeItem(e)}}const Ur={},qr={};function jr(e){return Ur[e]}class Hr{constructor(e,t){this.data=t||null,this.name=e}}class Wr extends Hr{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const Kr=new class{constructor(){this._listeners={}}on(e,t){return n=e,o=t,(r=this._listeners)[n]=r[n]||[],r[n].push(o),this;var n,o,r}off(e,t){return function(e,t,n){if(n&&n[e]){const o=n[e].indexOf(t);-1!==o&&n[e].splice(o,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Wr)return console.error(e.error),this;const n="string"==typeof e?new Hr(e,t||{}):e,o=n.name;if(this._listens(o)){n.target=this;const e=this._listeners[o]?[...this._listeners[o]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function Yr(e,t){Kr.on(e,t)}function Gr(e,t={}){Kr.fire(e,t)}function Jr(e,t){Kr.off(e,t)}const Zr="loginStateChanged",Xr="loginStateExpire",Qr="loginTypeChanged",ei="anonymousConverted",ti="refreshAccessToken";var ni;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(ni||(ni={}));const oi=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],ri={"X-SDK-Version":"1.3.5"};function ii(e,t,n){const o=e[t];e[t]=function(t){const r={},i={};n.forEach((n=>{const{data:o,headers:a}=n.call(e,t);Object.assign(r,o),Object.assign(i,a)}));const a=t.data;return a&&(()=>{var e;if(e=a,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...a,...r};else for(const t in r)a.append(t,r[t])})(),t.headers={...t.headers||{},...i},o.call(e,t)}}function ai(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...ri,"x-seqid":e}}}class si{constructor(e={}){var t;this.config=e,this._reqClass=new Fr.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=jr(this.config.env),this._localCache=(t=this.config.env,qr[t]),ii(this._reqClass,"post",[ai]),ii(this._reqClass,"upload",[ai]),ii(this._reqClass,"download",[ai])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(n){t=n}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:o,anonymousUuidKey:r}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let i=this._cache.getStore(n);if(!i)throw new dr({message:"未登录CloudBase"});const a={refresh_token:i},s=await this.request("auth.fetchAccessTokenWithRefreshToken",a);if(s.data.code){const{code:e}=s.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(o)===ni.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(r),t=this._cache.getStore(n),o=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(o.refresh_token),this._refreshAccessToken()}Gr(Xr),this._cache.removeStore(n)}throw new dr({code:s.data.code,message:`刷新access token失败：${s.data.code}`})}if(s.data.access_token)return Gr(ti),this._cache.setStore(e,s.data.access_token),this._cache.setStore(t,s.data.access_token_expire+Date.now()),{accessToken:s.data.access_token,accessTokenExpire:s.data.access_token_expire};s.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,s.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new dr({message:"refresh token不存在，登录状态异常"});let o=this._cache.getStore(e),r=this._cache.getStore(t),i=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(o,r))&&(i=!1),(!o||!r||r<Date.now())&&i?this.refreshAccessToken():{accessToken:o,accessTokenExpire:r}}async request(e,t,n){const o=`x-tcb-trace_${this.config.env}`;let r="application/x-www-form-urlencoded";const i={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};if(-1===oi.indexOf(e)){const{refreshTokenKey:e}=this._cache.keys;this._cache.getStore(e)&&(i.access_token=(await this.getAccessToken()).accessToken)}let a;if("storage.uploadFile"===e){a=new FormData;for(let e in a)a.hasOwnProperty(e)&&void 0!==a[e]&&a.append(e,i[e]);r="multipart/form-data"}else{r="application/json",a={};for(let e in i)void 0!==i[e]&&(a[e]=i[e])}let s={headers:{"content-type":r}};n&&n.onUploadProgress&&(s.onUploadProgress=n.onUploadProgress);const l=this._localCache.getStore(o);l&&(s.headers["X-TCB-Trace"]=l);const{parse:c,inQuery:u,search:d}=t;let h={env:this.config.env};c&&(h.parse=!0),u&&(h={...u,...h});let p=function(e,t,n={}){const o=/\?/.test(t);let r="";for(let i in n)""===r?!o&&(t+="?"):r+="&",r+=`${i}=${encodeURIComponent(n[i])}`;return/^http(s)?\:\/\//.test(t+=r)?t:`${e}${t}`}(Nr,"//tcb-api.tencentcloudapi.com/web",h);d&&(p+=d);const f=await this.post({url:p,data:a,...s}),m=f.header&&f.header["x-tcb-trace"];if(m&&this._localCache.setStore(o,m),200!==Number(f.status)&&200!==Number(f.statusCode)||!f.data)throw new dr({code:"NETWORK_ERROR",message:"network request error"});return f}async send(e,t={}){const n=await this.request(e,t,{onUploadProgress:t.onUploadProgress});if("ACCESS_TOKEN_EXPIRED"===n.data.code&&-1===oi.indexOf(e)){await this.refreshAccessToken();const n=await this.request(e,t,{onUploadProgress:t.onUploadProgress});if(n.data.code)throw new dr({code:n.data.code,message:n.data.message});return n.data}if(n.data.code)throw new dr({code:n.data.code,message:n.data.message});return n.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(o,e)}}const li={};function ci(e){return li[e]}class ui{constructor(e){this.config=e,this._cache=jr(e.env),this._request=ci(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(o,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:o}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(o,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class di{constructor(e){if(!e)throw new dr({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=jr(this._envId),this._request=ci(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new dr({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new dr({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:o,province:r,country:i,city:a}=e,{data:s}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:o,province:r,country:i,city:a});this.setLocalUserInfo(s)}async refresh(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class hi{constructor(e){if(!e)throw new dr({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=jr(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:o}=this._cache.keys,r=this._cache.getStore(t),i=this._cache.getStore(n),a=this._cache.getStore(o);this.credential={refreshToken:r,accessToken:i,accessTokenExpire:a},this.user=new di(e)}get isAnonymousAuth(){return this.loginType===ni.ANONYMOUS}get isCustomAuth(){return this.loginType===ni.CUSTOM}get isWeixinAuth(){return this.loginType===ni.WECHAT||this.loginType===ni.WECHAT_OPEN||this.loginType===ni.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class pi extends ui{async signIn(){this._cache.updatePersistence("local");const{anonymousUuidKey:e,refreshTokenKey:t}=this._cache.keys,n=this._cache.getStore(e)||void 0,o=this._cache.getStore(t)||void 0,r=await this._request.send("auth.signInAnonymously",{anonymous_uuid:n,refresh_token:o});if(r.uuid&&r.refresh_token){this._setAnonymousUUID(r.uuid),this.setRefreshToken(r.refresh_token),await this._request.refreshAccessToken(),Gr(Zr),Gr(Qr,{env:this.config.env,loginType:ni.ANONYMOUS,persistence:"local"});const e=new hi(this.config.env);return await e.user.refresh(),e}throw new dr({message:"匿名登录失败"})}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,o=this._cache.getStore(t),r=this._cache.getStore(n),i=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:o,refresh_token:r,ticket:e});if(i.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(i.refresh_token),await this._request.refreshAccessToken(),Gr(ei,{env:this.config.env}),Gr(Qr,{loginType:ni.CUSTOM,persistence:"local"}),{credential:{refreshToken:i.refresh_token}};throw new dr({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,ni.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class fi extends ui{async signIn(e){if("string"!=typeof e)throw new dr({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),Gr(Zr),Gr(Qr,{env:this.config.env,loginType:ni.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new hi(this.config.env);throw new dr({message:"自定义登录失败"})}}class mi extends ui{async signIn(e,t){if("string"!=typeof e)throw new dr({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,o=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:r,access_token:i,access_token_expire:a}=o;if(r)return this.setRefreshToken(r),i&&a?this.setAccessToken(i,a):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Gr(Zr),Gr(Qr,{env:this.config.env,loginType:ni.EMAIL,persistence:this.config.persistence}),new hi(this.config.env);throw o.code?new dr({code:o.code,message:`邮箱登录失败: ${o.message}`}):new dr({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class gi extends ui{async signIn(e,t){if("string"!=typeof e)throw new dr({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,o=await this._request.send("auth.signIn",{loginType:ni.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:r,access_token_expire:i,access_token:a}=o;if(r)return this.setRefreshToken(r),a&&i?this.setAccessToken(a,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Gr(Zr),Gr(Qr,{env:this.config.env,loginType:ni.USERNAME,persistence:this.config.persistence}),new hi(this.config.env);throw o.code?new dr({code:o.code,message:`用户名密码登录失败: ${o.message}`}):new dr({message:"用户名密码登录失败"})}}class yi{constructor(e){this.config=e,this._cache=jr(e.env),this._request=ci(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Yr(Qr,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new pi(this.config)}customAuthProvider(){return new fi(this.config)}emailAuthProvider(){return new mi(this.config)}usernameAuthProvider(){return new gi(this.config)}async signInAnonymously(){return new pi(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new mi(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new gi(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new pi(this.config)),Yr(ei,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===ni.ANONYMOUS)throw new dr({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,o=this._cache.getStore(e);if(!o)return;const r=await this._request.send("auth.logout",{refresh_token:o});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),Gr(Zr),Gr(Qr,{env:this.config.env,loginType:ni.NULL,persistence:this.config.persistence}),r}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){Yr(Zr,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){Yr(Xr,e.bind(this))}onAccessTokenRefreshed(e){Yr(ti,e.bind(this))}onAnonymousConverted(e){Yr(ei,e.bind(this))}onLoginTypeChanged(e){Yr(Qr,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{refreshTokenKey:e}=this._cache.keys;return this._cache.getStore(e)?new hi(this.config.env):null}async isUsernameRegistered(e){if("string"!=typeof e)throw new dr({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new fi(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:o}=e.data;o===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const vi=function(e,t){t=t||Ar();const n=ci(this.config.env),{cloudPath:o,filePath:r,onUploadProgress:i,fileType:a="image"}=e;return n.send("storage.getUploadMetadata",{path:o}).then((e=>{const{data:{url:s,authorization:l,token:c,fileId:u,cosFileId:d},requestId:h}=e,p={key:o,signature:l,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":c};n.upload({url:s,data:p,file:r,name:o,fileType:a,onUploadProgress:i}).then((e=>{201===e.statusCode?t(null,{fileID:u,requestId:h}):t(new dr({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},bi=function(e,t){t=t||Ar();const n=ci(this.config.env),{cloudPath:o}=e;return n.send("storage.getUploadMetadata",{path:o}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},wi=function({fileList:e},t){if(t=t||Ar(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let o of e)if(!o||"string"!=typeof o)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return ci(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},ki=function({fileList:e},t){t=t||Ar(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let r of e)"object"==typeof r?(r.hasOwnProperty("fileID")&&r.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:r.fileID,max_age:r.maxAge})):"string"==typeof r?n.push({fileid:r}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const o={file_list:n};return ci(this.config.env).send("storage.batchGetDownloadUrl",o).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},xi=async function({fileID:e},t){const n=(await ki.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const o=ci(this.config.env);let r=n.download_url;if(r=encodeURI(r),!t)return o.download({url:r});t(await o.download({url:r}))},Si=function({name:e,data:t,query:n,parse:o,search:r},i){const a=i||Ar();let s;try{s=t?JSON.stringify(t):""}catch(c){return Promise.reject(c)}if(!e)return Promise.reject(new dr({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:n,parse:o,search:r,function_name:e,request_data:s};return ci(this.config.env).send("functions.invokeFunction",l).then((e=>{if(e.code)a(null,e);else{let n=e.data.response_data;if(o)a(null,{result:n,requestId:e.requestId});else try{n=JSON.parse(e.data.response_data),a(null,{result:n,requestId:e.requestId})}catch(t){a(new dr({message:"response data must be json"}))}}return a.promise})).catch((e=>{a(e)})),a.promise},Ci={timeout:15e3,persistence:"session"},_i={};class Ni{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(Fr.adapter||(this.requestClient=new Fr.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Ci,...e},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Ni(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||Fr.adapter.primaryStorage||Ci.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;Ur[t]=new Rr(e),qr[t]=new Rr({...e,persistence:"local"})}(this.config),n=this.config,li[n.env]=new si(n),this.authObj=new yi(this.config),this.authObj}on(e,t){return Yr.apply(this,[e,t])}off(e,t){return Jr.apply(this,[e,t])}callFunction(e,t){return Si.apply(this,[e,t])}deleteFile(e,t){return wi.apply(this,[e,t])}getTempFileURL(e,t){return ki.apply(this,[e,t])}downloadFile(e,t){return xi.apply(this,[e,t])}uploadFile(e,t){return vi.apply(this,[e,t])}getUploadMetadata(e,t){return bi.apply(this,[e,t])}registerExtension(e){_i[e.name]=e}async invokeExtension(e,t){const n=_i[e];if(!n)throw new dr({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=function(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const o of t){const{isMatch:e,genAdapter:t,runtime:n}=o;if(e())return{adapter:t(),runtime:n}}}(e)||{};t&&(Fr.adapter=t),n&&(Fr.runtime=n)}}var Di=new Ni;function Ei(e,t,n){void 0===n&&(n={});var o=/\?/.test(t),r="";for(var i in n)""===r?!o&&(t+="?"):r+="&",r+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=r)?t:""+e+t}class Ti{post(e){const{url:t,data:n,headers:o}=e;return new Promise(((e,r)=>{hr.request({url:Ei("https:",t),data:n,method:"POST",header:o,success(t){e(t)},fail(e){r(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:o,file:r,data:i,headers:a,fileType:s}=e,l=hr.uploadFile({url:Ei("https:",o),name:"file",formData:Object.assign({},i),filePath:r,fileType:s,header:a,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const Bi={setItem(e,t){hr.setStorageSync(e,t)},getItem:e=>hr.getStorageSync(e),removeItem(e){hr.removeStorageSync(e)},clear(){hr.clearStorageSync()}};var Vi={genAdapter:function(){return{root:{},reqClass:Ti,localStorage:Bi,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Di.useAdapters(Vi);const Ii=Di,Ai=Ii.init;Ii.init=function(e){e.env=e.spaceId;const t=Ai.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:o,complete:r}=ur(e);if(!(t||o||r))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),r&&r(e)}),(e=>{o&&o(e),r&&r(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Pi=Ii,$i=class extends Cr{getAccessToken(){return new Promise(((e,t)=>{const n="Anonymous_Access_token";this.setAccessToken(n),e(n)}))}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),o={"Content-Type":"application/json"};"auth"!==t&&(n.token=this.accessToken,o["x-basement-token"]=this.accessToken),o["x-serverless-sign"]=kr(n,this.config.clientSecret);const r=wr();o["x-client-info"]=encodeURIComponent(JSON.stringify(r));const{token:i}=fr();return o["x-client-token"]=i,{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:JSON.parse(JSON.stringify(o))}}uploadFileToOSS({url:e,formData:t,name:n,filePath:o,fileType:r,onUploadProgress:i}){return new Promise(((a,s)=>{const l=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:o,fileType:r,success(e){e&&e.statusCode<400?a(e):s(new dr({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){s(new dr({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((e=>{i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:o}){if(!t)throw new dr({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let r;return this.getOSSUploadOptionsFromPath({cloudPath:t}).then((t=>{const{url:i,formData:a,name:s}=t.result;r=t.result.fileUrl;const l={url:i,formData:a,name:s,filePath:e,fileType:n};return this.uploadFileToOSS(Object.assign({},l,{onUploadProgress:o}))})).then((()=>this.reportOSSUpload({cloudPath:t}))).then((t=>new Promise(((n,o)=>{t.success?n({success:!0,filePath:e,fileID:r}):o(new dr({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(this.setupRequest(t)).then((e=>{if(e.success)return e.result;throw new dr({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new dr({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(this.setupRequest(n)).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new dr({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}},Oi={init(e){const t=new $i(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Fi=ko((function(e,t){e.exports=So.enc.Hex}));function Mi(e="",t={}){const{data:n,functionName:o,method:r,headers:i,signHeaderKeys:a=[],config:s}=t,l=Date.now(),c="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})),u=Object.assign({},i,{"x-from-app-id":s.spaceAppId,"x-from-env-id":s.spaceId,"x-to-env-id":s.spaceId,"x-from-instance-id":l,"x-from-function-name":o,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":c,"x-alipay-callid":c,"x-trace-id":c}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(a),[h="",p=""]=e.split("?")||[],f=function(e){const t=e.signedHeaders.join(";"),n=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),o=Vr(e.body).toString(Fi),r=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${n}\n${t}\n${o}\n`,i=Vr(r).toString(Fi),a=`HMAC-SHA256\n${e.timestamp}\n${i}\n`,s=Ir(a,e.secretKey).toString(Fi);return`HMAC-SHA256 Credential=${e.secretId}, SignedHeaders=${t}, Signature=${s}`}({path:h,query:p,method:r,headers:u,timestamp:l,body:JSON.stringify(n),secretId:s.accessKey,secretKey:s.secretKey,signedHeaders:d.sort()});return{url:`${s.endpoint}${e}`,headers:Object.assign({},u,{Authorization:f})}}function Li({url:e,data:t,method:n="POST",headers:o={}}){return new Promise(((r,i)=>{hr.request({url:e,method:n,data:t,header:o,dataType:"json",complete:(e={})=>{const t=o["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:o,trace_id:r}=e.data||{};return i(new dr({code:"SYS_ERR",message:n||o||"request:fail",requestId:r||t}))}r({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function zi(e,t){const{path:n,data:o,method:r="GET"}=e,{url:i,headers:a}=Mi(n,{functionName:"",data:o,method:r,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Li({url:i,data:o,method:r,headers:a}).then((e=>{const t=e.data||{};if(!t.success)throw new dr({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new dr({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Ri(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new dr({code:"INVALID_PARAM",message:"fileID不合法"});const o=t.substring(0,n),r=t.substring(n+1);return o!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),r}function Ui(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var qi={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`})}callFunction(e){return function(e,t){const{name:n,data:o}=e,r="POST",{url:i,headers:a}=Mi("/functions/invokeFunction",{functionName:n,data:o,method:r,headers:{"x-to-function-name":n},signHeaderKeys:["x-to-function-name"],config:t});return Li({url:i,data:o,method:r,headers:a}).then((e=>({errCode:0,success:!0,requestId:e.requestId,result:e.data}))).catch((e=>{throw new dr({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:o,onUploadProgress:r}){return new Promise(((i,a)=>{const s=hr.uploadFile({url:e,filePath:t,fileType:n,formData:o,name:"file",success(e){e&&e.statusCode<400?i(e):a(new dr({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new dr({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:o}){if("string"!==Bo(t))throw new dr({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new dr({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new dr({code:"INVALID_PARAM",message:"cloudPath不合法"});const r=await zi({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:i,upload_url:a,form_data:s}=r,l=s&&s.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:a,filePath:e,fileType:n,formData:l,onUploadProgress:o}).then((()=>({fileID:i})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&n(new dr({errCode:"INVALID_PARAM",errMsg:"fileList不能为空数组"})),e.length>50&&n(new dr({errCode:"INVALID_PARAM",errMsg:"fileList数组长度不能超过50"}));const o=[];for(const r of e){"string"!==Bo(r)&&n(new dr({errCode:"INVALID_PARAM",errMsg:"fileList的元素必须是非空的字符串"}));const e=Ri.call(this,r);o.push({file_id:e,expire:600})}zi({path:"/?download_url",data:{file_list:o},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:Ui.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function ji({data:e}){let t;t=wr();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=fr();e&&(n.uniIdToken=e)}return n}const Hi=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var Wi=/[\\^$.*+?()[\]{}|]/g,Ki=RegExp(Wi.source);function Yi(e,t,n){return e.replace(new RegExp((o=t)&&Ki.test(o)?o.replace(Wi,"\\$&"):o,"g"),n);var o}const Gi=2e4,Ji={code:20101,message:"Invalid client"};function Zi(e){const{errSubject:t,subject:n,errCode:o,errMsg:r,code:i,message:a,cause:s}=e||{};return new dr({subject:t||n||"uni-secure-network",code:o||i||Gi,message:r||a,cause:s})}let Xi;function Qi({secretType:e}={}){return"request"===e||"response"===e||"both"===e}function ea({name:e,data:t={}}={}){return"DCloud-clientDB"===e&&"encryption"===t.redirectTo&&"getAppClientKey"===t.action}function ta({functionName:e,result:t,logPvd:n}){}function na(e){const t=e.callFunction,n=function(n){const o=n.name;n.data=ji.call(e,{data:n.data});const r={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay"}[this.config.provider],i=Qi(n),a=ea(n),s=i||a;return t.call(this,n).then((e=>(e.errCode=0,!s&&ta.call(this,{functionName:o,result:e,logPvd:r}),Promise.resolve(e))),(e=>(!s&&ta.call(this,{functionName:o,result:e,logPvd:r}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let o=0;o<n.length;o++){const{rule:r,content:i,mode:a}=n[o],s=e.match(r);if(!s)continue;let l=i;for(let e=1;e<s.length;e++)l=Yi(l,`{$${e}}`,s[e]);for(const e in t)l=Yi(l,`{${e}}`,t[e]);return"replace"===a?l:e+l}return e}({message:`[${n.name}]: ${e.message}`,formatter:Hi,extraInfo:{functionName:o}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:o,spaceId:r}=e.config,i=t.name;let a,s;return t.data=t.data||{},a=n,a=a.bind(e),s=ea(t)?n.call(e,t):Qi(t)?new Xi({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function({provider:e,spaceId:t,functionName:n}={}){const{appId:o,uniPlatform:r,osName:i}=vr();let a=r;"app"===r&&(a=i);const s=function({provider:e,spaceId:t}={}){if(!Mo)return{};var n;e="tencent"===(n=e)?"tcb":n;const o=Mo.find((n=>n.provider===e&&n.spaceId===t));return o&&o.config}({provider:e,spaceId:t});if(!s||!s.accessControl||!s.accessControl.enable)return!1;const l=s.accessControl.function||{},c=Object.keys(l);if(0===c.length)return!0;const u=function(e,t){let n,o,r;for(let i=0;i<e.length;i++){const a=e[i];a!==t?"*"!==a?a.split(",").map((e=>e.trim())).indexOf(t)>-1&&(o=a):r=a:n=a}return n||o||r}(c,n);if(!u)return!1;if((l[u]||[]).find(((e={})=>e.appId===o&&(e.platform||"").toLowerCase()===a.toLowerCase())))return!0;throw console.error(`此应用[appId: ${o}, platform: ${a}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),Zi(Ji)}({provider:o,spaceId:r,functionName:i})?new Xi({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):a(t),Object.defineProperty(s,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),s}}Xi=class{constructor(){throw Zi({message:"Platform app is not enabled, please check whether secure network module is enabled in your manifest.json"})}};const oa=Symbol("CLIENT_DB_INTERNAL");function ra(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=oa,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,o){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,o)}})}function ia(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const o=e[t].indexOf(n);-1!==o&&e[t].splice(o,1)}}}const aa=["db.Geo","db.command","command.aggregate"];function sa(e,t){return aa.indexOf(`${e}.${t}`)>-1}function la(e){switch(Bo(e=pr(e))){case"array":return e.map((e=>la(e)));case"object":return e._internalType===oa||Object.keys(e).forEach((t=>{e[t]=la(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function ca(e){return e&&e.content&&e.content.$method}class ua{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:la(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=ca(e),n=ca(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===ca(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=ca(e),n=ca(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return da({$method:e,$param:la(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),o=this.getCommand();return o.$db.push({$method:e,$param:la(t)}),this._database._callCloudFunction({action:n,command:o})}}function da(e,t,n){return ra(new ua(e,t,n),{get(e,t){let o="db";return e&&e.content&&(o=e.content.$method),sa(o,t)?da({$method:t},e,n):function(){return da({$method:t,$param:la(Array.from(arguments))},e,n)}}})}function ha({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function pa(e,t={}){return ra(new e(t),{get:(e,t)=>sa("db",t)?da({$method:t},null,e):function(){return da({$method:t,$param:la(Array.from(arguments))},null,e)}})}class fa extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=Uo("_globalUniCloudDatabaseCallback")),t||(this.auth=ia(this._authCallBacks)),this._isJQL=t,Object.assign(this,ia(this._dbCallBacks)),this.env=ra({},{get:(e,t)=>({$env:t})}),this.Geo=ra({},{get:(e,t)=>ha({path:["Geo"],method:t})}),this.serverDate=ha({path:[],method:"serverDate"}),this.RegExp=ha({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:o}){function r(e,t){if(n&&o)for(let n=0;n<o.length;n++){const r=o[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}const i=this,a=this._isJQL?"databaseForJQL":"database";function s(e){return i._callback("error",[e]),Ko(Yo(a,"fail"),e).then((()=>Ko(Yo(a,"complete"),e))).then((()=>(r(null,e),ar(Zo,{type:er,content:e}),Promise.reject(e))))}const l=Ko(Yo(a,"invoke")),c=this._uniClient;return l.then((()=>c.callFunction({name:"DCloud-clientDB",type:"CLIENT_DB",data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:o,tokenExpired:l,systemInfo:c=[]}=e.result;if(c)for(let r=0;r<c.length;r++){const{level:e,message:t,detail:n}=c[r],o=console["warn"===e?"error":e]||console.log;let i="[System Info]"+t;n&&(i=`${i}\n详细信息：${n}`),o(i)}if(t)return s(new dr({code:t,message:n,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,o&&l&&(mr({token:o,tokenExpired:l}),this._callbackAuth("refreshToken",[{token:o,tokenExpired:l}]),this._callback("refreshToken",[{token:o,tokenExpired:l}]),ar(Qo,{token:o,tokenExpired:l}));const u=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let r=0;r<u.length;r++){const{prop:t,tips:n}=u[r];if(t in e.result){const o=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(n),o)})}}return d=e,Ko(Yo(a,"success"),d).then((()=>Ko(Yo(a,"complete"),d))).then((()=>{r(d,null);const e=i._parseResult(d);return ar(Zo,{type:er,content:e}),Promise.resolve(e)}));var d}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),s(new dr({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const ma="token无效，跳转登录页面",ga="token过期，跳转登录页面",ya={TOKEN_INVALID_TOKEN_EXPIRED:ga,TOKEN_INVALID_INVALID_CLIENTID:ma,TOKEN_INVALID:ma,TOKEN_INVALID_WRONG_TOKEN:ma,TOKEN_INVALID_ANONYMOUS_USER:ma},va={"uni-id-token-expired":ga,"uni-id-check-token-failed":ma,"uni-id-token-not-exist":ma,"uni-id-check-device-feature-failed":ma};function ba(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function wa(e=[],t=""){const n=[],o=[];return e.forEach((e=>{!0===e.needLogin?n.push(ba(t,e.path)):!1===e.needLogin&&o.push(ba(t,e.path))})),{needLoginPage:n,notNeedLoginPage:o}}function ka(e){return e.split("?")[0].replace(/^\//,"")}function xa(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function Sa(){return ka(xa())}function Ca(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,o=ka(e);return n.some((e=>e.pagePath===o))}const _a=!!wo.uniIdRouter,{loginPage:Na,routerNeedLogin:Da,resToLogin:Ea,needLoginPage:Ta,notNeedLoginPage:Ba,loginPageInTabBar:Va}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:o={}}=wo){const{loginPage:r,needLogin:i=[],resToLogin:a=!0}=n,{needLoginPage:s,notNeedLoginPage:l}=wa(e),{needLoginPage:c,notNeedLoginPage:u}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:o,pages:r=[]}=e,{needLoginPage:i,notNeedLoginPage:a}=wa(r,o);t.push(...i),n.push(...a)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:r,routerNeedLogin:i,resToLogin:a,needLoginPage:[...s,...c],notNeedLoginPage:[...l,...u],loginPageInTabBar:Ca(r,o)}}();if(Ta.indexOf(Na)>-1)throw new Error(`Login page [${Na}] should not be "needLogin", please check your pages.json`);function Ia(e){const t=Sa();if("/"===e.charAt(0))return e;const[n,o]=e.split("?"),r=n.replace(/^\//,"").split("/"),i=t.split("/");i.pop();for(let a=0;a<r.length;a++){const e=r[a];".."===e?i.pop():"."!==e&&i.push(e)}return""===i[0]&&i.shift(),"/"+i.join("/")+(o?"?"+o:"")}function Aa({redirect:e}){const t=ka(e),n=ka(Na);return Sa()!==n&&t!==n}function Pa({api:e,redirect:t}={}){if(!t||!Aa({redirect:t}))return;const n=(r=t,"/"!==(o=Na).charAt(0)&&(o="/"+o),r?o.indexOf("?")>-1?o+`&uniIdRedirectUrl=${encodeURIComponent(r)}`:o+`?uniIdRedirectUrl=${encodeURIComponent(r)}`:o);var o,r;Va?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const i={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((()=>{i[e]({url:n})}))}function $a({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=fr();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:va[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:va[e]}}return n}();if(function(e){const t=ka(Ia(e));return!(Ba.indexOf(t)>-1)&&(Ta.indexOf(t)>-1||Da.some((t=>{return n=e,new RegExp(t).test(n);var n})))}(e)&&n){if(n.uniIdRedirectUrl=e,or(Xo).length>0)return setTimeout((()=>{ar(Xo,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Oa(){!function(){const e=xa(),{abortLoginPageJump:t,autoToLoginPage:n}=$a({url:e});t||n&&Pa({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];uni.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:o}=$a({url:e.url});return t?e:o?(Pa({api:n,redirect:Ia(e.url)}),!1):e}})}}function Fa(){this.onResponse((e=>{const{type:t,content:n}=e;let o=!1;switch(t){case"cloudobject":o=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in va}(n);break;case"clientdb":o=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in ya}(n)}o&&function(e={}){const t=or(Xo);cr().then((()=>{const n=xa();if(n&&Aa({redirect:n}))return t.length>0?ar(Xo,Object.assign({uniIdRedirectUrl:n},e)):void(Na&&Pa({api:"navigateTo",redirect:n}))}))}(n)}))}function Ma(e){var t;(t=e).onResponse=function(e){rr(Zo,e)},t.offResponse=function(e){ir(Zo,e)},function(e){e.onNeedLogin=function(e){rr(Xo,e)},e.offNeedLogin=function(e){ir(Xo,e)},_a&&(Uo("_globalUniCloudStatus").needLoginInit||(Uo("_globalUniCloudStatus").needLoginInit=!0,cr().then((()=>{Oa.call(e)})),Ea&&Fa.call(e)))}(e),function(e){e.onRefreshToken=function(e){rr(Qo,e)},e.offRefreshToken=function(e){ir(Qo,e)}}(e)}let La;const za="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Ra=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Ua(){const e=fr().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(La(o).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(r){throw new Error("获取当前用户信息出错，详细错误信息为："+r.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}La="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Ra.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",i=0;i<e.length;)t=za.indexOf(e.charAt(i++))<<18|za.indexOf(e.charAt(i++))<<12|(n=za.indexOf(e.charAt(i++)))<<6|(o=za.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;var qa=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ko((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",o="chooseAndUploadFile:fail";function r(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function i(e,t,{onChooseFile:o,onUploadProgress:r}){return t.then((e=>{if(o){const t=o(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,o=5,r){(t=Object.assign({},t)).errMsg=n;const i=t.tempFiles,a=i.length;let s=0;return new Promise((n=>{for(;s<o;)l();function l(){const o=s++;if(o>=a)return void(!i.find((e=>!e.url&&!e.errMsg))&&n(t));const c=i[o];e.uploadFile({filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress(e){e.index=o,e.tempFile=c,e.tempFilePath=c.path,r&&r(e)}}).then((e=>{c.url=e.fileID,o<a&&l()})).catch((e=>{c.errMsg=e.errMsg||e.message,o<a&&l()}))}}))}(e,t,5,r)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?i(e,function(e){const{count:t,sizeType:n,sourceType:i=["album","camera"],extension:a}=e;return new Promise(((e,s)=>{uni.chooseImage({count:t,sizeType:n,sourceType:i,extension:a,success(t){e(r(t,"image"))},fail(e){s({errMsg:e.errMsg.replace("chooseImage:fail",o)})}})}))}(t),t):"video"===t.type?i(e,function(e){const{camera:t,compressed:n,maxDuration:i,sourceType:a=["album","camera"],extension:s}=e;return new Promise(((e,l)=>{uni.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:a,extension:s,success(t){const{tempFilePath:n,duration:o,size:i,height:a,width:s}=t;e(r({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:s,height:a,duration:o,fileType:"video",cloudPath:""}]},"video"))},fail(e){l({errMsg:e.errMsg.replace("chooseVideo:fail",o)})}})}))}(t),t):i(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,i)=>{let a=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(a=wx.chooseMessageFile),"function"!=typeof a)return i({errMsg:o+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});a({type:"all",count:t,extension:n,success(t){e(r(t))},fail(e){i({errMsg:e.errMsg.replace("chooseFile:fail",o)})}})}))}(t),t)}}})));function ja(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{}}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if("manual"===this.loadtime)return;let n=!1;const o=[];for(let r=2;r<e.length;r++)e[r]!==t[r]&&(o.push(e[r]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,o)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:o,count:r}=n.result;this.getcount&&(this.mixinDatacomPage.count=r),this.mixinDatacomHasMore=o.length<this.pageSize;const i=e?o.length?o[0]:void 0:o;this.mixinDatacomResData=i,t&&t(i)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,n&&n(e)})))},mixinDatacomGet(t={}){let n=e.database(this.spaceInfo);const o=t.action||this.action;o&&(n=n.action(o));const r=t.collection||this.collection;n=Array.isArray(r)?n.collection(...r):n.collection(r);const i=t.where||this.where;i&&Object.keys(i).length&&(n=n.where(i));const a=t.field||this.field;a&&(n=n.field(a));const s=t.foreignKey||this.foreignKey;s&&(n=n.foreignKey(s));const l=t.groupby||this.groupby;l&&(n=n.groupBy(l));const c=t.groupField||this.groupField;c&&(n=n.groupField(c)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const u=t.orderby||this.orderby;u&&(n=n.orderBy(u));const d=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,h=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,p=void 0!==t.getcount?t.getcount:this.getcount,f=void 0!==t.gettree?t.gettree:this.gettree,m=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,g={getCount:p},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return f&&(g.getTree=y),m&&(g.getTreePath=y),n=n.skip(h*(d-1)).limit(h).get(g),n}}}}function Ha(e){return Uo("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function Wa({openid:e,callLoginByWeixin:t=!1}={}){throw Ha(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `app`")}async function Ka(e){const t=Ha(this);return t.initPromise||(t.initPromise=Wa.call(this,e)),t.initPromise}function Ya(e){const t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise(((o,r)=>{t[e]({...n,success(e){o(e)},fail(e){r(e)}})}))}}class Ga extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const o=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(o,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let o=0;o<n.length;o++)n[o](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([Ya("getSystemInfo")(),Ya("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:o,message:r}=t;this._payloadQueue.push({action:n,messageId:o,message:r}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:o}=e;"end"===t?this._end({messageId:n,message:o}):"message"===t&&this._appendMessage({messageId:n,message:o})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){uni.onPushMessage(this._uniPushMessageCallback)}_destroy(){uni.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const Ja={tcb:Pi,tencent:Pi,aliyun:_r,private:Oi,alipay:qi};let Za=new class{init(e){let t={};const n=Ja[e.provider];if(!n)throw new Error("未提供正确的provider参数");var o;return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new Oo({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),na(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(o=t).database=function(e){if(e&&Object.keys(e).length>0)return o.init(e).database();if(this._database)return this._database;const t=pa(fa,{uniClient:o});return this._database=t,t},o.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return o.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=pa(fa,{uniClient:o,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=Ua,e.chooseAndUploadFile=qa.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return ja(e)}}),e.SSEChannel=Ga,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return Ka.call(e,{openid:t,callLoginByWeixin:n})}}(e),e.importObject=function(t){return function(n,o={}){o=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},o);const{customUI:r,loadingOptions:i,errorOptions:a,parseSystemError:s}=o,l=!r;return new Proxy({},{get(r,c){switch(c){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...o){const r=n?n({params:o}):{};let i,a;try{return await Ko(Yo(t,"invoke"),{...r}),i=await e(...o),await Ko(Yo(t,"success"),{...r,result:i}),i}catch(s){throw a=s,await Ko(Yo(t,"fail"),{...r,error:a}),a}finally{await Ko(Yo(t,"complete"),a?{...r,error:a}:{...r,result:i})}}}({fn:async function r(...u){let d;l&&uni.showLoading({title:i.title,mask:i.mask});const h={name:n,type:"OBJECT",data:{method:c,params:u}};"object"==typeof o.secretMethods&&function(e,t){const n=t.data.method,o=e.secretMethods||{},r=o[n]||o["*"];r&&(t.secretType=r)}(o,h);let p=!1;try{d=await t.callFunction(h)}catch(e){p=!0,d={result:new dr(e)}}const{errSubject:f,errCode:m,errMsg:g,newToken:y}=d.result||{};if(l&&uni.hideLoading(),y&&y.token&&y.tokenExpired&&(mr(y),ar(Qo,{...y})),m){let e=g;if(p&&s&&(e=(await s({objectName:n,methodName:c,params:u,errSubject:f,errCode:m,errMsg:g})).errMsg||g),l)if("toast"===a.type)uni.showToast({title:e,icon:"none"});else{if("modal"!==a.type)throw new Error(`Invalid errorOptions.type: ${a.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:o,confirmText:r}={}){return new Promise(((i,a)=>{uni.showModal({title:e,content:t,showCancel:n,cancelText:o,confirmText:r,success(e){i(e)},fail(){i({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:a.retry,cancelText:"取消",confirmText:a.retry?"重试":"确定"});if(a.retry&&t)return r(...u)}}const t=new dr({subject:f,code:m,message:g,requestId:d.requestId});throw t.detail=d.result,ar(Zo,{type:nr,content:t}),t}return ar(Zo,{type:nr,content:d.result}),d.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:n,methodName:c,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];var o,r;t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=(o=t[e],r=e,function(e){let t=!1;if("callFunction"===r){const n=e&&e.type||Do;t=n!==Do}const n="callFunction"===r&&!t,i=this._initPromiseHub.exec();e=e||{};const{success:a,fail:s,complete:l}=ur(e),c=i.then((()=>t?Promise.resolve():Ko(Yo(r,"invoke"),e))).then((()=>o.call(this,e))).then((e=>t?Promise.resolve(e):Ko(Yo(r,"success"),e).then((()=>Ko(Yo(r,"complete"),e))).then((()=>(n&&ar(Zo,{type:tr,content:e}),Promise.resolve(e))))),(e=>t?Promise.reject(e):Ko(Yo(r,"fail"),e).then((()=>Ko(Yo(r,"complete"),e))).then((()=>(ar(Zo,{type:tr,content:e}),Promise.reject(e))))));if(!(a||s||l))return c;c.then((e=>{a&&a(e),l&&l(e),n&&ar(Zo,{type:tr,content:e})}),(e=>{s&&s(e),l&&l(e),n&&ar(Zo,{type:tr,content:e})}))}).bind(t)})),t.init=this.init,t}};(()=>{const e=Lo;let t={};if(e&&1===e.length)t=e[0],Za=Za.init(t),Za._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{Za[e]=function(){return console.error(n),Promise.reject(new dr({code:"SYS_ERR",message:n}))}}))}Object.assign(Za,{get mixinDatacom(){return ja(Za)}}),Ma(Za),Za.addInterceptor=Ho,Za.removeInterceptor=Wo,Za.interceptObject=Go})();var Xa=Za;const Qa=Ve({components:{Loading1:Ve({name:"loading1",data:()=>({})},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"container loading1"},[e.createElementVNode("view",{class:"shape shape1"}),e.createElementVNode("view",{class:"shape shape2"}),e.createElementVNode("view",{class:"shape shape3"}),e.createElementVNode("view",{class:"shape shape4"})])}],["__scopeId","data-v-76c74c2c"]]),Loading2:Ve({name:"loading2",data:()=>({})},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"container loading2"},[e.createElementVNode("view",{class:"shape shape1"}),e.createElementVNode("view",{class:"shape shape2"}),e.createElementVNode("view",{class:"shape shape3"}),e.createElementVNode("view",{class:"shape shape4"})])}],["__scopeId","data-v-3ac378a0"]]),Loading3:Ve({name:"loading3",data:()=>({})},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"container loading3"},[e.createElementVNode("view",{class:"shape shape1"}),e.createElementVNode("view",{class:"shape shape2"}),e.createElementVNode("view",{class:"shape shape3"}),e.createElementVNode("view",{class:"shape shape4"})])}],["__scopeId","data-v-1eae5fe9"]]),Loading4:Ve({name:"loading5",data:()=>({})},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"container loading5"},[e.createElementVNode("view",{class:"shape shape1"}),e.createElementVNode("view",{class:"shape shape2"}),e.createElementVNode("view",{class:"shape shape3"}),e.createElementVNode("view",{class:"shape shape4"})])}],["__scopeId","data-v-df582bd7"]]),Loading5:Ve({name:"loading6",data:()=>({})},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"container loading6"},[e.createElementVNode("view",{class:"shape shape1"}),e.createElementVNode("view",{class:"shape shape2"}),e.createElementVNode("view",{class:"shape shape3"}),e.createElementVNode("view",{class:"shape shape4"})])}],["__scopeId","data-v-148834aa"]])},name:"qiun-loading",props:{loadingType:{type:Number,default:2}},data:()=>({})},[["render",function(t,n,o,r,i,a){const s=e.resolveComponent("Loading1"),l=e.resolveComponent("Loading2"),c=e.resolveComponent("Loading3"),u=e.resolveComponent("Loading4"),d=e.resolveComponent("Loading5");return e.openBlock(),e.createElementBlock("view",null,[1==o.loadingType?(e.openBlock(),e.createBlock(s,{key:0})):e.createCommentVNode("",!0),2==o.loadingType?(e.openBlock(),e.createBlock(l,{key:1})):e.createCommentVNode("",!0),3==o.loadingType?(e.openBlock(),e.createBlock(c,{key:2})):e.createCommentVNode("",!0),4==o.loadingType?(e.openBlock(),e.createBlock(u,{key:3})):e.createCommentVNode("",!0),5==o.loadingType?(e.openBlock(),e.createBlock(d,{key:4})):e.createCommentVNode("",!0)])}]]);const es=Ve({name:"qiun-error",props:{errorMessage:{type:String,default:null}},data:()=>({})},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"chartsview"},[e.createElementVNode("view",{class:"charts-error"}),e.createElementVNode("view",{class:"charts-font"},e.toDisplayString(null==o.errorMessage?"请点击重试":o.errorMessage),1)])}],["__scopeId","data-v-04fb35a8"]]),ts=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],ns={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","mount","bar","area","radar","gauge","candle","mix","tline","tarea","scatter","bubble","demotype"],range:["饼状图","圆环图","玫瑰图","词云图","漏斗图","地图","圆弧进度条","折线图","柱状图","山峰图","条状图","区域图","雷达图","仪表盘","K线图","混合图","时间轴折线","时间轴区域","散点图","气泡图","自定义类型"],categories:["line","column","mount","bar","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(e,t,n){return e+"元"},yAxisDemo2:function(e,t,n){return e.toFixed(2)},xAxisDemo1:function(e,t,n){return e+"年"},xAxisDemo2:function(e,t,n){return((e,t)=>{var n=new Date;n.setTime(1e3*e);var o=n.getFullYear(),r=n.getMonth()+1;r=r<10?"0"+r:r;var i=n.getDate();i=i<10?"0"+i:i;var a=n.getHours();a=a<10?"0"+a:a;var s=n.getMinutes(),l=n.getSeconds();return s=s<10?"0"+s:s,l=l<10?"0"+l:l,"full"==t?o+"-"+r+"-"+i+" "+a+":"+s+":"+l:"y-m-d"==t?o+"-"+r+"-"+i:"h:m"==t?a+":"+s:"h:m:s"==t?a+":"+s+":"+l:[o,r,i,a,s,l]})(e,"h:m")},seriesDemo1:function(e,t,n,o){return e+"元"},tooltipDemo1:function(e,t,n,o){return 0==n?"随便用"+e.data+"年":"其他我没改"+e.data+"天"},pieDemo:function(e,t,n,o){if(void 0!==t)return n[t].name+"："+n[t].data+"元"}},demotype:{type:"line",color:ts,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:ts,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:ts,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"收益率",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:ts,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:ts,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:ts,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:ts,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:ts,title:{name:"百分比",fontSize:25,color:"#00FF00"},subtitle:{name:"默认标题",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:ts,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2,activeType:"hollow"}}},tline:{type:"line",color:ts,padding:[15,10,0,15],xAxis:{disableGrid:!1,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{line:{type:"curve",width:2,activeType:"hollow"}}},tarea:{type:"area",color:ts,padding:[15,10,0,15],xAxis:{disableGrid:!0,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},column:{type:"column",color:ts,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},mount:{type:"mount",color:ts,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{mount:{type:"mount",widthRatio:1.5}}},bar:{type:"bar",color:ts,padding:[15,30,0,5],xAxis:{boundaryGap:"justify",disableGrid:!1,min:0,axisLine:!1},yAxis:{},legend:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:ts,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1,activeType:"hollow"}}},radar:{type:"radar",color:ts,padding:[5,5,5,5],dataLabel:!1,legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,max:200,labelShow:!0}}},gauge:{type:"gauge",color:ts,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"实时速度",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:ts,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:ts,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},scatter:{type:"scatter",color:ts,padding:[15,15,0,15],dataLabel:!1,xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0},yAxis:{disableGrid:!1,gridType:"dash"},legend:{},extra:{scatter:{}}},bubble:{type:"bubble",color:ts,padding:[15,15,0,15],xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0,max:250},yAxis:{disableGrid:!1,gridType:"dash",data:[{min:0,max:150}]},legend:{},extra:{bubble:{border:2,opacity:.5}}}},os=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],rs={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(e){let t="";for(let n in e){0==n&&(t+=e[n].axisValueLabel+"年销售额");let o="--";null!==e[n].data&&(o=e[n].data),t+="<br/>"+e[n].marker+e[n].seriesName+"："+o+" 万元"}return t},legendFormat:function(e){return"自定义图例+"+e},yAxisFormatDemo:function(e,t){return e+"元"},seriesFormatDemo:function(e){return e.name+"年"+e.value+"元"}},demotype:{color:os},column:{color:os,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:os,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:os,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:os,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:os,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:os,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],roseType:"area"}},funnel:{color:os,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:os,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}},candle:{xAxis:{data:[]},yAxis:{},color:os,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}},is=e=>{(e.$renderjs||(e.$renderjs=[])).push("rdcharts"),(e.$renderjsModules||(e.$renderjsModules={})).rdcharts="f9cb76fc"};function as(e={},...t){for(let n in t)for(let o in t[n])t[n].hasOwnProperty(o)&&(e[o]=t[n][o]&&"object"==typeof t[n][o]?as(Array.isArray(t[n][o])?[]:{},e[o],t[n][o]):t[n][o]);return e}function ss(e,t){for(let n in e)e.hasOwnProperty(n)&&null!==e[n]&&"object"==typeof e[n]?ss(e[n],t):"format"===n&&"string"==typeof e[n]&&(e.formatter=t[e[n]]?t[e[n]]:void 0);return e}const ls={name:"qiun-data-charts",mixins:[Xa.mixinDatacom],props:{type:{type:String,default:null},canvasId:{type:String,default:"uchartsid"},canvas2d:{type:Boolean,default:!1},background:{type:String,default:"rgba(0,0,0,0)"},animation:{type:Boolean,default:!0},chartData:{type:Object,default:()=>({categories:[],series:[]})},opts:{type:Object,default:()=>({})},eopts:{type:Object,default:()=>({})},loadingType:{type:Number,default:2},errorShow:{type:Boolean,default:!0},errorReload:{type:Boolean,default:!0},errorMessage:{type:String,default:null},inScrollView:{type:Boolean,default:!1},reshow:{type:Boolean,default:!1},reload:{type:Boolean,default:!1},disableScroll:{type:Boolean,default:!1},optsWatch:{type:Boolean,default:!0},onzoom:{type:Boolean,default:!1},ontap:{type:Boolean,default:!0},ontouch:{type:Boolean,default:!1},onmouse:{type:Boolean,default:!0},onmovetip:{type:Boolean,default:!1},echartsH5:{type:Boolean,default:!1},echartsApp:{type:Boolean,default:!1},tooltipShow:{type:Boolean,default:!0},tooltipFormat:{type:String,default:void 0},tooltipCustom:{type:Object,default:void 0},startDate:{type:String,default:void 0},endDate:{type:String,default:void 0},textEnum:{type:Array,default:()=>[]},groupEnum:{type:Array,default:()=>[]},pageScrollTop:{type:Number,default:0},directory:{type:String,default:"/"},tapLegend:{type:Boolean,default:!0},menus:{type:Array,default:()=>[]}},data:()=>({cid:"uchartsid",inWx:!1,inAli:!1,inTt:!1,inBd:!1,inH5:!1,inApp:!1,inWin:!1,type2d:!0,disScroll:!1,openmouse:!1,pixel:1,cWidth:375,cHeight:250,showchart:!1,echarts:!1,echartsResize:{state:!1},uchartsOpts:{},echartsOpts:{},drawData:{},lastDrawTime:null}),created(){if(this.cid=this.canvasId,"uchartsid"==this.canvasId||""==this.canvasId){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",t=e.length,n="";for(let o=0;o<32;o++)n+=e.charAt(Math.floor(Math.random()*t));this.cid=n}const e=uni.getSystemInfoSync();"windows"!==e.platform&&"mac"!==e.platform||(this.inWin=!0),this.type2d=!1,this.disScroll=this.disableScroll},mounted(){this.inApp=!0,!0===this.echartsApp&&(this.echarts=!0,this.openmouse=!1),this.$nextTick((()=>{this.beforeInit()}))},destroyed(){!0===this.echarts?(delete rs.option[this.cid],delete rs.instance[this.cid]):(delete ns.option[this.cid],delete ns.instance[this.cid]),uni.offWindowResize((()=>{}))},watch:{chartDataProps:{handler(e,t){"object"==typeof e?JSON.stringify(e)!==JSON.stringify(t)&&(this._clearChart(),e.series&&e.series.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null)):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：chartData数据类型错误")},immediate:!1,deep:!0},localdata:{handler(e,t){JSON.stringify(e)!==JSON.stringify(t)&&(e.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage=null))},immediate:!1,deep:!0},optsProps:{handler(e,t){"object"==typeof e?JSON.stringify(e)!==JSON.stringify(t)&&!1===this.echarts&&1==this.optsWatch&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：opts数据类型错误")},immediate:!1,deep:!0},eoptsProps:{handler(e,t){"object"==typeof e?JSON.stringify(e)!==JSON.stringify(t)&&!0===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：eopts数据类型错误")},immediate:!1,deep:!0},reshow(e,t){!0===e&&!1===this.mixinDatacomLoading&&setTimeout((()=>{this.mixinDatacomErrorMessage=null,this.echartsResize.state=!this.echartsResize.state,this.checkData(this.drawData)}),200)},reload(e,t){!0===e&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())},mixinDatacomErrorMessage(e,t){e&&(this.emitMsg({name:"error",params:{type:"error",errorShow:this.errorShow,msg:e,id:this.cid}}),this.errorShow&&Q("log","at uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue:611","[秋云图表组件]"+e))},errorMessage(e,t){e&&this.errorShow&&null!==e&&"null"!==e&&""!==e?(this.showchart=!1,this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e):(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())}},computed:{optsProps(){return JSON.parse(JSON.stringify(this.opts))},eoptsProps(){return JSON.parse(JSON.stringify(this.eopts))},chartDataProps(){return JSON.parse(JSON.stringify(this.chartData))}},methods:{beforeInit(){this.mixinDatacomErrorMessage=null,"object"==typeof this.chartData&&null!=this.chartData&&void 0!==this.chartData.series&&this.chartData.series.length>0?(this.drawData=as({},this.chartData),this.mixinDatacomLoading=!1,this.showchart=!0,this.checkData(this.chartData)):this.localdata.length>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.localdataInit(this.localdata)):""!==this.collection?(this.mixinDatacomLoading=!1,this.getCloudData()):this.mixinDatacomLoading=!0},localdataInit(e){if(this.groupEnum.length>0)for(let u=0;u<e.length;u++)for(let t=0;t<this.groupEnum.length;t++)e[u].group===this.groupEnum[t].value&&(e[u].group=this.groupEnum[t].text);if(this.textEnum.length>0)for(let u=0;u<e.length;u++)for(let t=0;t<this.textEnum.length;t++)e[u].text===this.textEnum[t].value&&(e[u].text=this.textEnum[t].text);let t=!1,n={categories:[],series:[]},o=[],r=[];if(t=!0===this.echarts?rs.categories.includes(this.type):ns.categories.includes(this.type),!0===t){if(this.chartData&&this.chartData.categories&&this.chartData.categories.length>0)o=this.chartData.categories;else if(this.startDate&&this.endDate){let e=new Date(this.startDate),t=new Date(this.endDate);for(;e<=t;)o.push((a=void 0,s=void 0,l=void 0,a=(i=e).getFullYear(),s=i.getMonth()+1,l=i.getDate(),s>=1&&s<=9&&(s="0"+s),l>=0&&l<=9&&(l="0"+l),a+"-"+s+"-"+l)),e=e.setDate(e.getDate()+1),e=new Date(e)}else{let t={};e.map((function(e,n){null==e.text||t[e.text]||(o.push(e.text),t[e.text]=!0)}))}n.categories=o}var i,a,s,l;let c={};if(e.map((function(e,t){null==e.group||c[e.group]||(r.push({name:e.group,data:[]}),c[e.group]=!0)})),0==r.length)if(r=[{name:"默认分组",data:[]}],!0===t)for(let u=0;u<o.length;u++){let t=0;for(let n=0;n<e.length;n++)e[n].text==o[u]&&(t=e[n].value);r[0].data.push(t)}else for(let u=0;u<e.length;u++)r[0].data.push({name:e[u].text,value:e[u].value});else for(let u=0;u<r.length;u++)if(o.length>0)for(let t=0;t<o.length;t++){let n=0;for(let i=0;i<e.length;i++)r[u].name==e[i].group&&e[i].text==o[t]&&(n=e[i].value);r[u].data.push(n)}else for(let t=0;t<e.length;t++)r[u].name==e[t].group&&r[u].data.push(e[t].value);n.series=r,this.drawData=as({},n),this.checkData(n)},reloading(){!1!==this.errorReload&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,""!==this.collection?(this.mixinDatacomLoading=!1,this.onMixinDatacomPropsChange(!0)):this.beforeInit())},checkData(e){let t=this.cid;!0===this.echarts?(rs.option[t]=as({},this.eopts),rs.option[t].id=t,rs.option[t].type=this.type):this.type&&ns.type.includes(this.type)?(ns.option[t]=as({},ns[this.type],this.opts),ns.option[t].canvasId=t):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：props参数中type类型不正确");let n=as({},e);void 0!==n.series&&n.series.length>0&&(this.mixinDatacomErrorMessage=null,!0===this.echarts?(rs.option[t].chartData=n,this.$nextTick((()=>{this.init()}))):(ns.option[t].categories=n.categories,ns.option[t].series=n.series,this.$nextTick((()=>{this.init()}))))},resizeHandler(){let e=Date.now();e-(this.lastDrawTime?this.lastDrawTime:e-3e3)<1e3||uni.createSelectorQuery().in(this).select("#ChartBoxId"+this.cid).boundingClientRect((e=>{this.showchart=!0,e.width>0&&e.height>0&&(e.width===this.cWidth&&e.height===this.cHeight||this.checkData(this.drawData))})).exec()},getCloudData(){1!=this.mixinDatacomLoading&&(this.mixinDatacomLoading=!0,this.mixinDatacomGet().then((e=>{this.mixinDatacomResData=e.result.data,this.localdataInit(this.mixinDatacomResData)})).catch((e=>{this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="请求错误："+e})))},onMixinDatacomPropsChange(e,t){1==e&&""!==this.collection&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this._clearChart(),this.getCloudData())},_clearChart(){let e=this.cid;if(!0!==this.echarts&&ns.option[e]&&ns.option[e].context){const t=ns.option[e].context;"object"!=typeof t||ns.option[e].update||(t.clearRect(0,0,this.cWidth*this.pixel,this.cHeight*this.pixel),t.draw())}},init(){let e=this.cid;uni.createSelectorQuery().in(this).select("#ChartBoxId"+e).boundingClientRect((t=>{t.width>0&&t.height>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.lastDrawTime=Date.now(),this.cWidth=t.width,this.cHeight=t.height,!0!==this.echarts&&(ns.option[e].background="rgba(0,0,0,0)"==this.background?"#FFFFFF":this.background,ns.option[e].canvas2d=this.type2d,ns.option[e].pixelRatio=this.pixel,ns.option[e].animation=this.animation,ns.option[e].width=t.width*this.pixel,ns.option[e].height=t.height*this.pixel,ns.option[e].onzoom=this.onzoom,ns.option[e].ontap=this.ontap,ns.option[e].ontouch=this.ontouch,ns.option[e].onmouse=this.openmouse,ns.option[e].onmovetip=this.onmovetip,ns.option[e].tooltipShow=this.tooltipShow,ns.option[e].tooltipFormat=this.tooltipFormat,ns.option[e].tooltipCustom=this.tooltipCustom,ns.option[e].inScrollView=this.inScrollView,ns.option[e].lastDrawTime=this.lastDrawTime,ns.option[e].tapLegend=this.tapLegend),this.inH5||this.inApp?1==this.echarts?(rs.option[e].ontap=this.ontap,rs.option[e].onmouse=this.openmouse,rs.option[e].tooltipShow=this.tooltipShow,rs.option[e].tooltipFormat=this.tooltipFormat,rs.option[e].tooltipCustom=this.tooltipCustom,rs.option[e].lastDrawTime=this.lastDrawTime,this.echartsOpts=as({},rs.option[e])):(ns.option[e].rotateLock=ns.option[e].rotate,this.uchartsOpts=as({},ns.option[e])):(ns.option[e]=ss(ns.option[e],ns.formatter),this.mixinDatacomErrorMessage=null,this.mixinDatacomLoading=!1,this.showchart=!0,this.$nextTick((()=>{if(!0===this.type2d){uni.createSelectorQuery().in(this).select("#"+e).fields({node:!0,size:!0}).exec((n=>{if(n[0]){const o=n[0].node,r=o.getContext("2d");ns.option[e].context=r,ns.option[e].rotateLock=ns.option[e].rotate,ns.instance[e]&&ns.option[e]&&!0===ns.option[e].update?this._updataUChart(e):(o.width=t.width*this.pixel,o.height=t.height*this.pixel,o._width=t.width*this.pixel,o._height=t.height*this.pixel,setTimeout((()=>{ns.option[e].context.restore(),ns.option[e].context.save(),this._newChart(e)}),100))}else this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：开启2d模式后，未获取到dom节点，canvas-id:"+e}))}else this.inAli&&(ns.option[e].rotateLock=ns.option[e].rotate),ns.option[e].context=uni.createCanvasContext(e,this),ns.instance[e]&&ns.option[e]&&!0===ns.option[e].update?this._updataUChart(e):setTimeout((()=>{ns.option[e].context.restore(),ns.option[e].context.save(),this._newChart(e)}),100)})))):(this.mixinDatacomLoading=!1,this.showchart=!1,1==this.reshow&&(this.mixinDatacomErrorMessage="布局错误：未获取到父元素宽高尺寸！canvas-id:"+e))})).exec()},saveImage(){uni.canvasToTempFilePath({canvasId:this.cid,success:e=>{uni.saveImageToPhotosAlbum({filePath:e.tempFilePath,success:function(){uni.showToast({title:"保存成功",duration:2e3})}})}},this)},getImage(){if(0==this.type2d)uni.canvasToTempFilePath({canvasId:this.cid,success:e=>{this.emitMsg({name:"getImage",params:{type:"getImage",base64:e.tempFilePath}})}},this);else{uni.createSelectorQuery().in(this).select("#"+this.cid).fields({node:!0,size:!0}).exec((e=>{if(e[0]){const t=e[0].node;this.emitMsg({name:"getImage",params:{type:"getImage",base64:t.toDataURL("image/png")}})}}))}},_error(e){this.mixinDatacomErrorMessage=e.detail.errMsg},emitMsg(e){this.$emit(e.name,e.params)},getRenderType(){!0===this.echarts&&!1===this.mixinDatacomLoading&&this.beforeInit()},toJSON(){return this}}};is(ls);const cs=Ve(ls,[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("qiun-loading"),Qa),l=ee(e.resolveDynamicComponent("qiun-error"),es);return e.openBlock(),e.createElementBlock("view",{class:"chartsview",id:"ChartBoxId"+i.cid},[t.mixinDatacomLoading?(e.openBlock(),e.createElementBlock("view",{key:0},[e.createVNode(s,{loadingType:o.loadingType},null,8,["loadingType"])])):e.createCommentVNode("",!0),t.mixinDatacomErrorMessage&&o.errorShow?(e.openBlock(),e.createElementBlock("view",{key:1,onClick:n[0]||(n[0]=(...e)=>a.reloading&&a.reloading(...e))},[e.createVNode(l,{errorMessage:o.errorMessage},null,8,["errorMessage"])])):e.createCommentVNode("",!0),i.echarts?e.withDirectives((e.openBlock(),e.createElementBlock("view",{key:2,style:e.normalizeStyle([{background:o.background},{width:"100%",height:"100%"}]),"data-directory":o.directory,id:"EC"+i.cid,prop:e.wp(i.echartsOpts),"change:prop":t.rdcharts.ecinit,resize:e.wp(i.echartsResize),"change:resize":t.rdcharts.ecresize},null,12,["data-directory","id","prop","change:prop","resize","change:resize"])),[[e.vShow,i.showchart]]):(e.openBlock(),e.createElementBlock("view",{key:3,onClick:n[2]||(n[2]=(...e)=>t.rdcharts.tap&&t.rdcharts.tap(...e)),onMousemove:n[3]||(n[3]=(...e)=>t.rdcharts.mouseMove&&t.rdcharts.mouseMove(...e)),onMousedown:n[4]||(n[4]=(...e)=>t.rdcharts.mouseDown&&t.rdcharts.mouseDown(...e)),onMouseup:n[5]||(n[5]=(...e)=>t.rdcharts.mouseUp&&t.rdcharts.mouseUp(...e)),onTouchstart:n[6]||(n[6]=(...e)=>t.rdcharts.touchStart&&t.rdcharts.touchStart(...e)),onTouchmove:n[7]||(n[7]=(...e)=>t.rdcharts.touchMove&&t.rdcharts.touchMove(...e)),onTouchend:n[8]||(n[8]=(...e)=>t.rdcharts.touchEnd&&t.rdcharts.touchEnd(...e)),id:"UC"+i.cid,prop:e.wp(i.uchartsOpts),"change:prop":t.rdcharts.ucinit},[e.withDirectives(e.createElementVNode("canvas",{id:i.cid,canvasId:i.cid,style:e.normalizeStyle({width:i.cWidth+"px",height:i.cHeight+"px",background:o.background}),"disable-scroll":o.disableScroll,onError:n[1]||(n[1]=(...e)=>a._error&&a._error(...e))},null,44,["id","canvasId","disable-scroll"]),[[e.vShow,i.showchart]])],40,["id","prop","change:prop"]))],8,["id"])}],["__scopeId","data-v-5df47dcd"]]),us={__name:"home-chart",setup(t){const n=e.ref({}),o=e.ref({color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],padding:[15,15,0,5],enableScroll:!1,legend:{},xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}});return setTimeout((()=>{n.value=JSON.parse(JSON.stringify({categories:["2018","2019","2020","2021","2022","2023"],series:[{name:"目标值",data:[35,36,31,33,13,34]},{name:"完成量",data:[18,27,21,24,6,28]}]}))}),500),(t,r)=>{const i=ee(e.resolveDynamicComponent("tui-section"),bo),a=ee(e.resolveDynamicComponent("qiun-data-charts"),cs);return e.openBlock(),e.createElementBlock("view",null,[e.createVNode(i,{padding:"20rpx 50rpx",title:"图表（示例）","is-line":"","line-cap":"square","line-right":20,background:"#fff",size:28}),e.createVNode(a,{type:"column",opts:o.value,chartData:n.value},null,8,["opts","chartData"]),e.createVNode(a,{type:"line",opts:o.value,chartData:n.value},null,8,["opts","chartData"])])}}},ds={props:{show:{type:Boolean,default:!0},showBorder:{type:Boolean,default:!1},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"},cancelColor:{type:String,default:"#909193"},confirmColor:{type:String,default:"#3c9cff"},title:{type:String,default:""},...null==(d=null==(u=uni.$uv)?void 0:u.props)?void 0:d.toolbar}};const hs=Ve({name:"uv-toolbar",emits:["confirm","cancel"],mixins:[X,Ee,ds],methods:{cancel(){this.$emit("cancel")},confirm(){this.$emit("confirm")}}},[["render",function(t,n,o,r,i,a){return t.show?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uv-toolbar",{"uv-border-bottom":t.showBorder}]),onTouchmove:n[2]||(n[2]=e.withModifiers(((...e)=>t.noop&&t.noop(...e)),["stop","prevent"]))},[e.createElementVNode("view",{class:"uv-toolbar__cancel__wrapper","hover-class":"uv-hover-class"},[e.createElementVNode("text",{class:"uv-toolbar__wrapper__cancel",onClick:n[0]||(n[0]=(...e)=>a.cancel&&a.cancel(...e)),style:e.normalizeStyle({color:t.cancelColor})},e.toDisplayString(t.cancelText),5)]),t.title?(e.openBlock(),e.createElementBlock("text",{key:0,class:"uv-toolbar__title uv-line-1"},e.toDisplayString(t.title),1)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"uv-toolbar__confirm__wrapper","hover-class":"uv-hover-class"},[e.createElementVNode("text",{class:"uv-toolbar__wrapper__confirm",onClick:n[1]||(n[1]=(...e)=>a.confirm&&a.confirm(...e)),style:e.normalizeStyle({color:t.confirmColor})},e.toDisplayString(t.confirmText),5)])],34)):e.createCommentVNode("",!0)}],["__scopeId","data-v-dd95b77d"]]);class ps{constructor(e,t){this.options=e,this.animation=uni.createAnimation({...e}),this.currentStepAnimates={},this.next=0,this.$=t}_nvuePushAnimates(e,t){let n=this.currentStepAnimates[this.next],o={};if(o=n||{styles:{},config:{}},fs.includes(e)){o.styles.transform||(o.styles.transform="");let n="";"rotate"===e&&(n="deg"),o.styles.transform+=`${e}(${t+n}) `}else o.styles[e]=`${t}`;this.currentStepAnimates[this.next]=o}_animateRun(e={},t={}){let n=this.$.$refs.ani.ref;if(n)return new Promise(((o,r)=>{nvueAnimation.transition(n,{styles:e,...t},(e=>{o()}))}))}_nvueNextAnimate(e,t=0,n){let o=e[t];if(o){let{styles:r,config:i}=o;this._animateRun(r,i).then((()=>{t+=1,this._nvueNextAnimate(e,t,n)}))}else this.currentStepAnimates={},"function"==typeof n&&n(),this.isEnd=!0}step(e={}){return this.animation.step(e),this}run(e){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((()=>{"function"==typeof e&&e()}),this.$.durationTime)}}const fs=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];function ms(e,t){if(t)return clearTimeout(t.timer),new ps(e,t)}fs.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((e=>{ps.prototype[e]=function(...t){return this.animation[e](...t),this}}));const gs=Ve({name:"uv-transition",mixins:[X,Ee],emits:["click","change"],props:{show:{type:Boolean,default:!1},mode:{type:[Array,String,null],default:()=>"fade"},duration:{type:[String,Number],default:300},timingFunction:{type:String,default:"ease-out"},customClass:{type:String,default:""},cellChild:{type:Boolean,default:!1}},data:()=>({isShow:!1,transform:"",opacity:1,animationData:{},durationTime:300,config:{}}),watch:{show:{handler(e){e?this.open():this.isShow&&this.close()},immediate:!0}},computed:{transformStyles(){const e={transform:this.transform,opacity:this.opacity,...this.$uv.addStyle(this.customStyle),"transition-duration":this.duration/1e3+"s"};return this.$uv.addStyle(e,"string")}},created(){this.config={duration:this.duration,timingFunction:this.timingFunction,transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init(e={}){e.duration&&(this.durationTime=e.duration),this.animation=ms(Object.assign(this.config,e),this)},onClick(){this.$emit("click",{detail:this.isShow})},step(e,t={}){if(this.animation){for(let t in e)try{"object"==typeof e[t]?this.animation[t](...e[t]):this.animation[t](e[t])}catch(Er){Q("error","at uni_modules/uv-transition/components/uv-transition/uv-transition.vue:166",`方法 ${t} 不存在`)}return this.animation.step(t),this}},run(e){this.animation&&this.animation.run(e)},open(){clearTimeout(this.timer),this.transform="",this.isShow=!0;let{opacity:e,transform:t}=this.styleInit(!1);void 0!==e&&(this.opacity=e),this.transform=t,this.$nextTick((()=>{this.timer=setTimeout((()=>{this.animation=ms(this.config,this),this.tranfromInit(!1).step(),this.animation.run(),this.$emit("change",{detail:this.isShow})}),20)}))},close(e){this.animation&&this.tranfromInit(!0).step().run((()=>{this.isShow=!1,this.animationData=null,this.animation=null;let{opacity:e,transform:t}=this.styleInit(!1);this.opacity=e||1,this.transform=t,this.$emit("change",{detail:this.isShow})}))},styleInit(e){let t={transform:""},n=(e,n)=>{"fade"===n?t.opacity=this.animationType(e)[n]:t.transform+=this.animationType(e)[n]+" "};return"string"==typeof this.mode?n(e,this.mode):this.mode.forEach((t=>{n(e,t)})),t},tranfromInit(e){let t=(e,t)=>{let n=null;"fade"===t?n=e?0:1:(n=e?"-100%":"0","zoom-in"===t&&(n=e?.8:1),"zoom-out"===t&&(n=e?1.2:1),"slide-right"===t&&(n=e?"100%":"0"),"slide-bottom"===t&&(n=e?"100%":"0")),this.animation[this.animationMode()[t]](n)};return"string"==typeof this.mode?t(e,this.mode):this.mode.forEach((n=>{t(e,n)})),this.animation},animationType:e=>({fade:e?1:0,"slide-top":`translateY(${e?"0":"-100%"})`,"slide-right":`translateX(${e?"0":"100%"})`,"slide-bottom":`translateY(${e?"0":"100%"})`,"slide-left":`translateX(${e?"0":"-100%"})`,"zoom-in":`scaleX(${e?1:.8}) scaleY(${e?1:.8})`,"zoom-out":`scaleX(${e?1:1.2}) scaleY(${e?1:1.2})`}),animationMode:()=>({fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}),toLine:e=>e.replace(/([A-Z])/g,"-$1").toLowerCase()}},[["render",function(t,n,o,r,i,a){return i.isShow?(e.openBlock(),e.createElementBlock("view",{key:0,ref:"ani",animation:i.animationData,class:e.normalizeClass(o.customClass),style:e.normalizeStyle(a.transformStyles),onClick:n[0]||(n[0]=(...e)=>a.onClick&&a.onClick(...e))},[e.renderSlot(t.$slots,"default")],14,["animation"])):e.createCommentVNode("",!0)}]]),ys={props:{show:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:10070},duration:{type:[String,Number],default:300},opacity:{type:[String,Number],default:.5},...null==(p=null==(h=uni.$uv)?void 0:h.props)?void 0:p.overlay}};const vs=Ve({name:"uv-overlay",emits:["click"],mixins:[X,Ee,ys],watch:{show(e){}},computed:{overlayStyle(){const e={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},methods:{clickHandler(){this.$emit("click")},clear(){}}},[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-transition"),gs);return e.openBlock(),e.createBlock(s,{show:t.show,mode:"fade","custom-class":"uv-overlay",duration:t.duration,"custom-style":a.overlayStyle,onClick:a.clickHandler,onTouchmove:e.withModifiers(a.clear,["stop","prevent"])},{default:e.withCtx((()=>[e.renderSlot(t.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style","onClick","onTouchmove"])}],["__scopeId","data-v-a7fd60d2"]]);const bs=Ve({name:"uv-status-bar",mixins:[X,Ee,{props:{bgColor:{type:String,default:"transparent"}}}],data:()=>({}),computed:{style(){const e={};return e.height=this.$uv.addUnit(this.$uv.sys().statusBarHeight,"px"),this.bgColor&&(this.bgColor.indexOf("gradient")>-1?e.backgroundImage=this.bgColor:e.background=this.bgColor),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{style:e.normalizeStyle([a.style]),class:"uv-status-bar"},[e.renderSlot(t.$slots,"default",{},void 0,!0)],4)}],["__scopeId","data-v-f978439c"]]);const ws=Ve({name:"uv-safe-bottom",mixins:[X,Ee],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))}},mounted(){}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-safe-bottom",[!i.isNvue&&"uv-safe-area-inset-bottom"]]),style:e.normalizeStyle([a.style])},null,6)}],["__scopeId","data-v-e97e85dd"]]),ks={name:"uv-popup",components:{},mixins:[X,Ee],emits:["change","maskClick"],props:{mode:{type:String,default:"center"},duration:{type:[String,Number],default:300},zIndex:{type:[String,Number],default:10075},bgColor:{type:String,default:"#ffffff"},safeArea:{type:Boolean,default:!0},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0},overlayOpacity:{type:[Number,String],default:.4},overlayStyle:{type:[Object,String],default:""},safeAreaInsetBottom:{type:Boolean,default:!0},safeAreaInsetTop:{type:Boolean,default:!1},closeable:{type:Boolean,default:!1},closeIconPos:{type:String,default:"top-right"},zoom:{type:Boolean,default:!0},round:{type:[Number,String],default:0},...null==(m=null==(f=uni.$uv)?void 0:f.props)?void 0:m.popup},watch:{type:{handler:function(e){this.config[e]&&this[this.config[e]](!0)},immediate:!0},isDesktop:{handler:function(e){this.config[e]&&this[this.config[this.mode]](!0)},immediate:!0},showPopup(e){}},data(){return{ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},transitionStyle:{position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupClass:this.isDesktop?"fixforpc-top":"top"}},computed:{isDesktop(){return this.popupWidth>=500&&this.popupHeight>=500},bg(){return""===this.bgColor||"none"===this.bgColor||this.$uv.getPx(this.round)>0?"transparent":this.bgColor},contentStyle(){const e={};if(this.bgColor&&(e.backgroundColor=this.bg),this.round){const t=this.$uv.addUnit(this.round);e.backgroundColor=this.bgColor,"top"===this.mode?(e.borderBottomLeftRadius=t,e.borderBottomRightRadius=t):"bottom"===this.mode?(e.borderTopLeftRadius=t,e.borderTopRightRadius=t):"center"===this.mode&&(e.borderRadius=t)}return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},unmounted(){this.setH5Visible()},created(){this.messageChild=null,this.clearPropagation=!1},methods:{setH5Visible(){},closeMask(){this.maskShow=!1},clear(e){e.stopPropagation(),this.clearPropagation=!0},open(e){if(this.showPopup)return;if(e&&-1!==["top","center","bottom","left","right","message","dialog","share"].indexOf(e)||(e=this.mode),!this.config[e])return this.$uv.error(`缺少类型：${e}`);this[this.config[e]](),this.$emit("change",{show:!0,type:e})},close(e){this.showTrans=!1,this.$emit("change",{show:!1,type:this.mode}),clearTimeout(this.timer),this.timer=setTimeout((()=>{this.showPopup=!1}),300)},touchstart(){this.clearPropagation=!1},onTap(){this.clearPropagation?this.clearPropagation=!1:(this.$emit("maskClick"),this.closeOnClickOverlay&&this.close())},top(e){this.popupClass=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,backgroundColor:this.bg},e||(this.showPopup=!0,this.showTrans=!0,this.$nextTick((()=>{this.messageChild&&"message"===this.mode&&this.messageChild.timerClose()})))},bottom(e){this.popupClass="bottom",this.ani=["slide-bottom"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,bottom:0,backgroundColor:this.bg},e||(this.showPopup=!0,this.showTrans=!0)},center(e){this.popupClass="center",this.ani=this.zoom?["zoom-in","fade"]:["fade"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,display:"flex",flexDirection:"column",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center"},e||(this.showPopup=!0,this.showTrans=!0)},left(e){this.popupClass="left",this.ani=["slide-left"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,bottom:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},e||(this.showPopup=!0,this.showTrans=!0)},right(e){this.popupClass="right",this.ani=["slide-right"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,bottom:0,right:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},e||(this.showPopup=!0,this.showTrans=!0)}}};const xs=Ve(ks,[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-overlay"),vs),l=ee(e.resolveDynamicComponent("uv-status-bar"),bs),c=ee(e.resolveDynamicComponent("uv-safe-bottom"),ws),u=ee(e.resolveDynamicComponent("uv-icon"),Ae),d=ee(e.resolveDynamicComponent("uv-transition"),gs);return i.showPopup?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uv-popup",[i.popupClass,a.isDesktop?"fixforpc-z-index":""]]),style:e.normalizeStyle([{zIndex:o.zIndex}])},[e.createElementVNode("view",{onTouchstart:n[2]||(n[2]=(...e)=>a.touchstart&&a.touchstart(...e))},[i.maskShow&&o.overlay?(e.openBlock(),e.createBlock(s,{key:"1",show:i.showTrans,duration:o.duration,"custom-style":o.overlayStyle,opacity:o.overlayOpacity,zIndex:o.zIndex,onClick:a.onTap},null,8,["show","duration","custom-style","opacity","zIndex","onClick"])):e.createCommentVNode("",!0),e.createVNode(d,{key:"2",mode:i.ani,name:"content","custom-style":i.transitionStyle,duration:o.duration,show:i.showTrans,onClick:a.onTap},{default:e.withCtx((()=>[e.createElementVNode("view",{class:e.normalizeClass(["uv-popup__content",[i.popupClass]]),style:e.normalizeStyle([a.contentStyle]),onClick:n[1]||(n[1]=(...e)=>a.clear&&a.clear(...e))},[o.safeAreaInsetTop?(e.openBlock(),e.createBlock(l,{key:0})):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"default",{},void 0,!0),o.safeAreaInsetBottom?(e.openBlock(),e.createBlock(c,{key:1})):e.createCommentVNode("",!0),o.closeable?(e.openBlock(),e.createElementBlock("view",{key:2,onClick:n[0]||(n[0]=e.withModifiers(((...e)=>a.close&&a.close(...e)),["stop"])),class:e.normalizeClass(["uv-popup__content__close",["uv-popup__content__close--"+o.closeIconPos]]),"hover-class":"uv-popup__content__close--hover","hover-stay-time":"150"},[e.createVNode(u,{name:"close",color:"#909399",size:"18",bold:""})],2)):e.createCommentVNode("",!0)],6)])),_:3},8,["mode","custom-style","duration","show","onClick"])],32)],6)):e.createCommentVNode("",!0)}],["__scopeId","data-v-2b2892c9"]]);var Ss={lunarInfo:[19416,19168,42352,21717,53856,55632,91476,22176,39632,21970,19168,42422,42192,53840,119381,46400,54944,44450,38320,84343,18800,42160,46261,27216,27968,109396,11104,38256,21234,18800,25958,54432,59984,28309,23248,11104,100067,37600,116951,51536,54432,120998,46416,22176,107956,9680,37584,53938,43344,46423,27808,46416,86869,19872,42416,83315,21168,43432,59728,27296,44710,43856,19296,43748,42352,21088,62051,55632,23383,22176,38608,19925,19152,42192,54484,53840,54616,46400,46752,103846,38320,18864,43380,42160,45690,27216,27968,44870,43872,38256,19189,18800,25776,29859,59984,27480,23232,43872,38613,37600,51552,55636,54432,55888,30034,22176,43959,9680,37584,51893,43344,46240,47780,44368,21977,19360,42416,86390,21168,43312,31060,27296,44368,23378,19296,42726,42208,53856,60005,54576,23200,30371,38608,19195,19152,42192,118966,53840,54560,56645,46496,22224,21938,18864,42359,42160,43600,111189,27936,44448,84835,37744,18936,18800,25776,92326,59984,27424,108228,43744,41696,53987,51552,54615,54432,55888,23893,22176,42704,21972,21200,43448,43344,46240,46758,44368,21920,43940,42416,21168,45683,26928,29495,27296,44368,84821,19296,42352,21732,53600,59752,54560,55968,92838,22224,19168,43476,41680,53584,62034,54560],solarMonth:[31,28,31,30,31,30,31,31,30,31,30,31],Gan:["甲","乙","丙","丁","戊","己","庚","辛","壬","癸"],Zhi:["子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"],Animals:["鼠","牛","虎","兔","龙","蛇","马","羊","猴","鸡","狗","猪"],solarTerm:["小寒","大寒","立春","雨水","惊蛰","春分","清明","谷雨","立夏","小满","芒种","夏至","小暑","大暑","立秋","处暑","白露","秋分","寒露","霜降","立冬","小雪","大雪","冬至"],sTermInfo:["9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd0b06bdb0722c965ce1cfcc920f","b027097bd097c36b0b6fc9274c91aa","9778397bd19801ec9210c965cc920e","97b6b97bd19801ec95f8c965cc920f","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd197c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bd09801d98082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec95f8c965cc920e","97bcf97c3598082c95f8e1cfcc920f","97bd097bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c3598082c95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf97c359801ec95f8c965cc920f","97bd097bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd19801ec9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b97bd19801ec95f8c965cc920f","97bd07f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c91aa","97b6b97bd19801ec9210c965cc920e","97bd07f1487f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c965cc920e","97bcf7f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b97bd19801ec9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b97bd197c36c9210c9274c920e","97bcf7f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","9778397bd097c36c9210c9274c920e","97b6b7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c36b0b6fc9210c8dc2","9778397bd097c36b0b70c9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9274c91aa","97b6b7f0e47f531b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c91aa","97b6b7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","9778397bd097c36b0b6fc9210c8dc2","977837f0e37f149b0723b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f5307f595b0b0bc920fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f595b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc9210c8dc2","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd097c35b0b6fc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0b0bb0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14998082b0723b06bd","7f07e7f0e37f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e397bd07f595b0b0bc920fb0722","977837f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f595b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e37f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f1487f531b0b0bb0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e47f149b0723b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14998082b0723b06bd","7f07e7f0e37f14998083b0787b0721","7f0e27f0e47f531b0723b0b6fb0722","7f0e37f0e366aa89801eb072297c35","7ec967f0e37f14898082b0723b02d5","7f07e7f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66aa89801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b0721","7f07e7f0e47f531b0723b0b6fb0722","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b0723b02d5","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e36665b66a449801e9808297c35","665f67f0e37f14898082b072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e26665b66a449801e9808297c35","665f67f0e37f1489801eb072297c35","7ec967f0e37f14998082b0787b06bd","7f07e7f0e47f531b0723b0b6fb0721","7f0e27f1487f531b0b0bb0b6fb0722"],nStr1:["日","一","二","三","四","五","六","七","八","九","十"],nStr2:["初","十","廿","卅"],nStr3:["正","二","三","四","五","六","七","八","九","十","冬","腊"],lYearDays:function(e){var t,n=348;for(t=32768;t>8;t>>=1)n+=this.lunarInfo[e-1900]&t?1:0;return n+this.leapDays(e)},leapMonth:function(e){return 15&this.lunarInfo[e-1900]},leapDays:function(e){return this.leapMonth(e)?65536&this.lunarInfo[e-1900]?30:29:0},monthDays:function(e,t){return t>12||t<1?-1:this.lunarInfo[e-1900]&65536>>t?30:29},solarDays:function(e,t){if(t>12||t<1)return-1;var n=t-1;return 1==n?e%4==0&&e%100!=0||e%400==0?29:28:this.solarMonth[n]},toGanZhiYear:function(e){var t=(e-3)%10,n=(e-3)%12;return 0==t&&(t=10),0==n&&(n=12),this.Gan[t-1]+this.Zhi[n-1]},toAstro:function(e,t){return"魔羯水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯".substr(2*e-(t<[20,19,21,21,21,22,23,23,23,23,22,22][e-1]?2:0),2)+"座"},toGanZhi:function(e){return this.Gan[e%10]+this.Zhi[e%12]},getTerm:function(e,t){if(e<1900||e>2100)return-1;if(t<1||t>24)return-1;var n=this.sTermInfo[e-1900],o=[parseInt("0x"+n.substr(0,5)).toString(),parseInt("0x"+n.substr(5,5)).toString(),parseInt("0x"+n.substr(10,5)).toString(),parseInt("0x"+n.substr(15,5)).toString(),parseInt("0x"+n.substr(20,5)).toString(),parseInt("0x"+n.substr(25,5)).toString()],r=[o[0].substr(0,1),o[0].substr(1,2),o[0].substr(3,1),o[0].substr(4,2),o[1].substr(0,1),o[1].substr(1,2),o[1].substr(3,1),o[1].substr(4,2),o[2].substr(0,1),o[2].substr(1,2),o[2].substr(3,1),o[2].substr(4,2),o[3].substr(0,1),o[3].substr(1,2),o[3].substr(3,1),o[3].substr(4,2),o[4].substr(0,1),o[4].substr(1,2),o[4].substr(3,1),o[4].substr(4,2),o[5].substr(0,1),o[5].substr(1,2),o[5].substr(3,1),o[5].substr(4,2)];return parseInt(r[t-1])},toChinaMonth:function(e){if(e>12||e<1)return-1;var t=this.nStr3[e-1];return t+="月"},toChinaDay:function(e){var t;switch(e){case 10:t="初十";break;case 20:t="二十";break;case 30:t="三十";break;default:t=this.nStr2[Math.floor(e/10)],t+=this.nStr1[e%10]}return t},getAnimal:function(e){return this.Animals[(e-4)%12]},solar2lunar:function(e,t,n){if(e<1900||e>2100)return-1;if(1900==e&&1==t&&n<31)return-1;if(e)o=new Date(e,parseInt(t)-1,n);else var o=new Date;var r,i=0,a=(e=o.getFullYear(),t=o.getMonth()+1,n=o.getDate(),(Date.UTC(o.getFullYear(),o.getMonth(),o.getDate())-Date.UTC(1900,0,31))/864e5);for(r=1900;r<2101&&a>0;r++)a-=i=this.lYearDays(r);a<0&&(a+=i,r--);var s=new Date,l=!1;s.getFullYear()==e&&s.getMonth()+1==t&&s.getDate()==n&&(l=!0);var c=o.getDay(),u=this.nStr1[c];0==c&&(c=7);var d=r,h=this.leapMonth(r),p=!1;for(r=1;r<13&&a>0;r++)h>0&&r==h+1&&0==p?(--r,p=!0,i=this.leapDays(d)):i=this.monthDays(d,r),1==p&&r==h+1&&(p=!1),a-=i;0==a&&h>0&&r==h+1&&(p?p=!1:(p=!0,--r)),a<0&&(a+=i,--r);var f=r,m=a+1,g=t-1,y=this.toGanZhiYear(d),v=this.getTerm(e,2*t-1),b=this.getTerm(e,2*t),w=this.toGanZhi(12*(e-1900)+t+11);n>=v&&(w=this.toGanZhi(12*(e-1900)+t+12));var k=!1,x=null;v==n&&(k=!0,x=this.solarTerm[2*t-2]),b==n&&(k=!0,x=this.solarTerm[2*t-1]);var S=Date.UTC(e,g,1,0,0,0,0)/864e5+25567+10,C=this.toGanZhi(S+n-1),_=this.toAstro(t,n);return{lYear:d,lMonth:f,lDay:m,Animal:this.getAnimal(d),IMonthCn:(p?"闰":"")+this.toChinaMonth(f),IDayCn:this.toChinaDay(m),cYear:e,cMonth:t,cDay:n,gzYear:y,gzMonth:w,gzDay:C,isToday:l,isLeap:p,nWeek:c,ncWeek:"星期"+u,isTerm:k,Term:x,astro:_}},lunar2solar:function(e,t,n,o){o=!!o;var r=this.leapMonth(e);if(this.leapDays(e),o&&r!=t)return-1;if(2100==e&&12==t&&n>1||1900==e&&1==t&&n<31)return-1;var i=this.monthDays(e,t),a=i;if(o&&(a=this.leapDays(e,t)),e<1900||e>2100||n>a)return-1;for(var s=0,l=1900;l<e;l++)s+=this.lYearDays(l);var c=0,u=!1;for(l=1;l<t;l++)c=this.leapMonth(e),u||c<=l&&c>0&&(s+=this.leapDays(e),u=!0),s+=this.monthDays(e,l);o&&(s+=i);var d=Date.UTC(1900,1,30,0,0,0),h=new Date(864e5*(s+n-31)+d),p=h.getUTCFullYear(),f=h.getUTCMonth()+1,m=h.getUTCDate();return this.solar2lunar(p,f,m)}};class Cs{constructor({date:e,selected:t,startDate:n,endDate:o,range:r,multiple:i,allowSameDay:a}={}){this.date=this.getDate(new Date),this.selected=t||[],this.startDate=n,this.endDate=o,this.range=r,this.multiple=i,this.allowSameDay=a,this.cleanRangeStatus(),this.cleanMultipleStatus(),this.weeks={}}setDate(e,t){this.range&&"init"==t?(this.cleanRangeStatus(),Array.isArray(e)?(this.rangeStatus.before=e[0],this.rangeStatus.after=e.length>1?e[e.length-1]:"",this.rangeStatus.after&&this.dateCompare(this.rangeStatus.before,this.rangeStatus.after)&&(this.rangeStatus.data=this.geDateAll(this.rangeStatus.before,this.rangeStatus.after)),this.selectDate=this.getDate(e[0]),this._getWeek(this.selectDate.fullDate)):(this.selectDate=this.getDate(e),this.rangeStatus.before=this.selectDate.fullDate,this._getWeek(this.selectDate.fullDate))):this.multiple&&"init"==t?(this.cleanMultipleStatus(),Array.isArray(e)?(this.multipleStatus.data=e,this.selectDate=this.getDate(e[0]),this._getWeek(this.selectDate.fullDate)):(this.selectDate=this.getDate(e),this.multipleStatus.data=[this.selectDate.fullDate],this._getWeek(this.selectDate.fullDate))):Array.isArray(e)?(this.selectDate=this.getDate(e[0]),this._getWeek(this.selectDate.fullDate)):(this.selectDate=this.getDate(e),this._getWeek(this.selectDate.fullDate))}cleanRangeStatus(){this.rangeStatus={before:"",after:"",data:[]}}cleanMultipleStatus(){this.multipleStatus={data:[]}}resetSatrtDate(e){this.startDate=e}resetEndDate(e){this.endDate=e}getDate(e,t=0,n="day"){e||(e=new Date),"object"!=typeof e&&(e=e.replace(/-/g,"/"));const o=new Date(e);switch(n){case"day":o.setDate(o.getDate()+t);break;case"month":if(31===o.getDate()&&t>0)o.setDate(o.getDate()+t);else{const e=o.getMonth();o.setMonth(e+t);const n=o.getMonth();t<0&&0!==e&&n-e>t&&o.setMonth(n+(n-e+t)),t>0&&n-e>t&&o.setMonth(n-(n-e-t))}break;case"year":o.setFullYear(o.getFullYear()+t)}const r=o.getFullYear(),i=o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1,a=o.getDate()<10?"0"+o.getDate():o.getDate();return{fullDate:r+"-"+i+"-"+a,year:r,month:i,date:a,day:o.getDay()}}_getLastMonthDays(e,t){let n=[];for(let o=e;o>0;o--){const e=new Date(t.year,t.month-1,1-o).getDate();n.push({date:e,month:t.month-1,lunar:this.getlunar(t.year,t.month-1,e),disable:!0})}return n}_currentMonthDys(e,t){let n=[],o=this.date.fullDate;for(let r=1;r<=e;r++){let e=t.year+"-"+(t.month,t.month+"-")+(r<10?"0"+r:r),i=o===e,a=this.selected&&this.selected.find((t=>{if(this.dateEqual(e,t.date))return t})),s=!0,l=!0;this.startDate&&(s=this.dateCompare(this.startDate,e)),this.endDate&&(l=this.dateCompare(e,this.endDate));let c=this.rangeStatus.data,u=!1,d=-1;this.range&&(c&&(d=c.findIndex((t=>this.dateEqual(t,e)))),-1!==d&&(u=!0));let h=this.multipleStatus.data,p=!1,f=-1;this.multiple&&(h&&(f=h.findIndex((t=>this.dateEqual(t,e)))),-1!==f&&(p=!0));let m={fullDate:e,year:t.year,date:r,range:!!this.range&&u,multiple:!!this.multiple&&p,beforeRange:this.dateEqual(this.rangeStatus.before,e),afterRange:this.dateEqual(this.rangeStatus.after,e),dateEqual:this.range&&u&&this.dateEqual(this.rangeStatus.before,this.rangeStatus.after),month:t.month,lunar:this.getlunar(t.year,t.month,r),disable:!(s&&l),isDay:i};a&&(m.extraInfo=a),n.push(m)}return n}_getNextMonthDays(e,t){let n=[];for(let o=1;o<e+1;o++)n.push({date:o,month:Number(t.month)+1,lunar:this.getlunar(t.year,Number(t.month)+1,o),disable:!0});return n}getInfo(e){e?Array.isArray(e)&&(e=e[0]):e=new Date;return this.canlender.find((t=>t.fullDate===this.getDate(e).fullDate))}dateCompare(e,t){return(e=new Date(e.replace("-","/").replace("-","/")))<=(t=new Date(t.replace("-","/").replace("-","/")))}dateEqual(e,t){return e=new Date(e.replace("-","/").replace("-","/")),t=new Date(t.replace("-","/").replace("-","/")),e.getTime()-t.getTime()==0}dateAfterLgBefore(e,t){return e=new Date(e.replace("-","/").replace("-","/")),(t=new Date(t.replace("-","/").replace("-","/"))).getTime()-e.getTime()>0}geDateAll(e,t){var n=[],o=e.split("-"),r=t.split("-"),i=new Date;i.setFullYear(o[0],o[1]-1,o[2]);var a=new Date;a.setFullYear(r[0],r[1]-1,r[2]);for(var s=i.getTime()-864e5,l=a.getTime()-864e5,c=s;c<=l;)c+=864e5,n.push(this.getDate(new Date(parseInt(c))).fullDate);return n}getlunar(e,t,n){return Ss.solar2lunar(e,t,n)}setSelectInfo(e,t){this.selected=t,this._getWeek(e)}setMultiple(e){if(!this.multiple)return;const t=this.multipleStatus.data.findIndex((t=>this.dateEqual(e,t)));t<0?this.multipleStatus.data=this.multipleStatus.data.concat([e]):this.multipleStatus.data.splice(t,1),this._getWeek(e)}setRange(e){let{before:t,after:n}=this.rangeStatus;if(this.range){if(t&&n)this.cleanRangeStatus(),this.rangeStatus.before=e;else if(t){if(this.allowSameDay&&this.dateEqual(t,e))this.rangeStatus.after=e;else if(!this.dateAfterLgBefore(this.rangeStatus.before,e))return this.cleanRangeStatus(),this.rangeStatus.before=e,void this._getWeek(e);this.rangeStatus.after=e,this.dateCompare(this.rangeStatus.before,this.rangeStatus.after)?this.rangeStatus.data=this.geDateAll(this.rangeStatus.before,this.rangeStatus.after):this.rangeStatus.data=this.geDateAll(this.rangeStatus.after,this.rangeStatus.before)}else this.rangeStatus.before=e;this._getWeek(e)}}_getWeek(e){const{year:t,month:n}=this.getDate(e);let o=new Date(t,n-1,1).getDay(),r=new Date(t,n,0).getDate(),i={lastMonthDays:this._getLastMonthDays(o,this.getDate(e)),currentMonthDys:this._currentMonthDys(r,this.getDate(e)),nextMonthDays:[],weeks:[]},a=[];const s=42-(i.lastMonthDays.length+i.currentMonthDys.length);i.nextMonthDays=this._getNextMonthDays(s,this.getDate(e)),a=a.concat(i.lastMonthDays,i.currentMonthDys,i.nextMonthDays);let l={};for(let c=0;c<a.length;c++)c%7==0&&(l[parseInt(c/7)]=new Array(7)),l[parseInt(c/7)][c%7]=a[c];this.canlender=a,this.weeks=l}}const _s=["{","}"];const Ns=/^(?:\d)+/,Ds=/^(?:\w)+/;const Es="zh-Hans",Ts="zh-Hant",Bs="en",Vs=Object.prototype.hasOwnProperty,Is=(e,t)=>Vs.call(e,t),As=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=_s){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let a=e[r++];if(a===t){i&&o.push({type:"text",value:i}),i="";let t="";for(a=e[r++];void 0!==a&&a!==n;)t+=a,a=e[r++];const s=a===n,l=Ns.test(t)?"list":s&&Ds.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=a}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function Ps(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Es;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Es:e.indexOf("-hant")>-1?Ts:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?Ts:Es);var n;let o=[Bs,"fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class $s{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale=Bs,this.fallbackLocale=Bs,this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||As,this.messages=n||{},this.setLocale(e||Bs),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=Ps(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{Is(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=Ps(t,this.messages))&&(o=this.messages[t]):n=t,Is(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function Os(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&uni.getLocale?uni.getLocale():"undefined"!=typeof global&&global.getLocale?global.getLocale():Bs),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||Bs);const r=new $s({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{if("function"!=typeof getApp)i=function(e,t){return r.t(e,t)};else{let e=!1;i=function(t,n){const o=getApp().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}const Fs={en:{"uv-calender.ok":"ok","uv-calender.cancel":"cancel","uv-calender.today":"today","uv-calender.MON":"MON","uv-calender.TUE":"TUE","uv-calender.WED":"WED","uv-calender.THU":"THU","uv-calender.FRI":"FRI","uv-calender.SAT":"SAT","uv-calender.SUN":"SUN"},"zh-Hans":{"uv-calender.ok":"确定","uv-calender.cancel":"取消","uv-calender.today":"今日","uv-calender.SUN":"日","uv-calender.MON":"一","uv-calender.TUE":"二","uv-calender.WED":"三","uv-calender.THU":"四","uv-calender.FRI":"五","uv-calender.SAT":"六"},"zh-Hant":{"uv-calender.ok":"確定","uv-calender.cancel":"取消","uv-calender.today":"今日","uv-calender.SUN":"日","uv-calender.MON":"一","uv-calender.TUE":"二","uv-calender.WED":"三","uv-calender.THU":"四","uv-calender.FRI":"五","uv-calender.SAT":"六"}},{t:Ms}=Os(Fs);const Ls=Ve({emits:["change"],props:{weeks:{type:Object,default:()=>({})},calendar:{type:Object,default:()=>({})},selected:{type:Array,default:()=>[]},lunar:{type:Boolean,default:!1},color:{type:String,default:"#3c9cff"},range:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1}},computed:{todayText:()=>Ms("uv-calender.today"),itemBoxStyle(){const e={};return this.multiple?this.weeks.multiple?(e.backgroundColor=this.color,e.color="#fff"):this.weeks.isDay&&(e.color=this.color):this.range?this.weeks.beforeRange||this.weeks.afterRange?e.backgroundColor=this.color:this.weeks.range&&(e.backgroundColor=so(this.color,"#ffffff",100)[90],e.color=this.color,e.opacity=.8,e.borderRadius=0):(this.weeks.isDay&&(e.color=this.color),this.calendar.fullDate===this.weeks.fullDate&&(e.backgroundColor=this.color,e.color="#fff")),e},infoStyle(e){return e=>{const t={};return this.weeks.multiple?t.color="#fff":("top"==e?t.color=this.weeks.extraInfo.topinfoColor?this.weeks.extraInfo.topinfoColor:"#606266":"bottom"==e&&(t.color=this.weeks.extraInfo.infoColor?this.weeks.extraInfo.infoColor:"#f56c6c"),this.weeks.range&&(t.color=this.color),(this.calendar.fullDate===this.weeks.fullDate||this.weeks.beforeRange||this.weeks.afterRange)&&(t.color=this.multiple?t.color:"#fff")),t}}},methods:{choiceDate(e){this.weeks.extraInfo&&this.weeks.extraInfo.disable||this.$emit("change",e)}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-calendar-item__weeks-box",{"uv-calendar-item--disable":o.weeks.disable||o.weeks.extraInfo&&o.weeks.extraInfo.disable,"uv-calendar-item--isDay":o.calendar.fullDate===o.weeks.fullDate&&o.weeks.isDay&&!o.multiple,"uv-calendar-item--checked":o.calendar.fullDate===o.weeks.fullDate&&!o.weeks.isDay&&!o.multiple,"uv-calendar-item--before-checked":o.weeks.beforeRange,"uv-calendar-item--range":o.weeks.range,"uv-calendar-item--after-checked":o.weeks.afterRange,"uv-calendar-item--multiple":o.weeks.multiple}]),style:e.normalizeStyle([a.itemBoxStyle]),onClick:n[0]||(n[0]=e=>a.choiceDate(o.weeks))},[e.createElementVNode("view",{class:"uv-calendar-item__weeks-box-item"},[o.selected&&o.weeks.extraInfo&&o.weeks.extraInfo.badge?(e.openBlock(),e.createElementBlock("text",{key:0,class:"uv-calendar-item__weeks-box-circle"})):e.createCommentVNode("",!0),o.weeks.extraInfo&&o.weeks.extraInfo.topinfo?(e.openBlock(),e.createElementBlock("text",{key:1,class:"uv-calendar-item__weeks-top-text",style:e.normalizeStyle([a.infoStyle("top")])},e.toDisplayString(o.weeks.extraInfo&&o.weeks.extraInfo.topinfo),5)):e.createCommentVNode("",!0),e.createElementVNode("text",{class:e.normalizeClass(["uv-calendar-item__weeks-box-text",{"uv-calendar-item--isDay-text":o.weeks.isDay,"uv-calendar-item--isDay":o.calendar.fullDate===o.weeks.fullDate&&o.weeks.isDay&&!o.multiple,"uv-calendar-item--checked":o.calendar.fullDate===o.weeks.fullDate&&!o.weeks.isDay&&!o.multiple,"uv-calendar-item--before-checked":o.weeks.beforeRange,"uv-calendar-item--range":o.weeks.range,"uv-calendar-item--after-checked":o.weeks.afterRange,"uv-calendar-item--multiple":o.weeks.multiple,"uv-calendar-item--disable":o.weeks.disable||o.weeks.extraInfo&&o.weeks.extraInfo.disable}]),style:e.normalizeStyle([a.itemBoxStyle])},e.toDisplayString(o.weeks.date),7),o.lunar||o.weeks.extraInfo||!o.weeks.isDay?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("text",{key:2,class:e.normalizeClass(["uv-calendar-item__weeks-lunar-text",{"uv-calendar-item--isDay-text":o.weeks.isDay,"uv-calendar-item--isDay":o.calendar.fullDate===o.weeks.fullDate&&o.weeks.isDay&&!o.multiple,"uv-calendar-item--checked":o.calendar.fullDate===o.weeks.fullDate&&!o.weeks.isDay&&!o.multiple,"uv-calendar-item--before-checked":o.weeks.beforeRange,"uv-calendar-item--range":o.weeks.range,"uv-calendar-item--after-checked":o.weeks.afterRange,"uv-calendar-item--multiple":o.weeks.multiple}]),style:e.normalizeStyle([a.itemBoxStyle])},e.toDisplayString(a.todayText),7)),o.lunar&&!o.weeks.extraInfo?(e.openBlock(),e.createElementBlock("text",{key:3,class:e.normalizeClass(["uv-calendar-item__weeks-lunar-text",{"uv-calendar-item--isDay-text":o.weeks.isDay,"uv-calendar-item--isDay":o.calendar.fullDate===o.weeks.fullDate&&o.weeks.isDay&&!o.multiple,"uv-calendar-item--checked":o.calendar.fullDate===o.weeks.fullDate&&!o.weeks.isDay&&!o.multiple,"uv-calendar-item--before-checked":o.weeks.beforeRange,"uv-calendar-item--range":o.weeks.range,"uv-calendar-item--after-checked":o.weeks.afterRange,"uv-calendar-item--multiple":o.weeks.multiple,"uv-calendar-item--disable":o.weeks.disable||o.weeks.extraInfo&&o.weeks.extraInfo.disable}]),style:e.normalizeStyle([a.itemBoxStyle])},e.toDisplayString(o.weeks.isDay?a.todayText:"初一"===o.weeks.lunar.IDayCn?o.weeks.lunar.IMonthCn:o.weeks.lunar.IDayCn),7)):e.createCommentVNode("",!0),o.weeks.extraInfo&&o.weeks.extraInfo.info?(e.openBlock(),e.createElementBlock("text",{key:4,class:e.normalizeClass(["uv-calendar-item__weeks-lunar-text",{"uv-calendar-item__weeks-lunar-text--equal":o.weeks.dateEqual}]),style:e.normalizeStyle([a.infoStyle("bottom")])},e.toDisplayString(o.weeks.extraInfo.info),7)):e.createCommentVNode("",!0)])],6)}],["__scopeId","data-v-e44136fa"]]),{t:zs}=Os(Fs),Rs={mixins:[X,Ee],components:{CalendarItem:Ls},props:{date:{type:[String,Array],default:""},nowDate:{type:[String,Object],default:""},weeks:{type:[Array,Object],default:()=>[]},calendar:{type:Object,default:()=>({})},selected:{type:Array,default:()=>[]},lunar:{type:Boolean,default:!1},showMonth:{type:Boolean,default:!0},color:{type:String,default:"#3c9cff"},startText:{type:String,default:"开始"},endText:{type:String,default:"结束"},range:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},allowSameDay:{type:Boolean,default:!1}},computed:{getDate(){return Array.isArray(this.date)?this.date[0]:this.date},todayText:()=>zs("uv-calender.today"),monText:()=>zs("uv-calender.MON"),TUEText:()=>zs("uv-calender.TUE"),WEDText:()=>zs("uv-calender.WED"),THUText:()=>zs("uv-calender.THU"),FRIText:()=>zs("uv-calender.FRI"),SATText:()=>zs("uv-calender.SAT"),SUNText:()=>zs("uv-calender.SUN"),rangeInfoText(e){return e=>{var t;return this.allowSameDay&&e.beforeRange&&e.afterRange&&e.dateEqual?this.setInfo(e,`${this.startText}/${this.endText}`):e.beforeRange?this.setInfo(e,this.startText):e.afterRange?this.setInfo(e,this.endText):void((null==(t=e.extraInfo)?void 0:t.info_old)&&(e.extraInfo.info=e.extraInfo.info_old))}}},methods:{setInfo(e,t){this.setInfoOld(e),e.extraInfo?e.extraInfo.info=t:e.extraInfo={info:t}},setInfoOld(e){e.extraInfo&&(e.extraInfo.info_old=e.extraInfo.info_old||e.extraInfo.info)},bindDateChange(e){this.$emit("bindDateChange",e)},backToday(){this.$emit("backToday")},pre(){this.$emit("pre")},next(){this.$emit("next")},choiceDate(e){this.$emit("choiceDate",e)}}};const Us=Ve(Rs,[["render",function(t,n,o,r,i,a){const s=e.resolveComponent("calendar-item");return e.openBlock(),e.createElementBlock("view",{class:"uv-calendar-body"},[e.createElementVNode("view",{class:"uv-calendar__header"},[e.createElementVNode("view",{class:"uv-calendar__header-btn-box",onClick:n[0]||(n[0]=e.withModifiers(((...e)=>a.pre&&a.pre(...e)),["stop"]))},[e.createElementVNode("view",{class:"uv-calendar__header-btn uv-calendar--left"})]),e.createElementVNode("picker",{mode:"date",value:a.getDate,fields:"month",onChange:n[1]||(n[1]=(...e)=>a.bindDateChange&&a.bindDateChange(...e))},[e.createElementVNode("text",{class:"uv-calendar__header-text"},e.toDisplayString((o.nowDate.year||"")+" / "+(o.nowDate.month||"")),1)],40,["value"]),e.createElementVNode("view",{class:"uv-calendar__header-btn-box",onClick:n[2]||(n[2]=e.withModifiers(((...e)=>a.next&&a.next(...e)),["stop"]))},[e.createElementVNode("view",{class:"uv-calendar__header-btn uv-calendar--right"})]),e.createElementVNode("text",{class:"uv-calendar__backtoday",onClick:n[3]||(n[3]=(...e)=>a.backToday&&a.backToday(...e))},e.toDisplayString(a.todayText),1)]),e.createElementVNode("view",{class:"uv-calendar__box"},[o.showMonth?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-calendar__box-bg"},[e.createElementVNode("text",{class:"uv-calendar__box-bg-text"},e.toDisplayString(o.nowDate.month),1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"uv-calendar__weeks uv-calendar__weeks-week"},[e.createElementVNode("view",{class:"uv-calendar__weeks-day"},[e.createElementVNode("text",{class:"uv-calendar__weeks-day-text"},e.toDisplayString(a.SUNText),1)]),e.createElementVNode("view",{class:"uv-calendar__weeks-day"},[e.createElementVNode("text",{class:"uv-calendar__weeks-day-text"},e.toDisplayString(a.monText),1)]),e.createElementVNode("view",{class:"uv-calendar__weeks-day"},[e.createElementVNode("text",{class:"uv-calendar__weeks-day-text"},e.toDisplayString(a.TUEText),1)]),e.createElementVNode("view",{class:"uv-calendar__weeks-day"},[e.createElementVNode("text",{class:"uv-calendar__weeks-day-text"},e.toDisplayString(a.WEDText),1)]),e.createElementVNode("view",{class:"uv-calendar__weeks-day"},[e.createElementVNode("text",{class:"uv-calendar__weeks-day-text"},e.toDisplayString(a.THUText),1)]),e.createElementVNode("view",{class:"uv-calendar__weeks-day"},[e.createElementVNode("text",{class:"uv-calendar__weeks-day-text"},e.toDisplayString(a.FRIText),1)]),e.createElementVNode("view",{class:"uv-calendar__weeks-day"},[e.createElementVNode("text",{class:"uv-calendar__weeks-day-text"},e.toDisplayString(a.SATText),1)])]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.weeks,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"uv-calendar__weeks",key:n},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"uv-calendar__weeks-item",key:n},[e.createVNode(s,{class:"uv-calendar-item--hook",weeks:t,rangeInfoText:a.rangeInfoText(t),multiple:o.multiple,range:o.range,calendar:o.calendar,selected:o.selected,lunar:o.lunar,color:o.color,onChange:a.choiceDate},null,8,["weeks","rangeInfoText","multiple","range","calendar","selected","lunar","color","onChange"])])))),128))])))),128))])])}],["__scopeId","data-v-64424481"]]),{t:qs}=Os(Fs),js={components:{calendarBody:Us},mixins:[X,Ee],emits:["close","confirm","change","monthSwitch"],props:{cancelColor:{type:String,default:""},confirmColor:{type:String,default:"#3c9cff"},title:{type:String,default:""},color:{type:String,default:"#3c9cff"},date:{type:[String,Array],default:""},selected:{type:Array,default:()=>[]},lunar:{type:Boolean,default:!1},startDate:{type:String,default:""},endDate:{type:String,default:""},mode:{type:String,default:""},insert:{type:Boolean,default:!1},showMonth:{type:Boolean,default:!0},clearDate:{type:Boolean,default:!0},round:{type:[Number,String],default:8},closeOnClickOverlay:{type:Boolean,default:!0},startText:{type:String,default:"开始"},endText:{type:String,default:"结束"},allowSameDay:{type:Boolean,default:!1},...null==(y=null==(g=uni.$uv)?void 0:g.props)?void 0:y.calendars},data:()=>({weeks:[],calendar:{},nowDate:"",allowConfirm:!1,multiple:!1,range:!1}),computed:{confirmText:()=>qs("uv-calender.ok"),cancelText:()=>qs("uv-calender.cancel"),getConfirmColor(){return this.range||this.multiple?this.allowConfirm?this.confirmColor:"#999":this.confirmColor}},watch:{date(e){this.init(e)},startDate(e){this.cale.resetSatrtDate(e),this.cale.setDate(this.nowDate.fullDate),this.weeks=this.cale.weeks},endDate(e){this.cale.resetEndDate(e),this.cale.setDate(this.nowDate.fullDate),this.weeks=this.cale.weeks},selected(e){this.cale.setSelectInfo(this.nowDate.fullDate,e),this.weeks=this.cale.weeks}},created(){this.setMode(),this.cale=new Cs({selected:this.selected,startDate:this.startDate,endDate:this.endDate,range:this.range,multiple:this.multiple,allowSameDay:this.allowSameDay}),this.init(this.date)},methods:{setMode(){switch(this.mode){case"range":this.range=!0;break;case"multiple":this.multiple=!0}},async open(){this.clearDate&&!this.insert&&(this.cale.cleanRangeStatus(),this.init(this.date)),this.insert||this.$refs.popup.open()},close(){this.$refs.popup.close(),this.$emit("close")},confirm(){this.range&&!this.cale.rangeStatus.after||this.multiple&&0==this.cale.multipleStatus.data.length||(this.setEmit("confirm"),this.close())},maskClick(){this.closeOnClickOverlay&&this.$emit("close")},bindDateChange(e){const t=e.detail.value+"-1";this.setDate(t);const{year:n,month:o}=this.cale.getDate(t);this.$emit("monthSwitch",{year:n,month:o})},init(e){this.range?this.cale.cleanRangeStatus():this.multiple&&this.cale.cleanMultipleStatus(),this.cale.setDate(e,"init"),this.weeks=this.cale.weeks,this.nowDate=this.calendar=this.cale.getInfo(e),this.changeConfirmStatus()},change(){this.changeConfirmStatus(),this.insert&&this.setEmit("change")},changeConfirmStatus(){this.range?this.allowConfirm=!!this.cale.rangeStatus.after:this.multiple&&(this.allowConfirm=this.cale.multipleStatus.data.length>0)},monthSwitch(){let{year:e,month:t}=this.nowDate;this.$emit("monthSwitch",{year:e,month:Number(t)})},setEmit(e){let{year:t,month:n,date:o,fullDate:r,lunar:i,extraInfo:a}=this.calendar;this.$emit(e,{range:this.cale.rangeStatus,multiple:this.cale.multipleStatus,year:t,month:n,date:o,fulldate:r,lunar:i,extraInfo:a||{}})},choiceDate(e){e.disable||(this.calendar=e,this.cale.setRange(this.calendar.fullDate),this.cale.setMultiple(this.calendar.fullDate),this.weeks=this.cale.weeks,this.change())},backToday(){const e=`${this.nowDate.year}-${this.nowDate.month}`,t=this.cale.getDate(new Date),n=`${t.year}-${t.month}`;this.init(t.fullDate),e!==n&&this.monthSwitch(),this.change()},pre(){const e=this.cale.getDate(this.nowDate.fullDate,-1,"month").fullDate;this.setDate(e),this.monthSwitch()},next(){const e=this.cale.getDate(this.nowDate.fullDate,1,"month").fullDate;this.setDate(e),this.monthSwitch()},setDate(e){this.cale.setDate(e),this.weeks=this.cale.weeks,this.nowDate=this.cale.getInfo(e)}}};const Hs=Ve(js,[["render",function(t,n,o,r,i,a){const s=e.resolveComponent("calendar-body"),l=ee(e.resolveDynamicComponent("uv-toolbar"),hs),c=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createElementBlock("view",{class:"uv-calendar"},[o.insert?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-calendar__content"},[e.createVNode(s,{date:o.date,nowDate:i.nowDate,weeks:i.weeks,calendar:i.calendar,selected:o.selected,lunar:o.lunar,showMonth:o.showMonth,color:o.color,startText:o.startText,endText:o.endText,range:i.range,multiple:i.multiple,allowSameDay:o.allowSameDay,onBindDateChange:a.bindDateChange,onPre:a.pre,onNext:a.next,onBackToday:a.backToday,onChoiceDate:a.choiceDate},null,8,["date","nowDate","weeks","calendar","selected","lunar","showMonth","color","startText","endText","range","multiple","allowSameDay","onBindDateChange","onPre","onNext","onBackToday","onChoiceDate"])])):(e.openBlock(),e.createBlock(c,{key:1,ref:"popup",mode:"bottom",round:o.round,"z-index":"998","close-on-click-overlay":o.closeOnClickOverlay,onMaskClick:a.maskClick},{default:e.withCtx((()=>[e.createElementVNode("view",{style:{"min-height":"100px"}},[e.createVNode(l,{show:!0,cancelColor:o.cancelColor,confirmColor:a.getConfirmColor,cancelText:a.cancelText,confirmText:a.confirmText,title:o.title,onCancel:a.close,onConfirm:a.confirm},null,8,["cancelColor","confirmColor","cancelText","confirmText","title","onCancel","onConfirm"]),e.createElementVNode("view",{class:"line"}),e.createVNode(s,{nowDate:i.nowDate,weeks:i.weeks,calendar:i.calendar,selected:o.selected,lunar:o.lunar,showMonth:o.showMonth,color:o.color,startText:o.startText,endText:o.endText,range:i.range,multiple:i.multiple,allowSameDay:o.allowSameDay,onBindDateChange:a.bindDateChange,onPre:a.pre,onNext:a.next,onBackToday:a.backToday,onChoiceDate:a.choiceDate},null,8,["nowDate","weeks","calendar","selected","lunar","showMonth","color","startText","endText","range","multiple","allowSameDay","onBindDateChange","onPre","onNext","onBackToday","onChoiceDate"])])])),_:1},8,["round","close-on-click-overlay","onMaskClick"]))])}],["__scopeId","data-v-7988315f"]]),Ws={props:{span:{type:[String,Number],default:12},offset:{type:[String,Number],default:0},justify:{type:String,default:"start"},align:{type:String,default:"stretch"},textAlign:{type:String,default:"left"},...null==(b=null==(v=uni.$uv)?void 0:v.props)?void 0:b.col}};const Ks=Ve({name:"uv-col",emits:["click"],mixins:[X,Ee,Ws],data:()=>({width:0,parentData:{gutter:0},gridNum:12}),computed:{uJustify(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align},colStyle(){const e={paddingLeft:this.$uv.addUnit(this.$uv.getPx(this.parentData.gutter)/2),paddingRight:this.$uv.addUnit(this.$uv.getPx(this.parentData.gutter)/2),alignItems:this.uAlignItem,justifyContent:this.uJustify,textAlign:this.textAlign,flex:`0 0 ${100/this.gridNum*this.span}%`,marginLeft:100/12*this.offset+"%"};return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},mounted(){this.init()},methods:{async init(){this.updateParentData(),this.width=await this.parent.getComponentWidth()},updateParentData(){this.getParentData("uv-row")},clickHandler(e){this.$emit("click")}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-col",["uv-col-"+t.span]]),ref:"uv-col",style:e.normalizeStyle([a.colStyle]),onClick:n[0]||(n[0]=(...e)=>a.clickHandler&&a.clickHandler(...e))},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)}],["__scopeId","data-v-b9e03a6f"]]),Ys={props:{gutter:{type:[String,Number],default:0},justify:{type:String,default:"start"},align:{type:String,default:"center"},...null==(k=null==(w=uni.$uv)?void 0:w.props)?void 0:k.row}};const Gs=Ve({name:"uv-row",emits:["click"],mixins:[X,Ee,Ys],data:()=>({}),computed:{uJustify(){return"end"==this.justify||"start"==this.justify?"flex-"+this.justify:"around"==this.justify||"between"==this.justify?"space-"+this.justify:this.justify},uAlignItem(){return"top"==this.align?"flex-start":"bottom"==this.align?"flex-end":this.align},rowStyle(){const e={alignItems:this.uAlignItem,justifyContent:this.uJustify};return this.gutter&&(e.marginLeft=this.$uv.addUnit(-Number(this.gutter)/2),e.marginRight=this.$uv.addUnit(-Number(this.gutter)/2)),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},methods:{clickHandler(e){this.$emit("click")},async getComponentWidth(){return await this.$uv.sleep(),new Promise((e=>{this.$uvGetRect(".uv-row").then((t=>{e(t.width)}))}))}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"uv-row",ref:"uv-row",style:e.normalizeStyle([a.rowStyle]),onClick:n[0]||(n[0]=(...e)=>a.clickHandler&&a.clickHandler(...e))},[e.renderSlot(t.$slots,"default",{},void 0,!0)],4)}],["__scopeId","data-v-bf766073"]]);const Js={props:{color:{type:String,default:"#d6d7d9"},length:{type:[String,Number],default:"100%"},direction:{type:String,default:"row"},hairline:{type:Boolean,default:!0},margin:{type:[String,Number],default:0},dashed:{type:Boolean,default:!1},...null==(S=null==(x=uni.$uv)?void 0:x.props)?void 0:S.line}};const Zs=Ve({name:"uv-line",mixins:[X,Ee,Js],computed:{lineStyle(){const e={};return e.margin=this.margin,"row"===this.direction?(e.borderBottomWidth="1px",e.borderBottomStyle=this.dashed?"dashed":"solid",e.width=this.$uv.addUnit(this.length),this.hairline&&(e.transform="scaleY(0.5)")):(e.borderLeftWidth="1px",e.borderLeftStyle=this.dashed?"dashed":"solid",e.height=this.$uv.addUnit(this.length),this.hairline&&(e.transform="scaleX(0.5)")),e.borderColor=this.color,this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"uv-line",style:e.normalizeStyle([a.lineStyle])},null,4)}],["__scopeId","data-v-6ea9af94"]]),Xs={props:{label:{type:String,default:""},prop:{type:String,default:""},borderBottom:{type:[Boolean],default:!1},labelPosition:{type:String,default:""},labelWidth:{type:[String,Number],default:""},rightIcon:{type:String,default:""},leftIcon:{type:String,default:""},required:{type:Boolean,default:!1},leftIconStyle:{type:[String,Object],default:""},...null==(_=null==(C=uni.$uv)?void 0:C.props)?void 0:_.formItem}};const Qs=Ve({name:"uv-form-item",emits:["click"],mixins:[X,Ee,Xs],data:()=>({message:"",parentData:{labelPosition:"left",labelAlign:"left",labelStyle:{},labelWidth:45,errorType:"message"}}),created(){this.init()},methods:{init(){this.updateParentData(),this.parent||this.$uv.error("uv-form-item需要结合uv-form组件使用")},updateParentData(){this.getParentData("uv-form")},clearValidate(){this.message=null},resetField(){const e=this.$uv.getProperty(this.parent.originalModel,this.prop);this.$uv.setProperty(this.parent.model,this.prop,e),this.message=null},clickHandler(){this.$emit("click")}}},[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-icon"),Ae),l=ee(e.resolveDynamicComponent("uv-transition"),gs),c=ee(e.resolveDynamicComponent("uv-line"),Zs);return e.openBlock(),e.createElementBlock("view",{class:"uv-form-item"},[e.createElementVNode("view",{class:"uv-form-item__body",onClick:n[0]||(n[0]=(...e)=>a.clickHandler&&a.clickHandler(...e)),style:e.normalizeStyle([t.$uv.addStyle(t.customStyle),{flexDirection:"left"===(t.labelPosition||i.parentData.labelPosition)?"row":"column"}])},[e.renderSlot(t.$slots,"label",{},(()=>[t.required||t.leftIcon||t.label?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-form-item__body__left",style:e.normalizeStyle({width:t.$uv.addUnit(t.labelWidth||i.parentData.labelWidth),marginBottom:"left"===i.parentData.labelPosition?0:"5px"})},[e.createElementVNode("view",{class:"uv-form-item__body__left__content"},[t.required?(e.openBlock(),e.createElementBlock("text",{key:0,class:"uv-form-item__body__left__content__required"},"*")):e.createCommentVNode("",!0),t.leftIcon?(e.openBlock(),e.createElementBlock("view",{key:1,class:"uv-form-item__body__left__content__icon"},[e.createVNode(s,{name:t.leftIcon,"custom-style":t.leftIconStyle},null,8,["name","custom-style"])])):e.createCommentVNode("",!0),e.createElementVNode("text",{class:"uv-form-item__body__left__content__label",style:e.normalizeStyle([i.parentData.labelStyle,{justifyContent:"left"===i.parentData.labelAlign?"flex-start":"center"===i.parentData.labelAlign?"center":"flex-end"}])},e.toDisplayString(t.label),5)])],4)):e.createCommentVNode("",!0)]),!0),e.createElementVNode("view",{class:"uv-form-item__body__right"},[e.createElementVNode("view",{class:"uv-form-item__body__right__content"},[e.createElementVNode("view",{class:"uv-form-item__body__right__content__slot"},[e.renderSlot(t.$slots,"default",{},void 0,!0)]),e.createElementVNode("view",{class:"item__body__right__content__icon"},[e.renderSlot(t.$slots,"right",{},void 0,!0)])])])],4),e.renderSlot(t.$slots,"error",{},(()=>[i.message&&"message"===i.parentData.errorType?(e.openBlock(),e.createBlock(l,{key:0,show:!0,duration:100,mode:"fade"},{default:e.withCtx((()=>[e.createElementVNode("text",{class:"uv-form-item__body__right__message",style:e.normalizeStyle({marginLeft:t.$uv.addUnit("top"===i.parentData.labelPosition?0:t.labelWidth||i.parentData.labelWidth)})},e.toDisplayString(i.message),5)])),_:1})):e.createCommentVNode("",!0)]),!0),t.borderBottom?(e.openBlock(),e.createBlock(c,{key:0,color:i.message&&"border-bottom"===i.parentData.errorType?"#f56c6c":"#d6d7d9"},null,8,["color"])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-79c3b104"]]),el={props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},disabledColor:{type:String,default:"#f5f7fa"},clearable:{type:Boolean,default:!1},password:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:-1},placeholder:{type:String,default:null},placeholderClass:{type:String,default:"input-placeholder"},placeholderStyle:{type:[String,Object],default:"color: #c0c4cc"},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},holdKeyboard:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},autoBlur:{type:Boolean,default:!1},cursor:{type:[String,Number],default:-1},cursorSpacing:{type:[String,Number],default:30},selectionStart:{type:[String,Number],default:-1},selectionEnd:{type:[String,Number],default:-1},adjustPosition:{type:Boolean,default:!0},inputAlign:{type:String,default:"left"},fontSize:{type:[String,Number],default:"14px"},color:{type:String,default:"#303133"},prefixIcon:{type:String,default:""},prefixIconStyle:{type:[String,Object],default:""},suffixIcon:{type:String,default:""},suffixIconStyle:{type:[String,Object],default:""},border:{type:String,default:"surround"},readonly:{type:Boolean,default:!1},shape:{type:String,default:"square"},formatter:{type:[Function,null],default:null},ignoreCompositionEvent:{type:Boolean,default:!0},...null==(D=null==(N=uni.$uv)?void 0:N.props)?void 0:D.input}},tl={name:"uv-input",mixins:[X,Ee,el],data:()=>({innerValue:"",focused:!1,innerFormatter:e=>e}),created(){this.innerValue=this.modelValue},watch:{value(e){this.innerValue=e},modelValue(e){this.innerValue=e}},computed:{isShowClear(){const{clearable:e,readonly:t,focused:n,innerValue:o}=this;return!!e&&!t&&!!n&&""!==o},inputClass(){let e=[],{border:t,disabled:n,shape:o}=this;return"surround"===t&&(e=e.concat(["uv-border","uv-input--radius"])),e.push(`uv-input--${o}`),"bottom"===t&&(e=e.concat(["uv-border-bottom","uv-input--no-radius"])),e.join(" ")},wrapperStyle(){const e={};return this.disabled&&(e.backgroundColor=this.disabledColor),"none"===this.border?e.padding="0":(e.paddingTop="6px",e.paddingBottom="6px",e.paddingLeft="9px",e.paddingRight="9px"),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))},inputStyle(){const e={color:this.color,fontSize:this.$uv.addUnit(this.fontSize),textAlign:this.inputAlign};return(this.disabled||this.readonly)&&(e["pointer-events"]="none"),e}},methods:{setFormatter(e){this.innerFormatter=e},onInput(e){let{value:t=""}=e.detail||{};const n=(this.formatter||this.innerFormatter)(t);this.innerValue=t,this.$nextTick((()=>{this.innerValue=n,this.valueChange()}))},onBlur(e){this.$emit("blur",e.detail.value),this.$uv.sleep(100).then((()=>{this.focused=!1})),this.$uv.formValidate(this,"blur")},onFocus(e){this.focused=!0,this.$emit("focus")},onConfirm(e){this.$emit("confirm",this.innerValue)},onkeyboardheightchange(e){this.$emit("keyboardheightchange",e)},valueChange(){const e=this.innerValue;this.$nextTick((()=>{this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.$uv.formValidate(this,"change")}))},onClear(){this.innerValue="",this.$nextTick((()=>{this.$emit("clear"),this.valueChange()}))},clickHandler(){}}};const nl=Ve(tl,[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-input",a.inputClass]),style:e.normalizeStyle([a.wrapperStyle])},[e.createElementVNode("view",{class:"uv-input__content"},[e.createElementVNode("view",{class:"uv-input__content__prefix-icon"},[e.renderSlot(t.$slots,"prefix",{},(()=>[t.prefixIcon?(e.openBlock(),e.createBlock(s,{key:0,name:t.prefixIcon,size:"18",customStyle:t.prefixIconStyle},null,8,["name","customStyle"])):e.createCommentVNode("",!0)]),!0)]),e.createElementVNode("view",{class:"uv-input__content__field-wrapper",onClick:n[5]||(n[5]=(...e)=>a.clickHandler&&a.clickHandler(...e))},[e.createElementVNode("input",{class:"uv-input__content__field-wrapper__field",style:e.normalizeStyle([a.inputStyle]),type:t.type,focus:t.focus,cursor:t.cursor,value:i.innerValue,"auto-blur":t.autoBlur,disabled:t.disabled||t.readonly,maxlength:t.maxlength,placeholder:t.placeholder,"placeholder-style":t.placeholderStyle,"placeholder-class":t.placeholderClass,"confirm-type":t.confirmType,"confirm-hold":t.confirmHold,"hold-keyboard":t.holdKeyboard,"cursor-spacing":t.cursorSpacing,"adjust-position":t.adjustPosition,"selection-end":t.selectionEnd,"selection-start":t.selectionStart,password:t.password||"password"===t.type||void 0,ignoreCompositionEvent:t.ignoreCompositionEvent,onInput:n[0]||(n[0]=(...e)=>a.onInput&&a.onInput(...e)),onBlur:n[1]||(n[1]=(...e)=>a.onBlur&&a.onBlur(...e)),onFocus:n[2]||(n[2]=(...e)=>a.onFocus&&a.onFocus(...e)),onConfirm:n[3]||(n[3]=(...e)=>a.onConfirm&&a.onConfirm(...e)),onKeyboardheightchange:n[4]||(n[4]=(...e)=>a.onkeyboardheightchange&&a.onkeyboardheightchange(...e))},null,44,["type","focus","cursor","value","auto-blur","disabled","maxlength","placeholder","placeholder-style","placeholder-class","confirm-type","confirm-hold","hold-keyboard","cursor-spacing","adjust-position","selection-end","selection-start","password","ignoreCompositionEvent"])]),a.isShowClear?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-input__content__clear",onClick:n[6]||(n[6]=(...e)=>a.onClear&&a.onClear(...e))},[e.createVNode(s,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"uv-input__content__subfix-icon"},[e.renderSlot(t.$slots,"suffix",{},(()=>[t.suffixIcon?(e.openBlock(),e.createBlock(s,{key:0,name:t.suffixIcon,size:"18",customStyle:t.suffixIconStyle},null,8,["name","customStyle"])):e.createCommentVNode("",!0)]),!0)])])],6)}],["__scopeId","data-v-53087111"]]),ol={props:{model:{type:Object,default:()=>({})},rules:{type:[Object,Function,Array],default:()=>({})},errorType:{type:String,default:"message"},borderBottom:{type:Boolean,default:!0},labelPosition:{type:String,default:"left"},labelWidth:{type:[String,Number],default:45},labelAlign:{type:String,default:"left"},labelStyle:{type:Object,default:()=>({})},...null==(T=null==(E=uni.$uv)?void 0:E.props)?void 0:T.form}},rl=/%[sdj%]/g;let il=function(){};function al(e){if(!e||!e.length)return null;const t={};return e.forEach((e=>{const{field:n}=e;t[n]=t[n]||[],t[n].push(e)})),t}function sl(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];let o=1;const r=t[0],i=t.length;if("function"==typeof r)return r.apply(null,t.slice(1));if("string"==typeof r){let e=String(r).replace(rl,(e=>{if("%%"===e)return"%";if(o>=i)return e;switch(e){case"%s":return String(t[o++]);case"%d":return Number(t[o++]);case"%j":try{return JSON.stringify(t[o++])}catch(n){return"[Circular]"}break;default:return e}}));for(let n=t[o];o<i;n=t[++o])e+=` ${n}`;return e}return r}function ll(e,t){return null==e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"pattern"===e}(t)||"string"!=typeof e||e))}function cl(e,t,n){let o=0;const r=e.length;!function i(a){if(a&&a.length)return void n(a);const s=o;o+=1,s<r?t(e[s],i):n([])}([])}function ul(e,t,n,o){if(t.first){const t=new Promise(((t,r)=>{const i=function(e){const t=[];return Object.keys(e).forEach((n=>{t.push.apply(t,e[n])})),t}(e);cl(i,n,(function(e){return o(e),e.length?r({errors:e,fields:al(e)}):t()}))}));return t.catch((e=>e)),t}let r=t.firstFields||[];!0===r&&(r=Object.keys(e));const i=Object.keys(e),a=i.length;let s=0;const l=[],c=new Promise(((t,c)=>{const u=function(e){if(l.push.apply(l,e),s++,s===a)return o(l),l.length?c({errors:l,fields:al(l)}):t()};i.length||(o(l),t()),i.forEach((t=>{const o=e[t];-1!==r.indexOf(t)?cl(o,n,u):function(e,t,n){const o=[];let r=0;const i=e.length;function a(e){o.push.apply(o,e),r++,r===i&&n(o)}e.forEach((e=>{t(e,a)}))}(o,n,u)}))}));return c.catch((e=>e)),c}function dl(e){return function(t){return t&&t.message?(t.field=t.field||e.fullField,t):{message:"function"==typeof t?t():t,field:t.field||e.fullField}}}function hl(e,t){if(t)for(const n in t)if(t.hasOwnProperty(n)){const o=t[n];"object"==typeof o&&"object"==typeof e[n]?e[n]={...e[n],...o}:e[n]=o}return e}function pl(e,t,n,o,r,i){!e.required||n.hasOwnProperty(e.field)&&!ll(t,i||e.type)||o.push(sl(r.messages.required,e.fullField))}"undefined"!=typeof process&&process.env;const fl={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i};var ml={integer:function(e){return/^(-)?\d+$/.test(e)},float:function(e){return/^(-)?\d+(\.\d+)?$/.test(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(Er){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear},number:function(e){return!isNaN(e)&&"number"==typeof+e},object:function(e){return"object"==typeof e&&!ml.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&!!e.match(fl.email)&&e.length<255},url:function(e){return"string"==typeof e&&!!e.match(fl.url)},hex:function(e){return"string"==typeof e&&!!e.match(fl.hex)}};const gl={required:pl,whitespace:function(e,t,n,o,r){(/^\s+$/.test(t)||""===t)&&o.push(sl(r.messages.whitespace,e.fullField))},type:function(e,t,n,o,r){if(e.required&&void 0===t)return void pl(e,t,n,o,r);const i=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(i)>-1?ml[i](t)||o.push(sl(r.messages.types[i],e.fullField,e.type)):i&&typeof t!==e.type&&o.push(sl(r.messages.types[i],e.fullField,e.type))},range:function(e,t,n,o,r){const i="number"==typeof e.len,a="number"==typeof e.min,s="number"==typeof e.max,l=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;let c=t,u=null;const d="number"==typeof t,h="string"==typeof t,p=Array.isArray(t);if(d?u="number":h?u="string":p&&(u="array"),!u)return!1;p&&(c=t.length),h&&(c=t.replace(l,"_").length),i?c!==e.len&&o.push(sl(r.messages[u].len,e.fullField,e.len)):a&&!s&&c<e.min?o.push(sl(r.messages[u].min,e.fullField,e.min)):s&&!a&&c>e.max?o.push(sl(r.messages[u].max,e.fullField,e.max)):a&&s&&(c<e.min||c>e.max)&&o.push(sl(r.messages[u].range,e.fullField,e.min,e.max))},enum:function(e,t,n,o,r){e.enum=Array.isArray(e.enum)?e.enum:[],-1===e.enum.indexOf(t)&&o.push(sl(r.messages.enum,e.fullField,e.enum.join(", ")))},pattern:function(e,t,n,o,r){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||o.push(sl(r.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(t)||o.push(sl(r.messages.pattern.mismatch,e.fullField,t,e.pattern))}}};function yl(e,t,n,o,r){const i=e.type,a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t,i)&&!e.required)return n();gl.required(e,t,o,a,r,i),ll(t,i)||gl.type(e,t,o,a,r)}n(a)}const vl={string:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t,"string")&&!e.required)return n();gl.required(e,t,o,i,r,"string"),ll(t,"string")||(gl.type(e,t,o,i,r),gl.range(e,t,o,i,r),gl.pattern(e,t,o,i,r),!0===e.whitespace&&gl.whitespace(e,t,o,i,r))}n(i)},method:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),void 0!==t&&gl.type(e,t,o,i,r)}n(i)},number:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(""===t&&(t=void 0),ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),void 0!==t&&(gl.type(e,t,o,i,r),gl.range(e,t,o,i,r))}n(i)},boolean:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),void 0!==t&&gl.type(e,t,o,i,r)}n(i)},regexp:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),ll(t)||gl.type(e,t,o,i,r)}n(i)},integer:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),void 0!==t&&(gl.type(e,t,o,i,r),gl.range(e,t,o,i,r))}n(i)},float:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),void 0!==t&&(gl.type(e,t,o,i,r),gl.range(e,t,o,i,r))}n(i)},array:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t,"array")&&!e.required)return n();gl.required(e,t,o,i,r,"array"),ll(t,"array")||(gl.type(e,t,o,i,r),gl.range(e,t,o,i,r))}n(i)},object:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),void 0!==t&&gl.type(e,t,o,i,r)}n(i)},enum:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r),void 0!==t&&gl.enum(e,t,o,i,r)}n(i)},pattern:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t,"string")&&!e.required)return n();gl.required(e,t,o,i,r),ll(t,"string")||gl.pattern(e,t,o,i,r)}n(i)},date:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();if(gl.required(e,t,o,i,r),!ll(t)){let n;n="number"==typeof t?new Date(t):t,gl.type(e,n,o,i,r),n&&gl.range(e,n.getTime(),o,i,r)}}n(i)},url:yl,hex:yl,email:yl,required:function(e,t,n,o,r){const i=[],a=Array.isArray(t)?"array":typeof t;gl.required(e,t,o,i,r,a),n(i)},any:function(e,t,n,o,r){const i=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(ll(t)&&!e.required)return n();gl.required(e,t,o,i,r)}n(i)}};function bl(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){const e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}const wl=bl();function kl(e){this.rules=null,this._messages=wl,this.define(e)}kl.prototype={messages:function(e){return e&&(this._messages=hl(bl(),e)),this._messages},define:function(e){if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");let t,n;for(t in this.rules={},e)e.hasOwnProperty(t)&&(n=e[t],this.rules[t]=Array.isArray(n)?n:[n])},validate:function(e,t,n){const o=this;void 0===t&&(t={}),void 0===n&&(n=function(){});let r,i,a=e,s=t,l=n;if("function"==typeof s&&(l=s,s={}),!this.rules||0===Object.keys(this.rules).length)return l&&l(),Promise.resolve();if(s.messages){let e=this.messages();e===wl&&(e=bl()),hl(e,s.messages),s.messages=e}else s.messages=this.messages();const c={};(s.keys||Object.keys(this.rules)).forEach((t=>{r=o.rules[t],i=a[t],r.forEach((n=>{let r=n;"function"==typeof r.transform&&(a===e&&(a={...a}),i=a[t]=r.transform(i)),r="function"==typeof r?{validator:r}:{...r},r.validator=o.getValidationMethod(r),r.field=t,r.fullField=r.fullField||t,r.type=o.getType(r),r.validator&&(c[t]=c[t]||[],c[t].push({rule:r,value:i,source:a,field:t}))}))}));const u={};return ul(c,s,((e,t)=>{const{rule:n}=e;let o,r=!("object"!==n.type&&"array"!==n.type||"object"!=typeof n.fields&&"object"!=typeof n.defaultField);function i(e,t){return{...t,fullField:`${n.fullField}.${e}`}}function a(o){void 0===o&&(o=[]);let a=o;if(Array.isArray(a)||(a=[a]),!s.suppressWarning&&a.length&&kl.warning("async-validator:",a),a.length&&n.message&&(a=[].concat(n.message)),a=a.map(dl(n)),s.first&&a.length)return u[n.field]=1,t(a);if(r){if(n.required&&!e.value)return a=n.message?[].concat(n.message).map(dl(n)):s.error?[s.error(n,sl(s.messages.required,n.field))]:[],t(a);let o={};if(n.defaultField)for(const t in e.value)e.value.hasOwnProperty(t)&&(o[t]=n.defaultField);o={...o,...e.rule.fields};for(const e in o)if(o.hasOwnProperty(e)){const t=Array.isArray(o[e])?o[e]:[o[e]];o[e]=t.map(i.bind(null,e))}const r=new kl(o);r.messages(s.messages),e.rule.options&&(e.rule.options.messages=s.messages,e.rule.options.error=s.error),r.validate(e.value,e.rule.options||s,(e=>{const n=[];a&&a.length&&n.push.apply(n,a),e&&e.length&&n.push.apply(n,e),t(n.length?n:null)}))}else t(a)}r=r&&(n.required||!n.required&&e.value),n.field=e.field,n.asyncValidator?o=n.asyncValidator(n,e.value,a,e.source,s):n.validator&&(o=n.validator(n,e.value,a,e.source,s),!0===o?a():!1===o?a(n.message||`${n.field} fails`):o instanceof Array?a(o):o instanceof Error&&a(o.message)),o&&o.then&&o.then((()=>a()),(e=>a(e)))}),(e=>{!function(e){let t,n=[],o={};function r(e){if(Array.isArray(e)){let t;n=(t=n).concat.apply(t,e)}else n.push(e)}for(t=0;t<e.length;t++)r(e[t]);n.length?o=al(n):(n=null,o=null),l(n,o)}(e)}))},getType:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!vl.hasOwnProperty(e.type))throw new Error(sl("Unknown rule type %s",e.type));return e.type||"string"},getValidationMethod:function(e){if("function"==typeof e.validator)return e.validator;const t=Object.keys(e),n=t.indexOf("message");return-1!==n&&t.splice(n,1),1===t.length&&"required"===t[0]?vl.required:vl[this.getType(e)]||!1}},kl.register=function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");vl[e]=t},kl.warning=il,kl.messages=wl,kl.warning=function(){};const xl=Ve({name:"uv-form",mixins:[X,Ee,ol],provide(){return{uForm:this}},data:()=>({formRules:{},validator:{},originalModel:null}),watch:{rules:{immediate:!0,handler(e){this.setRules(e)}},propsChange(e){var t;(null==(t=this.children)?void 0:t.length)&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))},model:{immediate:!0,handler(e){this.originalModel||(this.originalModel=this.$uv.deepClone(e))}}},computed:{propsChange(){return[this.errorType,this.borderBottom,this.labelPosition,this.labelWidth,this.labelAlign,this.labelStyle]}},created(){this.children=[]},methods:{setRules(e){0!==Object.keys(e).length&&(this.formRules=e,this.validator=new kl(e))},resetFields(){this.resetModel()},resetModel(e){this.children.map((e=>{const t=null==e?void 0:e.prop,n=this.$uv.getProperty(this.originalModel,t);this.$uv.setProperty(this.model,t,n)}))},clearValidate(e){e=[].concat(e),this.children.map((t=>{(void 0===e[0]||e.includes(t.prop))&&(t.message=null)}))},async validateField(e,t,n=null){this.$nextTick((()=>{const o=[];e=[].concat(e),this.children.map((t=>{const r=[];if(e.includes(t.prop)){const e=this.$uv.getProperty(this.model,t.prop),i=t.prop.split("."),a=i[i.length-1],s=this.formRules[t.prop];if(!s)return;const l=[].concat(s);for(let c=0;c<l.length;c++){const i=l[c],s=[].concat(null==i?void 0:i.trigger);if(n&&!s.includes(n))continue;new kl({[a]:i}).validate({[a]:e},((e,n)=>{this.$uv.test.array(e)&&(o.push(...e),r.push(...e)),this.$nextTick((()=>{var e,n;t.message=(null==(e=r[0])?void 0:e.message)?null==(n=r[0])?void 0:n.message:null}))}))}}})),"function"==typeof t&&t(o)}))},validate(e){return new Promise(((e,t)=>{this.$nextTick((()=>{const n=this.children.map((e=>e.prop));this.validateField(n,(n=>{n.length?("toast"===this.errorType&&this.$uv.toast(n[0].message),t(n)):e(!0)}))}))}))}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"uv-form"},[e.renderSlot(t.$slots,"default")])}]]);const Sl=Ve({name:"tuiButton",emits:["click","getuserinfo","contact","getphonenumber","error"],props:{type:{type:String,default:"primary"},shadow:{type:Boolean,default:!1},width:{type:String,default:"100%"},height:{type:String,default:""},btnSize:{type:String,default:""},size:{type:[Number,String],default:0},bold:{type:Boolean,default:!1},margin:{type:String,default:"0"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},link:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},disabledGray:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},formType:{type:String,default:""},openType:{type:String,default:""},appParameter:{type:String,default:""},index:{type:[Number,String],default:0},preventClick:{type:Boolean,default:!1}},computed:{getWidth(){let e=this.width;return this.btnSize&&!0!==this.btnSize&&(e={medium:"368rpx",small:"240rpx",mini:"116rpx"}[this.btnSize]||this.width),e},getHeight(){let e=this.height||uni&&uni.$tui&&uni.$tui.tuiButton.height||"96rpx";return this.btnSize&&!0!==this.btnSize&&(e={medium:"80rpx",small:"80rpx",mini:"64rpx"}[this.btnSize]||"96rpx"),e},getSize(){return this.size||uni&&uni.$tui&&uni.$tui.tuiButton.size||32}},data:()=>({time:0}),methods:{hexToRGB(e){if(4===e.length){let t=e.substring(1,4);e="#"+t+t}let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:{}},getColorByType(e,t,n){const o=uni&&uni.$tui&&uni.$tui.color;let r="";const i={primary:o&&o.primary||"#5677fc",white:"#fff",danger:o&&o.danger||"#EB0909",warning:o&&o.warning||"#ff7900",green:o&&o.success||"#07c160",blue:o&&o.blue||"#007aff",gray:"#bfbfbf",black:"#333333",brown:"#ac9157","gray-primary":"#f2f2f2","gray-danger":"#f2f2f2","gray-warning":"#f2f2f2","gray-green":"#f2f2f2"};if(t)if(e&&~e.indexOf("gray-")){r=i[e.replace("gray-","")]}else r="white"===e?"#333":n?i[e]:"#fff";else r=i[e]||i.primary;return r},getShadow(e,t){const n=this.getColorByType(e);if(t||!n)return"none";const o=this.hexToRGB(n);return`0 10rpx 14rpx 0 rgba(${o.r}, ${o.g}, ${o.b}, 0.2)`},getBgColor(e,t){return t?"transparent":this.getColorByType(e)},getColor(e,t){return this.getColorByType(e,!0,t)},handleClick(){if(!this.disabled){if(this.preventClick){if((new Date).getTime()-this.time<=200)return;this.time=(new Date).getTime(),setTimeout((()=>{this.time=0}),200)}this.$emit("click",{index:Number(this.index)})}},bindgetuserinfo({detail:e={}}={}){this.$emit("getuserinfo",e)},bindcontact({detail:e={}}={}){this.$emit("contact",e)},bindgetphonenumber({detail:e={}}={}){this.$emit("getphonenumber",e)},binderror({detail:e={}}={}){this.$emit("error",e)},bindchooseavatar({detail:e={}}={}){this.$emit("chooseavatar",e)},bindlaunchapp({detail:e={}}={}){this.$emit("launchapp",e)},getDisabledClass:function(e,t,n){let o="";if(e&&"white"!=t&&-1==t.indexOf("-")){let e=this.disabledGray?"tui-gray-disabled":"tui-dark-disabled";o=n?"tui-dark-disabled-outline":e}return o},getShapeClass:function(e,t){let n="";return"circle"==e?n=t?"tui-outline-fillet":"tui-fillet":"rightAngle"==e&&(n=t?"tui-outline-rightAngle":"tui-rightAngle"),n},getHoverClass:function(e,t,n){let o="";return e||(o=n?"tui-outline-hover":"tui-"+(t||"primary")+"-hover"),o}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["tui-button__wrap",["100%"!==o.width&&o.width&&!0!==o.width||o.btnSize&&!0!==o.btnSize?"":"tui-btn__flex-1",a.getShapeClass(o.shape,o.plain),o.disabled?"":"tui-button__hover"]]),style:e.normalizeStyle({width:a.getWidth,height:a.getHeight,margin:o.margin})},[e.createElementVNode("button",{class:e.normalizeClass(["tui-btn",[o.plain?"tui-"+o.type+"-outline":"tui-btn-"+(o.type||"primary"),a.getDisabledClass(o.disabled,o.type,o.plain),a.getShapeClass(o.shape,o.plain),o.bold?"tui-text-bold":"",o.link?"tui-btn__link":""]]),style:e.normalizeStyle({width:a.getWidth,height:a.getHeight,lineHeight:a.getHeight,fontSize:a.getSize+"rpx",background:a.getBgColor(o.type,o.plain),color:a.getColor(o.type,o.plain),boxShadow:o.shadow?a.getShadow(o.type,o.plain):"none"}),loading:o.loading,"form-type":o.formType,"open-type":o.openType,"app-parameter":o.appParameter,onGetuserinfo:n[0]||(n[0]=(...e)=>a.bindgetuserinfo&&a.bindgetuserinfo(...e)),onGetphonenumber:n[1]||(n[1]=(...e)=>a.bindgetphonenumber&&a.bindgetphonenumber(...e)),onContact:n[2]||(n[2]=(...e)=>a.bindcontact&&a.bindcontact(...e)),onError:n[3]||(n[3]=(...e)=>a.binderror&&a.binderror(...e)),onChooseavatar:n[4]||(n[4]=(...e)=>a.bindchooseavatar&&a.bindchooseavatar(...e)),onLaunchapp:n[5]||(n[5]=(...e)=>a.bindlaunchapp&&a.bindlaunchapp(...e)),disabled:o.disabled,onClick:n[6]||(n[6]=(...e)=>a.handleClick&&a.handleClick(...e))},[e.renderSlot(t.$slots,"default",{},void 0,!0)],46,["loading","form-type","open-type","app-parameter","disabled"]),!o.link&&o.plain?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["tui-button__border",[a.getShapeClass(o.shape,o.plain),a.getDisabledClass(o.disabled,o.type,o.plain)]]),style:e.normalizeStyle({borderColor:a.getBgColor(o.type)})},null,6)):e.createCommentVNode("",!0)],6)}],["__scopeId","data-v-4320c879"]]),Cl={__name:"add-pop",emits:{ok:null},setup(t,{expose:n,emit:o}){const r=e.ref(),i=e.ref(),a=e.ref({scheduleTime:"",scheduleContent:""}),s=e.reactive({scheduleTime:[{type:"string",required:!0,message:"请选择时间"}],scheduleContent:[{type:"string",required:!0,message:"请输入日程描述"}]}),l=()=>{i.value.validate().then((e=>{var t;(t=a.value,ro({url:"/sys/index/schedule/add",method:"post",data:t})).then((e=>{o("ok",e),r.value.close(),a.value={}}))}))},c=()=>{r.value.close()};return n({onOpen:e=>{a.value.scheduleDate=e,r.value.open()}}),(t,n)=>{const o=ee(e.resolveDynamicComponent("uv-form-item"),Qs),u=ee(e.resolveDynamicComponent("uv-input"),nl),d=ee(e.resolveDynamicComponent("uv-form"),xl),h=ee(e.resolveDynamicComponent("tui-button"),Sl),p=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createBlock(p,{ref_key:"popRef",ref:r,mode:"bottom","bg-color":"null","z-index":"99"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"container"},[e.createElementVNode("view",{class:"close"},[e.createElementVNode("icon",{type:"clear",size:20,color:"#5677fc",onClick:c})]),e.createVNode(d,{ref_key:"formRef",ref:i,model:a.value,rules:s,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:e.withCtx((()=>[e.createVNode(o,{label:"时间",prop:"scheduleTime",required:!0},{default:e.withCtx((()=>[e.createElementVNode("picker",{style:{width:"100%"},mode:"time",value:a.value.scheduleTime,onChange:n[0]||(n[0]=e=>{a.value.scheduleTime=e.detail.value})},[e.createElementVNode("view",{class:"uni-input input-value-border"},e.toDisplayString(a.value.scheduleTime?a.value.scheduleTime:"请选择时间"),1)],40,["value"])])),_:1}),e.createVNode(o,{label:"日程描述",prop:"scheduleContent",required:!0},{default:e.withCtx((()=>[e.createVNode(u,{type:"textarea",modelValue:a.value.scheduleContent,"onUpdate:modelValue":n[1]||(n[1]=e=>a.value.scheduleContent=e),placeholder:"请输入日程描述"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),e.createVNode(h,{height:"90rpx",type:"primary",onClick:l},{default:e.withCtx((()=>[e.createTextVNode("确认")])),_:1})])])),_:1},512)}}},_l=Ve(Cl,[["__scopeId","data-v-fe458830"]]),Nl={__name:"index",setup(t){const n=e.ref([]),o=e.ref(uni.$xeu.toDateString(new Date,"yyyy-MM-dd")),r=e.ref(),i=()=>{var e;(e={scheduleDate:o.value},ro({url:"/sys/index/schedule/list",method:"get",data:e})).then((e=>{n.value=e.data}))};i();const a=e=>{o.value=e.fulldate,i()},s=()=>{r.value.onOpen(o)},l=e=>{const t=[{id:e.id}];var n;(n=t,ro({url:"/sys/index/schedule/deleteSchedule",method:"post",data:n})).then((()=>{i()}))};return(t,o)=>{const c=ee(e.resolveDynamicComponent("tui-section"),bo),u=ee(e.resolveDynamicComponent("uv-calendars"),Hs),d=ee(e.resolveDynamicComponent("uv-icon"),Ae),h=ee(e.resolveDynamicComponent("uv-col"),Ks),p=ee(e.resolveDynamicComponent("uv-row"),Gs);return e.openBlock(),e.createElementBlock("view",null,[e.createVNode(c,{padding:"20rpx 50rpx",title:"日程","is-line":"","line-cap":"square","line-right":20,background:"#fff",size:28}),e.createVNode(u,{insert:!0,lunar:!0,onChange:a,showMonth:!1}),e.createElementVNode("view",{class:"add-schedule snowy-bold",onClick:s}," 新增 "),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,(t=>(e.openBlock(),e.createElementBlock("view",{class:"item",key:t.id},[e.createVNode(p,null,{default:e.withCtx((()=>[e.createVNode(h,{span:"1"},{default:e.withCtx((()=>[e.createVNode(d,{size:"18",name:"clock-fill",color:"#5677fc",onClick:e=>l(t)},null,8,["onClick"])])),_:2},1024),e.createVNode(h,{span:"8"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left snowy-bold snowy-ellipsis"},e.toDisplayString(t.scheduleContent),1)])),_:2},1024),e.createVNode(h,{span:"2",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(t.scheduleTime),1)])),_:2},1024),e.createVNode(h,{span:"1"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"snowy-flex-end"},[e.createVNode(d,{size:"18",name:"trash-fill",color:"#e43d33",onClick:e=>l(t)},null,8,["onClick"])])])),_:2},1024)])),_:2},1024)])))),128)),e.createVNode(_l,{ref_key:"addPopRef",ref:r,onOk:o[0]||(o[0]=e=>i())},null,512)])}}},Dl=Ve(Nl,[["__scopeId","data-v-53fd4b3b"]]),El=Ve({__name:"item",props:{code:{type:String,default:"",required:!0},isShow:{type:Boolean,default:!1,required:!0}},setup:t=>(n,o)=>(e.openBlock(),e.createElementBlock("view",null,[["swiper"].includes(t.code)&&t.isShow?(e.openBlock(),e.createElementBlock("view",{key:0},[e.createVNode(vo)])):e.createCommentVNode("",!0),["chart"].includes(t.code)&&t.isShow?(e.openBlock(),e.createElementBlock("view",{key:1,class:"item snowy-shadow"},[e.createVNode(us)])):e.createCommentVNode("",!0),["schedule"].includes(t.code)&&t.isShow?(e.openBlock(),e.createElementBlock("view",{key:2,class:"item snowy-shadow"},[e.createVNode(Dl)])):e.createCommentVNode("",!0)]))},[["__scopeId","data-v-00906aba"]]),Tl={__name:"index",setup(t){const n=e.computed((()=>Zn.getters.homeConfigs));return(t,o)=>(e.openBlock(),e.createElementBlock("view",{class:"home-container"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(n),((t,n)=>(e.openBlock(),e.createBlock(El,{index:n,key:n,code:t.code,isShow:t.isShow},null,8,["index","code","isShow"])))),128))]))}},Bl=Ve({__name:"snowy-icon",props:{backgroundColor:{type:String,required:!1}},setup:t=>(n,o)=>{const r=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:"container-icon",style:e.normalizeStyle({backgroundColor:t.backgroundColor})},[e.createVNode(r,e.normalizeProps(e.guardReactiveProps(n.$attrs)),null,16)],4)}},[["__scopeId","data-v-685a9892"]]),Vl={props:{name:{type:[String,Number,null],default:null},bgColor:{type:String,default:"transparent"},...null==(V=null==(B=uni.$uv)?void 0:B.props)?void 0:V.gridItem}};const Il=Ve({name:"uv-grid-item",mixins:[X,Ee,Vl],emits:["$uvGridItem","click"],data:()=>({parentData:{col:3,border:!0},classes:[]}),created(){this.updateParentData()},mounted(){this.init()},computed:{width(){return 100/Number(this.parentData.col)+"%"},itemStyle(){const e={background:this.bgColor,width:this.width};return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},methods:{init(){uni.$on("$uvGridItem",(()=>{this.gridItemClasses()})),uni.$emit("$uvGridItem"),this.gridItemClasses()},updateParentData(){this.getParentData("uv-grid")},clickHandler(){var e;let t=this.name;const n=null==(e=this.parent)?void 0:e.children;n&&null===this.name&&(t=n.findIndex((e=>e===this))),this.parent&&this.parent.childClick(t),this.$emit("click",t)},async getItemWidth(){let e=0;if(this.parent){e=await this.getParentWidth()/Number(this.parentData.col)+"px"}this.width=e},getParentWidth(){},gridItemClasses(){if(this.parentData.border){let e=[];this.parent.children.map(((t,n)=>{if(this===t){const t=this.parent.children.length;(n+1)%this.parentData.col!=0&&n+1!==t&&e.push("uv-border-right");n<t-(t%this.parentData.col==0?this.parentData.col:t%this.parentData.col)&&e.push("uv-border-bottom")}})),this.classes=e}}},beforeDestroy(){uni.$off("$uvGridItem")}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-grid-item",i.classes]),"hover-class":"uv-grid-item--hover-class","hover-stay-time":200,onClick:n[0]||(n[0]=(...e)=>a.clickHandler&&a.clickHandler(...e)),style:e.normalizeStyle([a.itemStyle])},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)}],["__scopeId","data-v-800b7737"]]),Al={props:{col:{type:[String,Number],default:3},border:{type:Boolean,default:!1},align:{type:String,default:"left"},...null==(A=null==(I=uni.$uv)?void 0:I.props)?void 0:A.grid}};const Pl=Ve({name:"uv-grid",mixins:[X,Ee,Al],emits:["click"],data:()=>({index:0,width:0}),watch:{parentData(){this.children.length&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))}},created(){this.children=[]},computed:{parentData(){return[this.hoverClass,this.col,this.size,this.border]},gridStyle(){let e={};switch(this.align){case"left":default:e.justifyContent="flex-start";break;case"center":e.justifyContent="center";break;case"right":e.justifyContent="flex-end"}return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},methods:{childClick(e){this.$emit("click",e)}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"uv-grid",ref:"uv-grid",style:e.normalizeStyle([a.gridStyle])},[e.renderSlot(t.$slots,"default",{},void 0,!0)],4)}],["__scopeId","data-v-acf1a89f"]]),$l=Ve({__name:"index",setup(t){const{proxy:n}=e.getCurrentInstance(),o=e.reactive([{image:`${Zn.getters.allEnv[Zn.getters.envKey].baseUrl}/mobile/swiper/swiper1.jpg`},{image:`${Zn.getters.allEnv[Zn.getters.envKey].baseUrl}/mobile/swiper/swiper2.jpg`}]),r=Zn.getters.userMobileMenus;let i=e.reactive({}),a=e.reactive({});r&&r.length>0&&r.forEach((e=>{a[e.id]=[],a[e.id].push(e),i[e.id]=[]}));const s=(e,t)=>i[e]&&i[e].length>0?i[e]:t,l=(e,t)=>(e.key=t,e.key);return(t,c)=>{const u=ee(e.resolveDynamicComponent("uv-swiper"),yo),d=ee(e.resolveDynamicComponent("uv-icon"),Ae),h=ee(e.resolveDynamicComponent("uv-col"),Ks),p=ee(e.resolveDynamicComponent("uv-row"),Gs),f=ee(e.resolveDynamicComponent("snowy-icon"),Bl),m=ee(e.resolveDynamicComponent("uv-grid-item"),Il),g=ee(e.resolveDynamicComponent("uv-grid"),Pl);return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createVNode(u,{list:o,keyName:"image",indicator:"",indicatorMode:"line",circular:""},null,8,["list"]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(r),((t,o)=>(e.openBlock(),e.createElementBlock("view",{class:"item snowy-shadow",index:o,key:t.id,style:{"background-color":"#ffffff"}},[e.createVNode(p,{customStyle:"padding: 10px"},{default:e.withCtx((()=>[e.createVNode(h,{span:"0.8"},{default:e.withCtx((()=>[e.createVNode(d,{"custom-prefix":"snowy",name:t.icon,size:"16",color:t.color},null,8,["name","color"])])),_:2},1024),e.createVNode(h,{span:"4"},{default:e.withCtx((()=>[e.createTextVNode(e.toDisplayString(t.name),1)])),_:2},1024),e.createVNode(h,{span:"7.2",textAlign:"right"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(a)[t.id],((n,o)=>(e.openBlock(),e.createElementBlock("text",{key:o,style:e.normalizeStyle({marginLeft:"5px",color:o===e.unref(a)[t.id].length-1?"#8799a3":"#1890FF"}),onClick:e=>((e,t,n)=>{s(e.id,e.children).forEach((e=>{e.key=e.key+1})),i[n]=e.children,a[n].splice(t+1,a[n].length-t)})(n,o,t.id)},e.toDisplayString(n.name+(o===e.unref(a)[t.id].length-1?"":" | ")),13,["onClick"])))),128))])),_:2},1024)])),_:2},1024),e.createVNode(g,{border:!1,col:4},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s(t.id,t.children),((o,r)=>(e.openBlock(),e.createBlock(m,{index:r,key:l(o,r),onClick:e=>((e,t,o)=>{t.children&&t.children.length>0?(t.key=t.key+1,i[e]=t.children,a[e].push(t)):"MENU"==t.menuType?uni.navigateTo({url:t.path,fail(e){n.$modal.alert("请将【"+t.title+"】的移动端路由地址("+t.path+")与uniapp的page.json的path路径对应！"),Q("error","at pages/work/index.vue:82",e)}}):("IFRAME"==t.menuType||"LINK"==t.menuType)&&uni.navigateTo({url:`/pages/common/webview/index?url=${t.path}`})})(t.id,o)},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"grid-item-box"},[e.createVNode(f,{backgroundColor:o.color,"custom-prefix":"snowy",name:o.icon,size:"20",color:"#FFFFFF"},null,8,["backgroundColor","name"]),e.createElementVNode("text",{class:"text"},e.toDisplayString(o.title),1)])])),_:2},1032,["index","onClick"])))),128))])),_:2},1024)],8,["index"])))),128))])}}},[["__scopeId","data-v-a28852c8"]]);const Ol=Ve({name:"tuiTabs",emits:["change"],props:{tabs:{type:Array,default:()=>[]},width:{type:Number,default:0},height:{type:Number,default:80},padding:{type:Number,default:30},backgroundColor:{type:String,default:"#FFFFFF"},isFixed:{type:Boolean,default:!1},top:{type:Number,default:0},unlined:{type:Boolean,default:!1},currentTab:{type:Number,default:0},isSlider:{type:Boolean,default:!0},sliderWidth:{type:Number,default:68},sliderHeight:{type:Number,default:6},sliderBgColor:{type:String,default:""},sliderRadius:{type:String,default:"50rpx"},bottom:{type:String,default:"0"},itemWidth:{type:String,default:""},color:{type:String,default:"#666"},selectedColor:{type:String,default:""},size:{type:Number,default:28},bold:{type:Boolean,default:!1},scale:{type:[Number,String],default:1},badgeColor:{type:String,default:"#fff"},badgeBgColor:{type:String,default:""},zIndex:{type:[Number,String],default:996}},watch:{currentTab(){this.checkCor()},tabs(){this.checkCor()},width(e){this.tabsWidth=e,this.checkCor()}},computed:{getItemWidth(){let e=100/(this.tabs.length||4)+"%";return this.itemWidth?this.itemWidth:e},getSliderBgColor(){return this.sliderBgColor||uni&&uni.$tui&&uni.$tui.color.primary||"#5677fc"},getSelectedColor(){return this.selectedColor||uni&&uni.$tui&&uni.$tui.color.primary||"#5677fc"},getBadgeBgColor(){return this.badgeBgColor||uni&&uni.$tui&&uni.$tui.color.pink||"#f74d54"}},created(){setTimeout((()=>{uni.getSystemInfo({success:e=>{this.winWidth=e.windowWidth,this.tabsWidth=0==this.width?this.winWidth:this.width,this.checkCor()}})}),0)},data:()=>({winWidth:0,tabsWidth:0,scrollLeft:0}),methods:{checkCor:function(){let e=this.tabs.length,t=this.winWidth/750*this.padding,n=this.tabsWidth-2*t,o=(n/e-this.winWidth/750*this.sliderWidth)/2+t;this.currentTab>0&&(o+=n/e*this.currentTab),this.scrollLeft=o},swichTabs:function(e){let t=this.tabs[e];if(!t||!t.disabled)return this.currentTab!=e&&void this.$emit("change",{index:Number(e)})}}},[["render",function(t,n,o,r,i,a){return i.tabsWidth>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["tui-tabs-view",[o.isFixed?"tui-tabs-fixed":"tui-tabs-relative",o.unlined?"tui-unlined":""]]),style:e.normalizeStyle({width:i.tabsWidth+"px",height:o.height+"rpx",padding:`0 ${o.padding}rpx`,background:o.backgroundColor,top:o.isFixed?o.top+"px":"auto",zIndex:o.isFixed?o.zIndex:"auto"})},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.tabs,((t,n)=>(e.openBlock(),e.createElementBlock("view",{key:n,class:"tui-tabs-item",style:e.normalizeStyle({width:a.getItemWidth,height:o.height+"rpx"}),onClick:e.withModifiers((e=>a.swichTabs(n)),["stop"])},[e.createElementVNode("view",{class:e.normalizeClass(["tui-tabs-title",{"tui-tabs-disabled":t.disabled}]),style:e.normalizeStyle({color:o.currentTab==n?a.getSelectedColor:o.color,fontSize:o.size+"rpx",fontWeight:o.bold&&o.currentTab==n?"bold":"normal",transform:`scale(${o.currentTab==n?o.scale:1})`})},[e.createTextVNode(e.toDisplayString(t.name)+" ",1),t.num||t.isDot?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass([t.isDot?"tui-badge__dot":"tui-tabs__badge"]),style:e.normalizeStyle({color:o.badgeColor,backgroundColor:a.getBadgeBgColor})},e.toDisplayString(t.isDot?"":t.num),7)):e.createCommentVNode("",!0)],6)],12,["onClick"])))),128)),o.isSlider?(e.openBlock(),e.createElementBlock("view",{key:0,class:"tui-tabs-slider",style:e.normalizeStyle({transform:"translateX("+i.scrollLeft+"px)",width:o.sliderWidth+"rpx",height:o.sliderHeight+"rpx",borderRadius:o.sliderRadius,bottom:o.bottom,background:a.getSliderBgColor,marginBottom:"50%"==o.bottom?"-"+o.sliderHeight/2+"rpx":0})},null,4)):e.createCommentVNode("",!0)],6)):e.createCommentVNode("",!0)}],["__scopeId","data-v-b5ffa707"]]),Fl={props:{isDot:{type:Boolean,default:!1},value:{type:[Number,String],default:""},show:{type:Boolean,default:!0},max:{type:[Number,String],default:999},type:{type:[String,void 0,null],default:"error"},showZero:{type:Boolean,default:!1},bgColor:{type:[String,null],default:null},color:{type:[String,null],default:null},shape:{type:[String,void 0,null],default:"circle"},numberType:{type:[String,void 0,null],default:"overflow"},offset:{type:Array,default:()=>[]},inverted:{type:Boolean,default:!1},absolute:{type:Boolean,default:!1},...null==($=null==(P=uni.$uv)?void 0:P.props)?void 0:$.badge}};const Ml=Ve({name:"uv-badge",mixins:[X,Ee,Fl],computed:{boxStyle:()=>({}),badgeStyle(){const e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){const t=this.offset[0],n=this.offset[1]||t;e.top=this.$uv.addUnit(t),e.right=this.$uv.addUnit(n)}return e},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}},propsType(){return this.type||"error"}}},[["render",function(t,n,o,r,i,a){return t.show&&(0!==Number(t.value)||t.showZero||t.isDot)?(e.openBlock(),e.createElementBlock("text",{key:0,class:e.normalizeClass([[t.isDot?"uv-badge--dot":"uv-badge--not-dot",t.inverted&&"uv-badge--inverted","horn"===t.shape&&"uv-badge--horn",`uv-badge--${a.propsType}${t.inverted?"--inverted":""}`],"uv-badge"]),style:e.normalizeStyle([t.$uv.addStyle(t.customStyle),a.badgeStyle])},e.toDisplayString(t.isDot?"":a.showValue),7)):e.createCommentVNode("",!0)}],["__scopeId","data-v-d4ba6c2a"]]);const Ll=Ve({name:"tuiNoData",emits:["click"],props:{fixed:{type:Boolean,default:!0},imgUrl:{type:String,default:""},imgWidth:{type:[Number,String],default:200},imgHeight:{type:[Number,String],default:200},imgBottom:{type:[Number,String],default:30},btnWidth:{type:[Number,String],default:200},btnHeight:{type:[Number,String],default:60},btnText:{type:String,default:""},backgroundColor:{type:String,default:""},size:{type:[Number,String],default:28},radius:{type:String,default:"8rpx"},marginTop:{type:[Number,String],default:0}},computed:{getBgColor(){return this.backgroundColor||uni&&uni.$tui&&uni.$tui.color.danger||"#EB0909"}},methods:{handleClick(e){this.$emit("click",{})}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["tui-nodata-box",[o.fixed?"tui-nodata-fixed":""]]),style:e.normalizeStyle({marginTop:o.marginTop+"rpx"})},[o.imgUrl?(e.openBlock(),e.createElementBlock("image",{key:0,src:o.imgUrl,class:"tui-tips-icon",style:e.normalizeStyle({width:o.imgWidth+"rpx",height:o.imgHeight+"rpx",marginBottom:o.imgBottom+"rpx"}),mode:"widthFix"},null,12,["src"])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"tui-tips-content"},[e.renderSlot(t.$slots,"default",{},void 0,!0)]),o.btnText?(e.openBlock(),e.createElementBlock("view",{key:1,class:"tui-tips-btn",style:e.normalizeStyle({width:o.btnWidth+"rpx",height:o.btnHeight+"rpx",background:a.getBgColor,borderRadius:o.radius,fontSize:o.size+"rpx"}),onClick:n[0]||(n[0]=(...e)=>a.handleClick&&a.handleClick(...e))},e.toDisplayString(o.btnText),5)):e.createCommentVNode("",!0)],6)}],["__scopeId","data-v-9887b143"]]);const zl=Ve({},[["render",function(t,n){const o=ee(e.resolveDynamicComponent("tui-no-data"),Ll);return e.openBlock(),e.createBlock(o,e.mergeProps(t.$attrs,{fixed:!!t.$attrs.fixed&&t.$attrs.fixed,imgUrl:t.$attrs.imgUrl?t.$attrs.imgUrl:"/static/svg/empty/nodata.svg"}),{default:e.withCtx((()=>[e.renderSlot(t.$slots,"default",{},(()=>[e.createTextVNode("暂无数据")]))])),_:3},16,["fixed","imgUrl"])}]]),Rl={__name:"index",setup(t){const n=e.ref(0),o=e.ref([]),r=uni.$snowy.tool.dictList("MESSAGE_CATEGORY");uni.$xeu.isEmpty(r)||r.forEach((e=>{o.value.push({name:e.text})}));const i=e.reactive({}),a=e.reactive({current:1,size:10}),s=e.ref([]),l=e=>{var t,o;e&&(a.current=1,s.value=[]),i.category=uni.$xeu.isEmpty(r)?"":null==(t=r[n.value])?void 0:t.value,Object.assign(a,i),(o=a,ro({url:"/sys/userCenter/loginUnreadMessagePage",method:"get",data:o})).then((e=>{var t;uni.$xeu.isEmpty(null==(t=null==e?void 0:e.data)?void 0:t.records)||(s.value=s.value.concat(e.data.records),a.current++)})).finally((()=>{uni.stopPullDownRefresh()}))};ne((()=>{l(!0)})),ie((()=>{l(!0)})),re((()=>{l()}));return(t,r)=>{const i=ee(e.resolveDynamicComponent("tui-tabs"),Ol),a=ee(e.resolveDynamicComponent("uv-col"),Ks),c=ee(e.resolveDynamicComponent("uv-badge"),Ml),u=ee(e.resolveDynamicComponent("uv-row"),Gs),d=ee(e.resolveDynamicComponent("snowy-empty"),zl);return e.openBlock(),e.createElementBlock("view",null,[e.createVNode(i,{top:0,isFixed:!0,tabs:o.value,currentTab:n.value,onChange:r[0]||(r[0]=e=>{n.value!=e.index&&(n.value=e.index,l(!0))})},null,8,["tabs","currentTab"]),e.createElementVNode("view",{class:"msg-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"item",key:n,index:n,onClick:e=>(e=>{uni.navigateTo({url:`/pages/msg/detail?id=${e.id}&createTime=${e.createTime}`})})(t)},[e.createVNode(u,null,{default:e.withCtx((()=>[e.createVNode(a,{span:"11"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:e.normalizeClass(["item-title snowy-bold snowy-ellipsis",t.read?"item-grey":""])},e.toDisplayString(t.subject),3)])),_:2},1024),e.createVNode(a,{span:"1"},{default:e.withCtx((()=>[e.withDirectives(e.createElementVNode("view",{class:"snowy-flex-end"},[e.createVNode(c,{isDot:!0,type:"error"})],512),[[e.vShow,!t.read]])])),_:2},1024)])),_:2},1024),e.createVNode(u,{customStyle:"margin-top: 15rpx"},{default:e.withCtx((()=>[e.createVNode(a,{span:"12"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-sub-title snowy-ellipsis"},e.toDisplayString(t.createTime),1)])),_:2},1024)])),_:2},1024),e.createVNode(u,{customStyle:"margin-top: 15rpx"},{default:e.withCtx((()=>[e.createVNode(a,{span:"12"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-sub-title snowy-ellipsis"},e.toDisplayString(t.content),1)])),_:2},1024)])),_:2},1024)],8,["index","onClick"])))),128))]),e.withDirectives(e.createVNode(d,{fixed:!0},null,512),[[e.vShow,t.$xeu.isEmpty(s.value)]])])}}},Ul=Ve(Rl,[["__scopeId","data-v-4f58071f"]]),ql={props:{type:{type:String,default:"primary"},disabled:{type:[Boolean,String],default:!1},size:{type:String,default:"medium"},shape:{type:String,default:"square"},text:{type:[String,Number],default:""},bgColor:{type:String,default:""},color:{type:String,default:""},borderColor:{type:String,default:""},name:{type:[String,Number],default:""},plainFill:{type:Boolean,default:!1},plain:{type:Boolean,default:!1},closable:{type:Boolean,default:!1},closeColor:{type:String,default:"#C6C7CB"},closePlace:{type:String,default:"right-top"},show:{type:Boolean,default:!0},icon:{type:String,default:""},iconColor:{type:String,default:""},cellChild:{type:Boolean,default:!1},...null==(F=null==(O=uni.$uv)?void 0:O.props)?void 0:F.tags}};const jl=Ve({name:"uv-tags",emits:["click","close"],mixins:[X,Ee,ql],data:()=>({}),computed:{$uv:()=>({test:{image:de}}),style(){const e={};return this.bgColor&&(e.backgroundColor=this.bgColor),this.color&&(e.color=this.color),this.borderColor&&(e.borderColor=this.borderColor),e},textColor(){const e={};return this.color&&(e.color=this.color),e},imgStyle(){const e="large"===this.size?"17px":"medium"===this.size?"15px":"13px";return{width:e,height:e}},closeSize(){return"large"===this.size?15:"medium"===this.size?13:12},iconSize(){return"large"===this.size?21:"medium"===this.size?19:16},elIconColor(){return this.iconColor?this.iconColor:this.plain?this.type:"#ffffff"}},methods:{closeHandler(){this.$emit("close",this.name)},clickHandler(){this.$emit("click",this.name)}}},[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-icon"),Ae),l=ee(e.resolveDynamicComponent("uv-transition"),gs);return e.openBlock(),e.createBlock(l,{mode:"fade",show:t.show,"cell-child":t.cellChild},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"uv-tags-wrapper"},[e.createElementVNode("view",{class:e.normalizeClass(["uv-tags",[`uv-tags--${t.shape}`,!t.plain&&`uv-tags--${t.type}`,t.plain&&`uv-tags--${t.type}--plain`,`uv-tags--${t.size}`,`uv-tags--${t.size}--${t.closePlace}`,t.plain&&t.plainFill&&`uv-tags--${t.type}--plain--fill`]]),onClick:n[1]||(n[1]=e.withModifiers(((...e)=>a.clickHandler&&a.clickHandler(...e)),["stop"])),style:e.normalizeStyle([{marginRight:t.closable&&"right-top"==t.closePlace?"10px":0,marginTop:t.closable&&"right-top"==t.closePlace?"10px":0},a.style])},[e.renderSlot(t.$slots,"icon",{},(()=>[t.icon?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-tags__icon"},[a.$uv.test.image(t.icon)?(e.openBlock(),e.createElementBlock("image",{key:0,src:t.icon,style:e.normalizeStyle([a.imgStyle])},null,12,["src"])):(e.openBlock(),e.createBlock(s,{key:1,color:a.elIconColor,name:t.icon,size:a.iconSize},null,8,["color","name","size"]))])):e.createCommentVNode("",!0)]),!0),e.createElementVNode("text",{class:e.normalizeClass(["uv-tags__text",[`uv-tags__text--${t.type}`,t.plain&&`uv-tags__text--${t.type}--plain`,`uv-tags__text--${t.size}`]]),style:e.normalizeStyle([a.textColor])},e.toDisplayString(t.text),7),t.closable&&"right"==t.closePlace?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uv-tags__close",[`uv-tags__close--${t.size}`,`uv-tags__close--${t.closePlace}`]]),onClick:n[0]||(n[0]=e.withModifiers(((...e)=>a.closeHandler&&a.closeHandler(...e)),["stop"])),style:e.normalizeStyle({backgroundColor:t.closeColor})},[e.createVNode(s,{name:"close",size:a.closeSize,color:"#ffffff"},null,8,["size"])],6)):e.createCommentVNode("",!0)],6),t.closable&&"right-top"==t.closePlace?(e.openBlock(),e.createElementBlock("view",{key:0,class:e.normalizeClass(["uv-tags__close",[`uv-tags__close--${t.size}`,`uv-tags__close--${t.closePlace}`]]),onClick:n[2]||(n[2]=e.withModifiers(((...e)=>a.closeHandler&&a.closeHandler(...e)),["stop"])),style:e.normalizeStyle({backgroundColor:t.closeColor})},[e.createVNode(s,{name:"close",size:a.closeSize,color:"#ffffff"},null,8,["size"])],6)):e.createCommentVNode("",!0)])])),_:3},8,["show","cell-child"])}],["__scopeId","data-v-698402db"]]),Hl=Ve({__name:"detail",setup(t){const n=e.ref({}),o=e.ref([]);return oe((e=>{var t;e.id&&(t={id:e.id},ro({url:"/sys/userCenter/loginUnreadMessageDetail",method:"get",data:t})).then((t=>{n.value=t.data,n.value.createTime=null==e?void 0:e.createTime,o.value=t.data.receiveInfoList}))})),(t,r)=>{const i=ee(e.resolveDynamicComponent("uv-col"),Ks),a=ee(e.resolveDynamicComponent("uv-row"),Gs),s=ee(e.resolveDynamicComponent("uv-tags"),jl);return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"12"},{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"item-title snowy-bold"},e.toDisplayString(null==(t=n.value)?void 0:t.subject),1)]})),_:1})])),_:1}),e.createVNode(a,{customStyle:"margin-top: 20rpx"},{default:e.withCtx((()=>[e.createVNode(i,{span:"12"},{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"item-time"},e.toDisplayString(null==(t=n.value)?void 0:t.createTime),1)]})),_:1})])),_:1}),e.createVNode(a,{customStyle:"margin-top: 20rpx"},{default:e.withCtx((()=>[e.createVNode(i,{span:"12"},{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"item-content"},e.toDisplayString(null==(t=n.value)?void 0:t.content),1)]})),_:1})])),_:1})]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"item",key:n},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"6"},{default:e.withCtx((()=>[e.createElementVNode("view",null,"姓名")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",null,e.toDisplayString(t.receiveUserName),1)])),_:2},1024)])),_:2},1024),e.createVNode(a,{customStyle:"margin-top: 20rpx"},{default:e.withCtx((()=>[e.createVNode(i,{span:"6"},{default:e.withCtx((()=>[e.createElementVNode("view",null,"是否已读")])),_:1}),e.createVNode(i,{span:"6"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"snowy-flex-end"},[t.read?(e.openBlock(),e.createBlock(s,{key:0,text:"已读",type:"info"})):e.createCommentVNode("",!0),t.read?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(s,{key:1,text:"未读",type:"primary"}))])])),_:2},1024)])),_:2},1024)])))),128))])}}},[["__scopeId","data-v-772fa7ba"]]),Wl=Ve({__name:"index",setup(t){const n=e.computed((()=>Zn.getters.userInfo)),o=()=>{uni.navigateTo({url:"/pages/mine/info/index"})},r=()=>{uni.navigateTo({url:"/pages/mine/info/edit"})},i=()=>{uni.navigateTo({url:"/pages/mine/pwd/index"})},a=()=>{uni.navigateTo({url:"/pages/mine/home-config/index"})},s=()=>{uni.$snowy.modal.confirm("确定注销并退出系统吗？").then((()=>{Zn.dispatch("LogOut").then((()=>{uni.$snowy.tab.reLaunch(dt.NO_TOKEN_BACK_URL)}))}))};return(t,l)=>{const c=ee(e.resolveDynamicComponent("uv-col"),Ks),u=ee(e.resolveDynamicComponent("uv-icon"),Ae),d=ee(e.resolveDynamicComponent("uv-row"),Gs),h=ee(e.resolveDynamicComponent("snowy-icon"),Bl),p=ee(e.resolveDynamicComponent("uv-grid-item"),Il),f=ee(e.resolveDynamicComponent("uv-grid"),Pl);return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"header snowy-shadow"},[e.createVNode(d,null,{default:e.withCtx((()=>[e.createVNode(c,{span:"2.5"},{default:e.withCtx((()=>{var t,o;return[(null==(t=e.unref(n))?void 0:t.avatar)?(e.openBlock(),e.createElementBlock("image",{key:0,class:"avatar",src:null==(o=e.unref(n))?void 0:o.avatar,mode:"aspectFit"},null,8,["src"])):e.createCommentVNode("",!0)]})),_:1}),e.createVNode(c,{span:"4"},{default:e.withCtx((()=>{var t;return[e.createElementVNode("view",{class:"header-left"},e.toDisplayString(null==(t=e.unref(n))?void 0:t.name),1)]})),_:1}),e.createVNode(c,{span:"5",textAlign:"right",onClick:o},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"header-right"},"个人信息")])),_:1}),e.createVNode(c,{span:"0.5",onClick:o},{default:e.withCtx((()=>[e.createVNode(u,{name:"arrow-right",color:"white",size:"15"})])),_:1})])),_:1})]),e.createElementVNode("view",{class:"grid snowy-shadow"},[e.createVNode(f,{border:!1,col:4},{default:e.withCtx((()=>[e.createVNode(p,{onClick:a},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"grid-item-box"},[e.createVNode(h,{backgroundColor:"#ff9900",name:"home-fill",size:"20",color:"#FFFFFF"}),e.createElementVNode("text",{class:"text"},"首页设置")])])),_:1}),e.createVNode(p,{onClick:a},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"grid-item-box"},[e.createVNode(h,{backgroundColor:"#ff9900",name:"home-fill",size:"20",color:"#FFFFFF"}),e.createElementVNode("text",{class:"text"},"首页设置")])])),_:1}),e.createVNode(p,{onClick:a},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"grid-item-box"},[e.createVNode(h,{backgroundColor:"#ff9900",name:"home-fill",size:"20",color:"#FFFFFF"}),e.createElementVNode("text",{class:"text"},"首页设置")])])),_:1}),e.createVNode(p,{onClick:a},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"grid-item-box"},[e.createVNode(h,{backgroundColor:"#ff9900",name:"home-fill",size:"20",color:"#FFFFFF"}),e.createElementVNode("text",{class:"text"},"首页设置")])])),_:1})])),_:1})]),e.createElementVNode("view",{class:"menu snowy-shadow"},[e.createVNode(d,{onClick:r},{default:e.withCtx((()=>[e.createVNode(c,{span:"1"},{default:e.withCtx((()=>[e.createVNode(u,{name:"account-fill",color:"#2979ff",size:"20"})])),_:1}),e.createVNode(c,{span:"10.5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"menu-left"},"编辑资料")])),_:1}),e.createVNode(c,{span:"0.5"},{default:e.withCtx((()=>[e.createVNode(u,{name:"arrow-right",color:"#2979ff",size:"15"})])),_:1})])),_:1})]),e.createElementVNode("view",{class:"menu snowy-shadow"},[e.createVNode(d,{onClick:i},{default:e.withCtx((()=>[e.createVNode(c,{span:"1"},{default:e.withCtx((()=>[e.createVNode(u,{name:"lock-fill",color:"#fa3534",size:"20"})])),_:1}),e.createVNode(c,{span:"10.5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"menu-left"},"修改密码")])),_:1}),e.createVNode(c,{span:"0.5"},{default:e.withCtx((()=>[e.createVNode(u,{name:"arrow-right",color:"#2979ff",size:"15"})])),_:1})])),_:1})]),e.createElementVNode("view",{class:"menu snowy-shadow"},[e.createVNode(d,{onClick:s},{default:e.withCtx((()=>[e.createVNode(c,{span:"1"},{default:e.withCtx((()=>[e.createVNode(u,{name:"warning-fill",color:"#ff9900",size:"20"})])),_:1}),e.createVNode(c,{span:"10.5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"menu-left"},"退出登录")])),_:1}),e.createVNode(c,{span:"0.5"},{default:e.withCtx((()=>[e.createVNode(u,{name:"arrow-right",color:"#2979ff",size:"15"})])),_:1})])),_:1})])],64)}}},[["__scopeId","data-v-93f5a2b4"]]),Kl={__name:"snowy-search",props:{modelValue:[String,Array]},emits:["update:modelValue","confirm","clear","seniorSearch"],setup(t,{emit:n}){const o=t,r=e.ref("");e.watch((()=>o.modelValue),((e,t)=>{r.value=e}),{deep:!1,immediate:!0});const i=e.ref(!1),a=e=>{r.value=e.detail.value,i.value=!0,n("update:modelValue",r.value)},s=()=>{r.value="",i.value=!1,uni.hideKeyboard(),n("update:modelValue",r.value),n("clear")},l=()=>{n("update:modelValue",r.value),n("confirm",r.value)},c=()=>{n("seniorSearch")};return(t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"search-bar"},[e.createElementVNode("view",{class:"search-bar-form"},[e.createElementVNode("view",{class:"search-bar-box"},[e.createElementVNode("icon",{type:"search",size:16}),e.createElementVNode("input",{class:"search-bar-input","confirm-type":"search",placeholder:t.$attrs.placeholder?t.$attrs.placeholder:"请输入搜索关键词","placeholder-class":"phcolor",value:r.value,onInput:a,onConfirm:l},null,40,["placeholder","value"]),i.value?(e.openBlock(),e.createElementBlock("icon",{key:0,type:"clear",size:15,onClick:s})):e.createCommentVNode("",!0)])]),t.$attrs.enableSenior?(e.openBlock(),e.createElementBlock("view",{key:0,class:"senior",onClick:c},[e.renderSlot(t.$slots,"default",{},(()=>[e.createElementVNode("text",{style:{color:"#5677fc","font-weight":"bold","text-shadow":"2px 2px 4px #ccc"}},"高级\\n搜索")]),!0)])):e.createCommentVNode("",!0)]))}},Yl=Ve(Kl,[["__scopeId","data-v-201ff995"]]),Gl=Ve({__name:"snowy-float-btn",props:{buttonText:{type:String,required:!1,default:"+"}},emits:["click"],setup(t,{emit:n}){const o=()=>{n("click")};return(t,n)=>{const r=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:"floating-button",onClick:o},[e.renderSlot(t.$slots,"default",{},(()=>[e.createVNode(r,{name:"plus",color:"#ffffff",size:"20"})]),!0)])}}},[["__scopeId","data-v-d78192c0"]]);const Jl=Ve({name:"tuiListCell",emits:["click"],props:{arrow:{type:Boolean,default:!1},arrowColor:{type:String,default:""},hover:{type:Boolean,default:!0},unlined:{type:Boolean,default:!1},lineColor:{type:String,default:""},lineLeft:{type:[Number,String],default:-1},lineRight:{type:[Number,String],default:0},padding:{type:String,default:""},marginTop:{type:[Number,String],default:0},marginBottom:{type:[Number,String],default:0},backgroundColor:{type:String,default:"#fff"},size:{type:Number,default:0},color:{type:String,default:""},radius:{type:[Number,String],default:0},arrowRight:{type:[Number,String],default:30},index:{type:Number,default:0}},computed:{getArrowColor(){return this.arrowColor||uni&&uni.$tui&&uni.$tui.tuiListCell.arrowColor||"#c0c0c0"},getLineColor(){return this.lineColor||uni&&uni.$tui&&uni.$tui.tuiListCell.lineColor||"#eaeef1"},getLineLeft(){let e=this.lineLeft;return-1===e&&(e=uni&&uni.$tui&&uni.$tui.tuiListCell.lineLeft),null==e?30:e},getPadding(){return this.padding||uni&&uni.$tui&&uni.$tui.tuiListCell.padding||"26rpx 30rpx"},getColor(){return this.color||uni&&uni.$tui&&uni.$tui.tuiListCell.color||"#333"},getSize(){return this.size||uni&&uni.$tui&&uni.$tui.tuiListCell.size||28}},methods:{handleClick(){this.$emit("click",{index:this.index})}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["tui-list-class tui-list-cell",[o.radius&&"0"!=o.radius?"tui-radius":"",o.hover?"tui-cell-hover":""]]),style:e.normalizeStyle({backgroundColor:o.backgroundColor,fontSize:a.getSize+"rpx",color:a.getColor,padding:a.getPadding,borderRadius:o.radius+"rpx",marginTop:o.marginTop+"rpx",marginBottom:o.marginBottom+"rpx"}),onClick:n[0]||(n[0]=(...e)=>a.handleClick&&a.handleClick(...e))},[e.renderSlot(t.$slots,"default",{},void 0,!0),o.unlined?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"tui-cell__line",style:e.normalizeStyle({borderBottomColor:a.getLineColor,left:a.getLineLeft+"rpx",right:o.lineRight+"rpx"})},null,4)),o.arrow?(e.openBlock(),e.createElementBlock("view",{key:1,class:"tui-cell__arrow",style:e.normalizeStyle({borderColor:a.getArrowColor,right:o.arrowRight+"rpx"})},null,4)):e.createCommentVNode("",!0)],6)}],["__scopeId","data-v-fccf1d93"]]);const Zl=Ve({name:"tuiListView",props:{title:{type:String,default:""},color:{type:String,default:"#666"},size:{type:Number,default:30},backgroundColor:{type:String,default:"transparent"},unlined:{type:String,default:""},marginTop:{type:String,default:"0"},radius:{type:[Number,String],default:0}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["tui-list-view tui-view-class",{"tui-radius":o.radius&&"0"!=o.radius}]),style:e.normalizeStyle({backgroundColor:o.backgroundColor,marginTop:o.marginTop,borderRadius:o.radius+"rpx"})},[o.title?(e.openBlock(),e.createElementBlock("view",{key:0,class:"tui-list-title",style:e.normalizeStyle({color:o.color,fontSize:o.size+"rpx",lineHeight:"30rpx"})},e.toDisplayString(o.title),5)):e.createCommentVNode("",!0),e.createElementVNode("view",{class:e.normalizeClass(["tui-list-content",[o.unlined?"tui-border-"+o.unlined:""]])},[e.renderSlot(t.$slots,"default",{},void 0,!0)],2)],6)}],["__scopeId","data-v-0dd6d04a"]]),Xl=Ve({__name:"more",emits:["handleOk"],setup(t,{expose:n,emit:o}){const r=e.ref(),i=e.ref({}),a=()=>{uni.navigateTo({url:"/pages/config/form?record="+encodeURIComponent(JSON.stringify(i.value))}),r.value.close()},s=()=>{uni.$snowy.modal.confirm(`是否确认删除【${i.value.name}】环境？`).then((()=>{let e=uni.$xeu.clone(Zn.getters.allEnv,!0);delete e[i.value.key],Zn.commit("SET_allEnv",e),r.value.close()}))},l=()=>{r.value.close()};return n({open:e=>{i.value=e,r.value.open()}}),(t,n)=>{const o=ee(e.resolveDynamicComponent("tui-list-cell"),Jl),c=ee(e.resolveDynamicComponent("tui-list-view"),Zl),u=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createBlock(u,{ref_key:"popRef",ref:r,mode:"bottom","bg-color":"null","z-index":"99"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"container"},[e.createVNode(c,{unlined:"all","background-color":"transparent"},{default:e.withCtx((()=>[e.createVNode(o,{hover:!0,arrow:!1,onClick:a,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 编辑 ")])),_:1}),e.unref(Zn).getters.envKey!=i.value.key?(e.openBlock(),e.createBlock(o,{key:0,hover:!0,arrow:!1,onClick:s,radius:10,"margin-top":2},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 刪除 ")])),_:1})):e.createCommentVNode("",!0),e.createVNode(o,{hover:!0,arrow:!1,onClick:l,"margin-top":10,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 取消 ")])),_:1})])),_:1})])])),_:1},512)}}},[["__scopeId","data-v-d0149e43"]]),Ql=Ve({__name:"index",setup(t){const n=e.ref(""),o=e.computed((()=>Zn.getters.envKey)),r=e.computed((()=>Zn.getters.allEnv)),i=e=>{const t={};return uni.$xeu.objectEach(e,((e,o)=>{n.value&&-1==e.name.indexOf(n.value)||(t[o]=e)})),t},a=()=>{Zn.commit("SET_envKey",Gn.DEFAULT_ENV_KEY),Zn.commit("SET_allEnv",Gn.DEFAULT_ALL_ENV)},s=()=>{uni.reLaunch({url:"/pages/login"})},l=()=>{uni.navigateTo({url:"/pages/config/form"})};return(t,c)=>{const u=ee(e.resolveDynamicComponent("snowy-search"),Yl),d=ee(e.resolveDynamicComponent("uv-icon"),Ae),h=ee(e.resolveDynamicComponent("uv-col"),Ks),p=ee(e.resolveDynamicComponent("uv-row"),Gs),f=ee(e.resolveDynamicComponent("tui-button"),Sl),m=ee(e.resolveDynamicComponent("snowy-float-btn"),Gl);return e.openBlock(),e.createElementBlock("view",null,[e.createVNode(u,{modelValue:n.value,"onUpdate:modelValue":c[0]||(c[0]=e=>n.value=e),enableSenior:!0},{default:e.withCtx((()=>[e.createElementVNode("view",{onClick:a},[e.createElementVNode("text",null," 重置\\n环境 ")])])),_:1},8,["modelValue"]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i(e.unref(r)),((n,r)=>(e.openBlock(),e.createElementBlock("view",{class:"item",key:r,index:r},[e.createElementVNode("view",{onClick:e=>(e=>{Zn.commit("SET_envKey",e)})(r),class:"item"},[e.createVNode(p,null,{default:e.withCtx((()=>[e.createVNode(h,{span:"6"},{default:e.withCtx((()=>[r===e.unref(o)?(e.openBlock(),e.createBlock(d,{key:0,size:"20",name:"integral-fill",color:"#007AFF"})):(e.openBlock(),e.createBlock(d,{key:1,size:"20",name:"integral"}))])),_:2},1024),e.createVNode(h,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(n.name),1)])),_:2},1024)])),_:2},1024)],8,["onClick"]),e.createElementVNode("view",{onClick:e=>t.$refs.moreRef.open({key:r,...n}),class:"item"},[e.createVNode(p,{customStyle:"margin-top: 15rpx"},{default:e.withCtx((()=>[e.createVNode(h,{span:"6"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"key：")])),_:1}),e.createVNode(h,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(r),1)])),_:2},1024)])),_:2},1024),e.createVNode(p,{customStyle:"margin-top: 15rpx"},{default:e.withCtx((()=>[e.createVNode(h,{span:"6"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"baseUrl：")])),_:1}),e.createVNode(h,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(n.baseUrl),1)])),_:2},1024)])),_:2},1024),e.createVNode(p,{customStyle:"margin-top: 15rpx"},{default:e.withCtx((()=>[e.createVNode(h,{span:"6"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"tenant：")])),_:1}),e.createVNode(h,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(n.tenantDomain),1)])),_:2},1024)])),_:2},1024)],8,["onClick"])],8,["index"])))),128)),e.createVNode(f,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:s},{default:e.withCtx((()=>[e.createTextVNode("确认")])),_:1}),e.createVNode(m,{onClick:l}),e.createVNode(Xl,{ref:"moreRef"},null,512)])}}},[["__scopeId","data-v-987b3966"]]),ec=Ve({__name:"form",setup(t){const n=e.ref(),o=e.ref({key:"",name:"",baseUrl:"",tenantDomain:""}),r=e.reactive({key:[{type:"string",required:!0,message:"请输入环境key",trigger:["blur","change"]}],name:[{type:"string",required:!0,message:"请输入环境名称",trigger:["blur","change"]}],baseUrl:[{type:"string",required:!0,message:"请输入baseUrl",trigger:["blur","change"]}]});oe((e=>{e.record&&(o.value=JSON.parse(decodeURIComponent(e.record)))}));const i=()=>{n.value.validate().then((e=>{let t=uni.$xeu.clone(Zn.getters.allEnv,!0);t[o.value.key]={name:o.value.name,baseUrl:o.value.baseUrl,tenantDomain:o.value.tenantDomain},Zn.commit("SET_allEnv",t),uni.navigateBack({delta:1})}))};return(t,a)=>{const s=ee(e.resolveDynamicComponent("uv-input"),nl),l=ee(e.resolveDynamicComponent("uv-form-item"),Qs),c=ee(e.resolveDynamicComponent("uv-form"),xl),u=ee(e.resolveDynamicComponent("tui-button"),Sl);return e.openBlock(),e.createElementBlock("view",{class:"container snowy-shadow"},[e.createVNode(c,{ref_key:"formRef",ref:n,model:o.value,rules:r,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:e.withCtx((()=>[e.createVNode(l,{label:"环境KEY",prop:"key",required:""},{default:e.withCtx((()=>[e.createVNode(s,{modelValue:o.value.key,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value.key=e),placeholder:"请输入环境key"},null,8,["modelValue"])])),_:1}),e.createVNode(l,{label:"环境名称",prop:"name",required:""},{default:e.withCtx((()=>[e.createVNode(s,{modelValue:o.value.name,"onUpdate:modelValue":a[1]||(a[1]=e=>o.value.name=e),placeholder:"请输入环境名称"},null,8,["modelValue"])])),_:1}),e.createVNode(l,{label:"baseUrl",prop:"baseUrl",required:""},{default:e.withCtx((()=>[e.createVNode(s,{modelValue:o.value.baseUrl,"onUpdate:modelValue":a[2]||(a[2]=e=>o.value.baseUrl=e),placeholder:"请输入baseUrl"},null,8,["modelValue"])])),_:1}),e.createVNode(l,{label:"tenantDomain",prop:"tenantDomain",required:""},{default:e.withCtx((()=>[e.createVNode(s,{modelValue:o.value.tenantDomain,"onUpdate:modelValue":a[3]||(a[3]=e=>o.value.tenantDomain=e),placeholder:"请输入tenantDomain"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),e.createVNode(u,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:i},{default:e.withCtx((()=>[e.createTextVNode("提交")])),_:1})])}}},[["__scopeId","data-v-e48ce3a5"]]),tc={__name:"index",setup(t){const n=e.ref({}),o=e.ref({progress:{color:"#FF3333"}});return oe((e=>{n.value=e,e.title&&uni.setNavigationBarTitle({title:e.title})})),(t,r)=>n.value.url?(e.openBlock(),e.createElementBlock("view",{key:0},[e.createElementVNode("web-view",{"webview-styles":o.value,src:`${n.value.url}`},null,8,["webview-styles","src"])])):e.createCommentVNode("",!0)}};const nc=Ve({name:"tuiSwipeAction",emits:["click"],props:{actions:{type:Array,default:()=>[]},closable:{type:Boolean,default:!0},showMask:{type:Boolean,default:!0},operateWidth:{type:Number,default:80},params:{type:Object,default:()=>({})},forbid:{type:Boolean,default:!1},open:{type:Boolean,default:!1},backgroundColor:{type:String,default:"#fff"}},watch:{actions(e,t){this.updateButtonSize()},open(e){this.manualSwitch(e)}},data:()=>({tStart:{pageX:0,pageY:0},limitMove:0,position:{pageX:0,pageY:0},isShowBtn:!1,move:!1}),mounted(){this.updateButtonSize()},methods:{swipeDirection:(e,t,n,o)=>Math.abs(e-t)>=Math.abs(n-o)?e-t>0?"Left":"Right":n-o>0?"Up":"Down",loop(){},updateButtonSize(){const e=this.actions;if(e.length>0){uni.createSelectorQuery().in(this);let t=0;e.forEach((e=>{t+=e.width||0})),this.limitMove=t}else this.limitMove=this.operateWidth},handlerTouchstart(e){if(this.forbid)return;let t=e.touches;if(t&&t.length>1)return;this.move=!0,t=t?e.touches[0]:{},(!t||void 0===t.pageX&&void 0===t.pageY)&&(t={pageX:e.pageX,pageY:e.pageY});const n=this.tStart;if(t)for(let o in n)t[o]&&(n[o]=t[o])},swipper(e){const t=this.tStart,n={pageX:e.pageX-t.pageX,pageY:e.pageY-t.pageY};this.limitMove<Math.abs(n.pageX)&&(n.pageX=-this.limitMove),this.position=n},handlerTouchmove(e){if(this.forbid||!this.move)return;const t=this.tStart;let n=e.touches?e.touches[0]:{};if((!n||void 0===n.pageX&&void 0===n.pageY)&&(n={pageX:e.pageX,pageY:e.pageY}),n){"Left"===this.swipeDirection(t.pageX,n.pageX,t.pageY,n.pageY)&&Math.abs(this.position.pageX)!==this.limitMove&&this.swipper(n)}},handlerTouchend(e){if(this.forbid||!this.move)return;this.move=!1;const t=this.tStart;let n=e.changedTouches?e.changedTouches[0]:{};if((!n||void 0===n.pageX&&void 0===n.pageY)&&(n={pageX:e.pageX,pageY:e.pageY}),n){const e=this.swipeDirection(t.pageX,n.pageX,t.pageY,n.pageY),o={pageX:n.pageX-t.pageX,pageY:n.pageY-t.pageY};Math.abs(o.pageX)>=40&&"Left"===e?(o.pageX=o.pageX<0?-this.limitMove:this.limitMove,this.isShowBtn=!0):o.pageX=0,0==o.pageX&&(this.isShowBtn=!1),this.position=o}},handlerButton(e){this.closable&&this.closeButtonGroup();const t=e.currentTarget.dataset;this.$emit("click",{index:Number(t.index),item:this.params})},closeButtonGroup(){this.position={pageX:0,pageY:0},this.isShowBtn=!1},handlerParentButton(e){this.closable&&this.closeButtonGroup()},manualSwitch(e){let t=0;if(e)if(0===this.actions.length)t=this.operateWidth;else{let e=0;this.actions.forEach((t=>{e+=t.width})),t=e}this.position={pageX:-t,pageY:0}},px:e=>uni.upx2px(e)+"px"}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:"tui-swipeout-wrap",style:e.normalizeStyle({backgroundColor:o.backgroundColor})},[e.createElementVNode("view",{class:e.normalizeClass(["tui-swipeout-item",[i.isShowBtn?"swipe-action-show":""]]),style:e.normalizeStyle({transform:"translate("+i.position.pageX+"px,0)"})},[e.createElementVNode("view",{class:"tui-swipeout-content",onTouchstart:n[0]||(n[0]=(...e)=>a.handlerTouchstart&&a.handlerTouchstart(...e)),onTouchmove:n[1]||(n[1]=(...e)=>a.handlerTouchmove&&a.handlerTouchmove(...e)),onTouchend:n[2]||(n[2]=(...e)=>a.handlerTouchend&&a.handlerTouchend(...e)),onMousedown:n[3]||(n[3]=(...e)=>a.handlerTouchstart&&a.handlerTouchstart(...e)),onMousemove:n[4]||(n[4]=(...e)=>a.handlerTouchmove&&a.handlerTouchmove(...e)),onMouseup:n[5]||(n[5]=(...e)=>a.handlerTouchend&&a.handlerTouchend(...e))},[e.renderSlot(t.$slots,"content",{},void 0,!0)],32),o.actions.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"tui-swipeout-button-right-group",onTouchend:n[7]||(n[7]=e.withModifiers(((...e)=>a.loop&&a.loop(...e)),["stop"]))},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.actions,((t,o)=>(e.openBlock(),e.createElementBlock("view",{class:"tui-swipeout-button-right-item",key:o,style:e.normalizeStyle({backgroundColor:t.background||"#f7f7f7",color:t.color,width:t.width+"px"}),"data-index":o,onClick:n[6]||(n[6]=(...e)=>a.handlerButton&&a.handlerButton(...e))},[t.icon?(e.openBlock(),e.createElementBlock("image",{key:0,src:t.icon,style:e.normalizeStyle({width:a.px(t.imgWidth),height:a.px(t.imgHeight)})},null,12,["src"])):e.createCommentVNode("",!0),e.createElementVNode("text",{style:e.normalizeStyle({fontSize:a.px(t.fontsize)})},e.toDisplayString(t.name),5)],12,["data-index"])))),128))],32)):e.createCommentVNode("",!0),0===o.actions.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"tui-swipeout-button-right-group",onTouchend:n[8]||(n[8]=e.withModifiers(((...e)=>a.loop&&a.loop(...e)),["stop"])),onClick:n[9]||(n[9]=(...e)=>a.handlerParentButton&&a.handlerParentButton(...e)),style:e.normalizeStyle({width:o.operateWidth+"px",right:"-"+o.operateWidth+"px"})},[e.renderSlot(t.$slots,"button",{},void 0,!0)],36)):e.createCommentVNode("",!0)],6),i.isShowBtn&&o.showMask?(e.openBlock(),e.createElementBlock("view",{key:0,class:"swipe-action_mask",onClick:n[10]||(n[10]=e.withModifiers(((...e)=>a.closeButtonGroup&&a.closeButtonGroup(...e)),["stop"])),onTouchstart:n[11]||(n[11]=e.withModifiers(((...e)=>a.closeButtonGroup&&a.closeButtonGroup(...e)),["stop","prevent"]))},null,32)):e.createCommentVNode("",!0)],4)}],["__scopeId","data-v-b12d2bb3"]]);function oc(e){return ro({url:"/biz/org/tree",method:"get",data:e})}const rc=Ve({__name:"index",setup(t){const n=e.ref([]),o=e.ref([]),r=e.ref(!1);ne((()=>{uni.$once("formBack",(e=>{i()}))}));const i=()=>{r.value=!0,oc().then((e=>{o.value=(null==e?void 0:e.data)||[],n.value=[{id:"0",name:"全部",children:(null==e?void 0:e.data)||[]}],r.value=!1}))};i();const a=()=>{uni.navigateTo({url:"/pages/biz/org/form"})},s=[];uni.$snowy.hasPerm(["mobileBizOrgEdit"])&&s.push({name:"修改",color:"#fff",fontsize:30,width:70,background:"#5677fc"}),uni.$snowy.hasPerm(["mobileBizOrgDelete"])&&s.push({name:"删除",color:"#fff",fontsize:30,width:70,background:"#FD3B31"});const l=({index:e,item:t})=>{var n,o;return"修改"===(null==(n=s[e])?void 0:n.name)?uni.navigateTo({url:"/pages/biz/org/form?id="+t.id}):"删除"===(null==(o=s[e])?void 0:o.name)?uni.$snowy.modal.confirm(`是否确认删除【${t.name}】机构？`).then((()=>{var e;(e=[{id:t.id}],ro({url:"/biz/org/delete",method:"post",data:e})).then((e=>{i()}))})):void 0};return(t,i)=>{const c=ee(e.resolveDynamicComponent("tui-list-cell"),Jl),u=ee(e.resolveDynamicComponent("tui-swipe-action"),nc),d=ee(e.resolveDynamicComponent("tui-list-view"),Zl),h=ee(e.resolveDynamicComponent("snowy-empty"),zl),p=ee(e.resolveDynamicComponent("snowy-float-btn"),Gl);return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"crumb snowy-shadow"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((t,r)=>(e.openBlock(),e.createElementBlock("text",{class:e.normalizeClass(["text",r===n.value.length-1?"uni-secondary-color":"uni-primary"]),key:r,onClick:e=>((e,t)=>{o.value=e.children,n.value.splice(t+1,n.value.length-(t+1))})(t,r)},e.toDisplayString(t.name+(r===n.value.length-1?"":" | ")),11,["onClick"])))),128))]),e.withDirectives(e.createElementVNode("view",{class:"list snowy-shadow"},[e.createVNode(d,{unlined:"all"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,((t,r)=>(e.openBlock(),e.createBlock(u,{key:r,actions:s,params:t,onClick:l},{content:e.withCtx((()=>[e.createVNode(c,{"line-left":0,hover:!!t.children,arrow:!!t.children,onClick:e=>((e,t)=>{e.children&&(o.value=e.children,n.value.push(e))})(t)},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"},[e.withDirectives(e.createElementVNode("view",null,[e.createElementVNode("image",{class:"item-img",src:"/static/svg/org/company.svg",mode:"widthFix"})],512),[[e.vShow,"COMPANY"===(null==t?void 0:t.category)]]),e.withDirectives(e.createElementVNode("view",null,[e.createElementVNode("image",{class:"item-img",src:"/static/svg/org/department.svg",mode:"widthFix"})],512),[[e.vShow,"DEPT"===(null==t?void 0:t.category)]]),e.createElementVNode("view",{class:"item-left"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"item-right"})])])),_:2},1032,["hover","arrow","onClick"])])),_:2},1032,["params"])))),128))])),_:1}),e.withDirectives(e.createVNode(h,null,null,512),[[e.vShow,t.$xeu.isEmpty(o.value)]])],512),[[e.vShow,!r.value]]),t.$snowy.hasPerm("mobileBizOrgAdd")?(e.openBlock(),e.createBlock(p,{key:0,onClick:a})):e.createCommentVNode("",!0)],64)}}},[["__scopeId","data-v-1b85ec06"]]),ic=Ve({__name:"snowy-org-picker",props:{modelValue:[String,Array],isMultiple:{type:Boolean,default:!1,required:!1},placeholder:{type:String,default:"请选择机构",required:!1},orgTreeApi:{type:Function,default:()=>Promise.resolve(),required:!1},isTopLevel:{type:Boolean,default:!1,required:!1}},emits:["update:modelValue","cancel","confirm"],setup(t,{emit:n}){const o=t,{proxy:r}=e.getCurrentInstance(),i=e.ref(),a=o.isMultiple?e.ref([]):e.ref(""),s=o.isMultiple?e.ref([]):e.ref({}),l=e.ref([]),c=e.ref([]),u=()=>{o.orgTreeApi().then((e=>{if(o.isTopLevel?(c.value=[{id:"0",parentId:"-1",name:"顶级",children:null==e?void 0:e.data}],l.value=[{id:"-1",name:"全部",children:[{id:"0",parentId:"-1",name:"顶级",children:null==e?void 0:e.data}]}]):(c.value=(null==e?void 0:e.data)||[],l.value=[{id:"0",parentId:"-1",name:"全部",children:null==e?void 0:e.data}]),!o.isMultiple)if(o.modelValue?a.value=uni.$xeu.clone(o.modelValue,!0):a.value="",a.value){const e=uni.$xeu.filterTree(l.value,(e=>a.value===e.id));e&&1===e.length&&(s.value=e[0])}else s.value={};o.isMultiple&&(o.modelValue&&o.modelValue.length>0?a.value=uni.$xeu.clone(o.modelValue,!0):a.value=[],a.value&&a.value.length>0?s.value=uni.$xeu.filterTree(l.value,(e=>a.value.includes(e.id))):s.value=[])}))};e.watch((()=>o.modelValue),((e,t)=>{u()}),{deep:!1,immediate:!0});const d=()=>{i.value.open()},h=(e,t)=>{o.isMultiple?(a.value.splice(a.value.findIndex((t=>t===e.id)),1),s.value.splice(s.value.findIndex((t=>t.id===e.id)),1)):(a.value="",s.value={})},p=(e,t)=>{e.children&&(c.value=e.children,l.value.push(e))},f=()=>{u(),i.value.close()},m=()=>{n("update:modelValue",a.value),n("confirm",{curSelOrgId:a.value,curSelOrg:s.value}),uni.$uv.formValidate(r,"change"),i.value.close()};return(n,r)=>{const u=ee(e.resolveDynamicComponent("uv-icon"),Ae),g=ee(e.resolveDynamicComponent("uv-col"),Ks),y=ee(e.resolveDynamicComponent("uv-row"),Gs),v=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createElementBlock("view",{class:"snowy-org-picker"},[e.createElementVNode("view",{onClick:d,class:e.normalizeClass({"input-disabled":o.disabled})},[e.createElementVNode("view",{class:"input-value"},[!t.isMultiple&&e.unref(a)&&""!==e.unref(a)?(e.openBlock(),e.createElementBlock("view",{key:0},e.toDisplayString(e.unref(s).name),1)):t.isMultiple&&e.unref(a)&&e.unref(a).length>0?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(e.unref(s),((t,n,o)=>(e.openBlock(),e.createElementBlock("view",{class:"multiple",key:o},e.toDisplayString(t.name),1)))),128)):(e.openBlock(),e.createElementBlock("view",{key:2,class:"placeholder"},e.toDisplayString(t.placeholder),1))])],2),e.createVNode(v,{ref_key:"popRef",ref:i,mode:"bottom","bg-color":"null","z-index":"99",onMaskClick:f},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"pop-container"},[e.createElementVNode("view",{class:"action"},[e.createElementVNode("view",{class:"cancel snowy-bold",onClick:f}," 取消 "),e.createElementVNode("view",{class:"confirm snowy-bold",onClick:m}," 确定 ")]),e.withDirectives(e.createElementVNode("scroll-view",{"scroll-y":!0,style:e.normalizeStyle({maxHeight:t.isMultiple?"10vh":"5vh"}),class:"choiced"},[t.isMultiple?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"single"},[e.createElementVNode("view",{class:"label",onClick:r[0]||(r[0]=t=>h(e.unref(s)))},e.toDisplayString(e.unref(s).name),1),e.createElementVNode("view",{class:"icon"},[e.createElementVNode("icon",{type:"clear",onClick:r[1]||(r[1]=t=>h(e.unref(s))),color:"#ffffff",size:"15"})])])),t.isMultiple?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(e.unref(s),((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"multiple"},[e.createElementVNode("view",{class:"label",onClick:e=>h(t)},e.toDisplayString(t.name),9,["onClick"]),e.createElementVNode("view",{class:"icon"},[e.createElementVNode("icon",{type:"clear",onClick:r[2]||(r[2]=t=>h(e.unref(s))),color:"#ffffff",size:"15"})])])))),256)):e.createCommentVNode("",!0)],4),[[e.vShow,!!e.unref(a)&&(!t.isMultiple||e.unref(a).length>0)]]),e.createElementVNode("view",{class:"crumb snowy-shadow"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((t,n)=>(e.openBlock(),e.createElementBlock("text",{key:n,onClick:e=>((e,t)=>{c.value=e.children,l.value.splice(t+1,l.value.length-(t+1))})(t,n),style:e.normalizeStyle({color:n===l.value.length-1?"#3a3a3a":"#5677fc"})},e.toDisplayString(t.name+(n===l.value.length-1?"":" | ")),13,["onClick"])))),128))]),e.createElementVNode("scroll-view",{class:"data","scroll-y":!0},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value,((r,i)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["item",{"item-sel":t.isMultiple?-1!=e.unref(a).indexOf(r.id):r.id==e.unref(a)}]),key:i,index:i},[e.createVNode(y,null,{default:e.withCtx((()=>[e.createVNode(g,{span:1.5},{default:e.withCtx((()=>[e.withDirectives(e.createElementVNode("view",null,[e.createVNode(u,{name:"checkmark-circle",size:20,color:"#999",onClick:e=>((e,t)=>{o.isMultiple?(a.value.push(e.id),s.value.push(e)):(a.value=e.id,s.value=e)})(r)},null,8,["onClick"])],512),[[e.vShow,t.isMultiple?-1==e.unref(a).indexOf(r.id):r.id!=e.unref(a)]]),e.withDirectives(e.createElementVNode("view",null,[e.createVNode(u,{name:"checkmark-circle-fill",size:20,color:"#5677fc",onClick:e=>h(r)},null,8,["onClick"])],512),[[e.vShow,t.isMultiple?-1!=e.unref(a).indexOf(r.id):r.id==e.unref(a)]])])),_:2},1032,["span"]),e.createVNode(g,{span:9.5},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left",onClick:e=>p(r)},e.toDisplayString(r.name),9,["onClick"])])),_:2},1032,["span"]),e.createVNode(g,{span:1},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"snowy-flex-end"},[n.$xeu.isEmpty(r.children)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(u,{key:0,name:"arrow-right",size:"20",onClick:e=>p(r)},null,8,["onClick"]))])])),_:2},1024)])),_:2},1024)],10,["index"])))),128))])])])),_:1},512)])}}},[["__scopeId","data-v-ee459709"]]),ac={props:{name:{type:[String,Number,Boolean],default:""},shape:{type:String,default:""},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},inactiveColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},label:{type:[String,Number,Boolean],default:""},size:{type:[String,Number],default:""},iconColor:{type:String,default:""},labelColor:{type:String,default:""},...null==(L=null==(M=uni.$uv)?void 0:M.props)?void 0:L.radio}},sc={name:"uv-radio",mixins:[X,Ee,ac],data:()=>({checked:!1,parentData:{iconSize:12,labelSize:14,labelDisabled:null,disabled:null,shape:null,activeColor:null,inactiveColor:null,size:18,value:null,modelValue:null,iconColor:null,placement:"row",borderBottom:!1,iconPlacement:"left"}}),computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:21},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:12},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"#2979ff"},elInactiveColor(){return this.inactiveColor?this.inactiveColor:this.parentData.inactiveColor?this.parentData.inactiveColor:"#c8c9cc"},elLabelColor(){return this.labelColor?this.labelColor:this.parentData.labelColor?this.parentData.labelColor:"#606266"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},elLabelSize(){return this.$uv.addUnit(this.labelSize?this.labelSize:this.parentData.labelSize?this.parentData.labelSize:"15")},elIconColor(){const e=this.iconColor?this.iconColor:this.parentData.iconColor?this.parentData.iconColor:"#ffffff";return this.elDisabled?this.checked?this.elInactiveColor:"transparent":this.checked?e:"transparent"},iconClasses(){let e=[];return e.push("uv-radio__icon-wrap--"+this.elShape),this.elDisabled&&e.push("uv-radio__icon-wrap--disabled"),this.checked&&this.elDisabled&&e.push("uv-radio__icon-wrap--disabled--checked"),e},iconWrapStyle(){const e={};return e.backgroundColor=this.checked&&!this.elDisabled?this.elActiveColor:"#ffffff",e.borderColor=this.checked&&!this.elDisabled?this.elActiveColor:this.elInactiveColor,e.width=this.$uv.addUnit(this.elSize),e.height=this.$uv.addUnit(this.elSize),"right"===this.parentData.iconPlacement&&(e.marginRight=0),e},radioStyle(){const e={};return this.parentData.borderBottom&&"row"===this.parentData.placement&&error("检测到您将borderBottom设置为true，需要同时将uv-radio-group的placement设置为column才有效"),this.parentData.borderBottom&&"column"===this.parentData.placement&&(e.paddingBottom="ios"===this.$uv.os()?"12px":"8px"),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},mounted(){this.init()},methods:{init(){this.updateParentData(),this.parent||error("uv-radio必须搭配uv-radio-group组件使用"),this.$nextTick((()=>{let e=null;e=this.parentData.modelValue,this.checked=this.name===e}))},updateParentData(){this.getParentData("uv-radio-group")},iconClickHandler(e){this.preventEvent(e),this.elDisabled||this.setRadioCheckedStatus()},wrapperClickHandler(e){"right"===this.parentData.iconPlacement&&this.iconClickHandler(e)},labelClickHandler(e){this.preventEvent(e),this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.checked||(this.$emit("change",this.name),this.$nextTick((()=>{this.$uv.formValidate(this,"change")})))},setRadioCheckedStatus(){this.emitEvent(),this.checked=!0,"function"==typeof this.parent.unCheckedOther&&this.parent.unCheckedOther(this)}}};const lc=Ve(sc,[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-radio",[`uv-radio-label--${i.parentData.iconPlacement}`,i.parentData.borderBottom&&"column"===i.parentData.placement&&"uv-border-bottom"]]),onClick:n[2]||(n[2]=e.withModifiers(((...e)=>a.wrapperClickHandler&&a.wrapperClickHandler(...e)),["stop"])),style:e.normalizeStyle([a.radioStyle])},[e.createElementVNode("view",{class:e.normalizeClass(["uv-radio__icon-wrap",a.iconClasses]),onClick:n[0]||(n[0]=e.withModifiers(((...e)=>a.iconClickHandler&&a.iconClickHandler(...e)),["stop"])),style:e.normalizeStyle([a.iconWrapStyle])},[e.renderSlot(t.$slots,"icon",{},(()=>[e.createVNode(s,{class:"uv-radio__icon-wrap__icon",name:"checkbox-mark",size:a.elIconSize,color:a.elIconColor},null,8,["size","color"])]),!0)],6),e.createElementVNode("view",{class:"uv-radio__label-wrap",onClick:n[1]||(n[1]=e.withModifiers(((...e)=>a.labelClickHandler&&a.labelClickHandler(...e)),["stop"]))},[e.renderSlot(t.$slots,"default",{},(()=>[e.createElementVNode("text",{style:e.normalizeStyle({color:a.elDisabled?a.elInactiveColor:a.elLabelColor,fontSize:a.elLabelSize,lineHeight:a.elLabelSize})},e.toDisplayString(t.label),5)]),!0)])],6)}],["__scopeId","data-v-4ca27a99"]]),cc={props:{value:{type:[String,Number,Boolean],default:""},modelValue:{type:[String,Number,Boolean],default:""},disabled:{type:Boolean,default:!1},shape:{type:String,default:"circle"},activeColor:{type:String,default:"#2979ff"},inactiveColor:{type:String,default:"#c8c9cc"},name:{type:String,default:""},size:{type:[String,Number],default:18},placement:{type:String,default:"row"},label:{type:[String],default:""},labelColor:{type:[String],default:"#303133"},labelSize:{type:[String,Number],default:14},labelDisabled:{type:Boolean,default:!1},iconColor:{type:String,default:"#fff"},iconSize:{type:[String,Number],default:12},borderBottom:{type:Boolean,default:!1},iconPlacement:{type:String,default:"left"},...null==(R=null==(z=uni.$uv)?void 0:z.props)?void 0:R.radioGroup}};const uc=Ve({name:"uv-radio-group",mixins:[X,Ee,cc],computed:{parentData(){return[this.value||this.modelValue,this.disabled,this.inactiveColor,this.activeColor,this.size,this.labelDisabled,this.shape,this.iconSize,this.borderBottom,this.placement]},bemClass(){return this.bem("radio-group",["placement"])}},watch:{parentData(){this.children.length&&this.children.map((e=>{"function"==typeof e.init&&e.init()}))}},data:()=>({}),created(){this.children=[]},methods:{unCheckedOther(e){this.children.map((t=>{e!==t&&(t.checked=!1)}));const{name:t}=e;this.$emit("update:modelValue",t),this.$emit("change",t)}}},[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-radio-group",a.bemClass]),style:e.normalizeStyle([t.$uv.addStyle(this.customStyle)])},[e.renderSlot(t.$slots,"default",{},void 0,!0)],6)}],["__scopeId","data-v-fe601beb"]]),dc={props:{value:{type:[String,Number],default:0},modelValue:{type:[String,Number],default:0},name:{type:[String,Number],default:""},min:{type:[String,Number],default:1},max:{type:[String,Number],default:Number.MAX_SAFE_INTEGER},step:{type:[String,Number],default:1},integer:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},disabledInput:{type:Boolean,default:!1},asyncChange:{type:Boolean,default:!1},inputWidth:{type:[String,Number],default:35},showMinus:{type:Boolean,default:!0},showPlus:{type:Boolean,default:!0},decimalLength:{type:[String,Number,null],default:null},longPress:{type:Boolean,default:!0},color:{type:String,default:"#323233"},buttonSize:{type:[String,Number],default:30},bgColor:{type:String,default:"#EBECEE"},cursorSpacing:{type:[String,Number],default:100},disablePlus:{type:Boolean,default:!1},disableMinus:{type:Boolean,default:!1},iconStyle:{type:[Object,String],default:""},...null==(q=null==(U=uni.$uv)?void 0:U.props)?void 0:q.numberBox}},hc={name:"uv-number-box",mixins:[X,Ee,dc],data:()=>({currentValue:"",longPressTimer:null}),watch:{watchChange(e){this.check()},value(e){e!==this.currentValue&&(this.currentValue=this.format(this.value))},modelValue(e){e!==this.currentValue&&(this.currentValue=this.format(this.modelValue))}},computed:{getCursorSpacing(){return this.$uv.getPx(this.cursorSpacing)},buttonStyle(){return e=>{const t={backgroundColor:this.bgColor,height:this.$uv.addUnit(this.buttonSize),color:this.color};return this.isDisabled(e)&&(t.backgroundColor="#f7f8fa"),t}},inputStyle(){this.disabled||this.disabledInput;return{color:this.color,backgroundColor:this.bgColor,height:this.$uv.addUnit(this.buttonSize),width:this.$uv.addUnit(this.inputWidth)}},watchChange(){return[this.integer,this.decimalLength,this.min,this.max]},isDisabled(){return e=>"plus"===e?this.disabled||this.disablePlus||this.currentValue>=this.max:this.disabled||this.disableMinus||this.currentValue<=this.min}},created(){this.init()},methods:{init(){const e=this.value||this.modelValue;this.currentValue=this.format(e)},format(e){return e=""===(e=this.filter(e))?0:+e,e=Math.max(Math.min(this.max,e),this.min),null!==this.decimalLength&&(e=e.toFixed(this.decimalLength)),e},filter(e){return e=String(e).replace(/[^0-9.-]/g,""),this.integer&&-1!==e.indexOf(".")&&(e=e.split(".")[0]),e},check(){const e=this.format(this.currentValue);e!==this.currentValue&&(this.currentValue=e)},onFocus(e){this.$emit("focus",{...e.detail,name:this.name})},onBlur(e){this.format(e.detail.value),this.$emit("blur",{...e.detail,name:this.name})},onInput(e){const{value:t=""}=e.detail||{};if(""===t)return;let n=this.filter(t);if(null!==this.decimalLength&&-1!==n.indexOf(".")){const e=n.split(".");n=`${e[0]}.${e[1].slice(0,this.decimalLength)}`}n=this.format(n),this.emitChange(n)},emitChange(e){this.asyncChange||this.$nextTick((()=>{this.$emit("input",e),this.$emit("update:modelValue",e),this.currentValue=e,this.$forceUpdate()})),this.$emit("change",{value:e,name:this.name})},onChange(){const{type:e}=this;if(this.isDisabled(e))return this.$emit("overlimit",e);const t="minus"===e?-this.step:+this.step,n=this.format(this.add(+this.currentValue,t));this.emitChange(n),this.$emit(e)},add(e,t){const n=Math.pow(10,10);return Math.round((e+t)*n)/n},clickHandler(e){this.type=e,this.onChange()},longPressStep(){this.clearTimeout(),this.longPressTimer=setTimeout((()=>{this.onChange(),this.longPressStep()}),250)},onTouchStart(e){this.longPress&&(this.clearTimeout(),this.type=e,this.longPressTimer=setTimeout((()=>{this.onChange(),this.longPressStep()}),600))},onTouchEnd(){this.longPress&&this.clearTimeout()},clearTimeout(){clearTimeout(this.longPressTimer),this.longPressTimer=null}}};const pc=Ve(hc,[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:"uv-number-box"},[t.showMinus&&t.$slots.minus?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-number-box__slot",onClick:n[0]||(n[0]=e.withModifiers((e=>a.clickHandler("minus")),["stop"])),onTouchstart:n[1]||(n[1]=e=>a.onTouchStart("minus")),onTouchend:n[2]||(n[2]=e.withModifiers(((...e)=>a.clearTimeout&&a.clearTimeout(...e)),["stop"]))},[e.renderSlot(t.$slots,"minus",{},void 0,!0)],32)):t.showMinus?(e.openBlock(),e.createElementBlock("view",{key:1,class:e.normalizeClass(["uv-number-box__minus",{"uv-number-box__minus--disabled":a.isDisabled("minus")}]),onClick:n[3]||(n[3]=e.withModifiers((e=>a.clickHandler("minus")),["stop"])),onTouchstart:n[4]||(n[4]=e=>a.onTouchStart("minus")),onTouchend:n[5]||(n[5]=e.withModifiers(((...e)=>a.clearTimeout&&a.clearTimeout(...e)),["stop"])),"hover-class":"uv-number-box__minus--hover","hover-stay-time":"150",style:e.normalizeStyle([a.buttonStyle("minus")])},[e.createVNode(s,{name:"minus",color:a.isDisabled("minus")?"#c8c9cc":"#323233",size:"15",bold:"",customStyle:t.iconStyle},null,8,["color","customStyle"])],38)):e.createCommentVNode("",!0),e.renderSlot(t.$slots,"input",{},(()=>[e.withDirectives(e.createElementVNode("input",{disabled:t.disabledInput||t.disabled,"cursor-spacing":a.getCursorSpacing,class:e.normalizeClass([{"uv-number-box__input--disabled":t.disabled||t.disabledInput},"uv-number-box__input"]),"onUpdate:modelValue":n[6]||(n[6]=e=>i.currentValue=e),onBlur:n[7]||(n[7]=(...e)=>a.onBlur&&a.onBlur(...e)),onFocus:n[8]||(n[8]=(...e)=>a.onFocus&&a.onFocus(...e)),onInput:n[9]||(n[9]=(...e)=>a.onInput&&a.onInput(...e)),type:"number",style:e.normalizeStyle([a.inputStyle])},null,46,["disabled","cursor-spacing"]),[[e.vModelText,i.currentValue]])]),!0),t.showPlus&&t.$slots.plus?(e.openBlock(),e.createElementBlock("view",{key:2,class:"uv-number-box__slot",onClick:n[10]||(n[10]=e.withModifiers((e=>a.clickHandler("plus")),["stop"])),onTouchstart:n[11]||(n[11]=e=>a.onTouchStart("plus")),onTouchend:n[12]||(n[12]=e.withModifiers(((...e)=>a.clearTimeout&&a.clearTimeout(...e)),["stop"]))},[e.renderSlot(t.$slots,"plus",{},void 0,!0)],32)):t.showPlus?(e.openBlock(),e.createElementBlock("view",{key:3,class:e.normalizeClass(["uv-number-box__plus",{"uv-number-box__minus--disabled":a.isDisabled("plus")}]),onClick:n[13]||(n[13]=e.withModifiers((e=>a.clickHandler("plus")),["stop"])),onTouchstart:n[14]||(n[14]=e=>a.onTouchStart("plus")),onTouchend:n[15]||(n[15]=e.withModifiers(((...e)=>a.clearTimeout&&a.clearTimeout(...e)),["stop"])),"hover-class":"uv-number-box__plus--hover","hover-stay-time":"150",style:e.normalizeStyle([a.buttonStyle("plus")])},[e.createVNode(s,{name:"plus",color:a.isDisabled("plus")?"#c8c9cc":"#323233",size:"15",bold:"",customStyle:t.iconStyle},null,8,["color","customStyle"])],38)):e.createCommentVNode("",!0)])}],["__scopeId","data-v-bc43264f"]]),fc=Ve({__name:"snowy-user-picker",props:{modelValue:[String,Array],placeholder:{type:String,default:"请选择",required:!1},isMultiple:{type:Boolean,default:!1,required:!1},orgTreeApi:{type:Function,default:()=>Promise.resolve(),required:!1},checkedUserListApi:{type:Function,default:()=>Promise.resolve(),required:!1},userPageApi:{type:Function,default:()=>Promise.resolve(),required:!1},autoInitData:{type:Boolean,default:!0,required:!1}},emits:["update:modelValue","cancel","confirm"],setup(t,{expose:n,emit:o}){const r=t,{proxy:i}=e.getCurrentInstance(),a=e.ref(),s=e.reactive({}),l=e.reactive({current:1,size:10}),c=e.ref([]),u=r.isMultiple?e.ref([]):e.ref(""),d=r.isMultiple?e.ref([]):e.ref({}),h=e.ref([]),p=e.ref([]),f=()=>{r.isMultiple||(uni.$xeu.isEmpty(r.modelValue)?(d.value={},u.value=""):(r.checkedUserListApi({idList:[r.modelValue]}).then((e=>{d.value=uni.$xeu.isEmpty(null==e?void 0:e.data)?{}:null==e?void 0:e.data[0]})),u.value=uni.$xeu.clone(r.modelValue,!0))),r.isMultiple&&(uni.$xeu.isEmpty(r.modelValue)?(d.value=[],u.value=[]):(r.checkedUserListApi({idList:r.modelValue}).then((e=>{d.value=uni.$xeu.isEmpty(null==e?void 0:e.data)?[]:e.data})),u.value=uni.$xeu.clone(r.modelValue,!0)))},m=e.ref(!0);e.watch((()=>r.modelValue),((e,t)=>{m.value&&f(),m.value=!0}),{deep:!1,immediate:!0});const g=e=>{r.orgTreeApi().then((e=>{p.value=(null==e?void 0:e.data)||[],h.value=[{id:"0",name:"全部",children:(null==e?void 0:e.data)||[]}]}))};r.autoInitData&&g();const y=(e,t)=>{e&&(l.current=1,c.value=[]),Object.assign(l,t),Object.assign(l,s),r.userPageApi(l).then((e=>{var t;uni.$xeu.isEmpty(null==(t=null==e?void 0:e.data)?void 0:t.records)||(c.value=c.value.concat(e.data.records),l.current++)}))};r.autoInitData&&y(!0);const v=()=>{a.value.open()},b=(e,t)=>{r.isMultiple?(u.value.push(e.id),d.value.push(e)):(u.value=e.id,d.value=e)},w=(e,t)=>{r.isMultiple?(u.value.splice(u.value.findIndex((t=>t===e.id)),1),d.value.splice(d.value.findIndex((t=>t.id===e.id)),1)):(u.value="",d.value={})},k=()=>{f(),a.value.close()},x=()=>{m.value=!1,o("update:modelValue",u.value),o("confirm",{curSelUserId:u.value,curSelUser:d.value}),uni.$uv.formValidate(i,"change"),a.value.close()},S=()=>{y()};return n({initOrResetData:f,loadOrgTree:g,loadUserData:y}),(n,o)=>{const i=ee(e.resolveDynamicComponent("snowy-search"),Yl),l=ee(e.resolveDynamicComponent("uv-col"),Ks),f=ee(e.resolveDynamicComponent("uv-icon"),Ae),m=ee(e.resolveDynamicComponent("uv-row"),Gs),g=ee(e.resolveDynamicComponent("snowy-empty"),zl),C=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createElementBlock("view",{class:"snowy-user-picker"},[e.createElementVNode("view",{onClick:v,class:e.normalizeClass({"input-disabled":r.disabled})},[e.createElementVNode("view",{class:"input-value"},[!t.isMultiple&&e.unref(u)&&""!==e.unref(u)?(e.openBlock(),e.createElementBlock("view",{key:0},e.toDisplayString(e.unref(d).name),1)):t.isMultiple&&e.unref(u)&&e.unref(u).length>0?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(e.unref(d),((t,n,o)=>(e.openBlock(),e.createElementBlock("view",{class:"multiple",key:o},e.toDisplayString(t.name),1)))),128)):(e.openBlock(),e.createElementBlock("view",{key:2,class:"placeholder"},e.toDisplayString(t.placeholder),1))])],2),e.createVNode(C,{ref_key:"popRef",ref:a,mode:"bottom","bg-color":"null","z-index":"99",onMaskClick:k},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"pop-container"},[e.createElementVNode("view",{class:"action"},[e.createElementVNode("view",{class:"cancel snowy-bold",onClick:k}," 取消 "),e.createElementVNode("view",{class:"confirm snowy-bold",onClick:x}," 确定 ")]),e.withDirectives(e.createElementVNode("scroll-view",{"scroll-y":!0,style:e.normalizeStyle({maxHeight:t.isMultiple?"10vh":"5vh"}),class:"choiced"},[t.isMultiple?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"single"},[e.createElementVNode("view",{class:"label",onClick:o[0]||(o[0]=t=>w(e.unref(d)))},e.toDisplayString(e.unref(d).name),1),e.createElementVNode("view",{class:"icon"},[e.createElementVNode("icon",{type:"clear",onClick:o[1]||(o[1]=t=>w(e.unref(d))),color:"#ffffff",size:"15"})])])),t.isMultiple?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(e.unref(d),((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"multiple",key:n},[e.createElementVNode("view",{class:"label",onClick:e=>w(t)},e.toDisplayString(t.name),9,["onClick"]),e.createElementVNode("view",{class:"icon"},[e.createElementVNode("icon",{type:"clear",onClick:o[2]||(o[2]=t=>w(e.unref(d))),color:"#ffffff",size:"15"})])])))),128)):e.createCommentVNode("",!0)],4),[[e.vShow,!!e.unref(u)&&(!t.isMultiple||e.unref(u).length>0)]]),e.createElementVNode("scroll-view",{class:"orgAndUser","scroll-y":!0,onScrolltolower:S},[e.createVNode(i,{modelValue:s.searchKey,"onUpdate:modelValue":o[3]||(o[3]=e=>s.searchKey=e),onConfirm:o[4]||(o[4]=e=>y(!0)),onClear:o[5]||(o[5]=e=>y(!0)),customStyle:{padding:0}},null,8,["modelValue"]),e.createElementVNode("view",{class:"crumb snowy-shadow"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(h.value,((t,n)=>(e.openBlock(),e.createElementBlock("text",{key:n,onClick:e=>((e,t)=>{p.value=e.children,h.value.splice(t+1,h.value.length-(t+1)),s.orgId="0"===e.id?null:e.id,y(!0)})(t,n),style:e.normalizeStyle({color:n===h.value.length-1?"#3a3a3a":"#5677fc"})},e.toDisplayString(t.name+(n===h.value.length-1?"":" | ")),13,["onClick"])))),128))]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(p.value,((t,n)=>(e.openBlock(),e.createElementBlock("view",{class:"org-item",key:n,index:n,onClick:e=>((e,t)=>{p.value=e.children,h.value.push(e),s.orgId="0"===e.id?null:e.id,y(!0)})(t)},[e.createVNode(m,null,{default:e.withCtx((()=>[e.createVNode(l,{span:6},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},e.toDisplayString(t.name),1)])),_:2},1024),e.createVNode(l,{span:6},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"snowy-flex-end"},[e.createVNode(f,{name:"arrow-right",size:"20"})])])),_:1})])),_:2},1024)],8,["index","onClick"])))),128)),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value,((n,o)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["user-item",{"item-sel":t.isMultiple?-1!=e.unref(u).indexOf(n.id):n.id==e.unref(u)}]),key:o,index:o,onClick:e=>((e,t)=>{r.isMultiple?u.value.includes(e.id)?w(e):b(e):e.id!=u.value?b(e):w(e)})(n)},[e.createVNode(m,null,{default:e.withCtx((()=>[e.createVNode(l,{span:1.5},{default:e.withCtx((()=>[e.createElementVNode("image",{class:"item-img",src:(null==n?void 0:n.avatar)||"/static/logo.png",mode:"widthFix"},null,8,["src"])])),_:2},1032,["span"]),e.createVNode(l,{span:9.5},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-title"},e.toDisplayString(null==n?void 0:n.name),1),e.createElementVNode("view",{class:"item-sub-title"},e.toDisplayString((null==n?void 0:n.orgName)+" | "+(null==n?void 0:n.positionName)+" | "+(null==n?void 0:n.genderName)),1)])),_:2},1032,["span"]),e.createVNode(l,{span:1},{default:e.withCtx((()=>[e.withDirectives(e.createElementVNode("view",null,[e.createVNode(f,{name:"checkmark-circle",size:20})],512),[[e.vShow,t.isMultiple?-1==e.unref(u).indexOf(n.id):n.id!=e.unref(u)]]),e.withDirectives(e.createElementVNode("view",null,[e.createVNode(f,{name:"checkmark-circle-fill",size:20,color:"#2979ff"})],512),[[e.vShow,t.isMultiple?-1!=e.unref(u).indexOf(n.id):n.id==e.unref(u)]])])),_:2},1024)])),_:2},1024)],10,["index","onClick"])))),128)),e.withDirectives(e.createVNode(g,null,null,512),[[e.vShow,n.$xeu.isEmpty(c.value)]])],32)])])),_:1},512)])}}},[["__scopeId","data-v-f0102865"]]),mc=Ve({__name:"form",setup(t){const n=e.ref(),o=e.ref({parentId:"",name:"",category:"",sortCode:99,directorId:""}),r=e.reactive({parentId:[{type:"string",required:!0,message:"请选择上级机构",trigger:["change"]}],name:[{type:"string",required:!0,message:"请输入机构名称",trigger:["blur","change"]}],category:[{type:"string",required:!0,message:"请选择机构分类",trigger:["change"]}],sortCode:[{type:"number",required:!0,message:"请输入排序",trigger:["blur","change"]}]}),i=uni.$snowy.tool.dictList("ORG_CATEGORY"),a={orgTreeSelectorApi:e=>{return(t=e,ro({url:"/biz/org/orgTreeSelector",method:"get",data:t})).then((e=>Promise.resolve(e)));var t},userPageApi:e=>{return(t=e,ro({url:"/biz/org/userSelector",method:"get",data:t})).then((e=>Promise.resolve(e)));var t},checkedUserListApi:e=>Wn(e).then((e=>Promise.resolve(e)))};oe((e=>{var t;e.id&&(t={id:e.id},ro({url:"/biz/org/detail",method:"get",data:t})).then((e=>{o.value=null==e?void 0:e.data}))}));const s=()=>{n.value.validate().then((e=>{(function(e,t=!0){return ro({url:"/biz/org/"+(t?"add":"edit"),method:"post",data:e})})(o.value,!o.value.id).then((e=>{uni.$emit("formBack",{data:e.data}),uni.navigateBack({delta:1})}))}))};return(t,l)=>{const c=ee(e.resolveDynamicComponent("snowy-org-picker"),ic),u=ee(e.resolveDynamicComponent("uv-form-item"),Qs),d=ee(e.resolveDynamicComponent("uv-input"),nl),h=ee(e.resolveDynamicComponent("uv-radio"),lc),p=ee(e.resolveDynamicComponent("uv-radio-group"),uc),f=ee(e.resolveDynamicComponent("uv-number-box"),pc),m=ee(e.resolveDynamicComponent("snowy-user-picker"),fc),g=ee(e.resolveDynamicComponent("uv-form"),xl),y=ee(e.resolveDynamicComponent("tui-button"),Sl);return e.openBlock(),e.createElementBlock("view",{class:"container snowy-shadow"},[e.createVNode(g,{ref_key:"formRef",ref:n,model:o.value,rules:r,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:e.withCtx((()=>[e.createVNode(u,{label:"上级机构",prop:"parentId",required:""},{default:e.withCtx((()=>[e.createVNode(c,{modelValue:o.value.parentId,"onUpdate:modelValue":l[0]||(l[0]=e=>o.value.parentId=e),isTopLevel:!0,placeholder:"请选择上级机构","org-tree-api":a.orgTreeSelectorApi},null,8,["modelValue","org-tree-api"])])),_:1}),e.createVNode(u,{label:"机构名称",prop:"name",required:""},{default:e.withCtx((()=>[e.createVNode(d,{modelValue:o.value.name,"onUpdate:modelValue":l[1]||(l[1]=e=>o.value.name=e),placeholder:"请输入机构名称",fontSize:"27rpx"},null,8,["modelValue"])])),_:1}),e.createVNode(u,{label:"机构分类",prop:"category",required:""},{default:e.withCtx((()=>[e.createVNode(p,{modelValue:o.value.category,"onUpdate:modelValue":l[2]||(l[2]=e=>o.value.category=e)},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(i),((t,n)=>(e.openBlock(),e.createBlock(h,{customStyle:{marginRight:"50rpx"},key:n,label:t.text,name:t.value},null,8,["label","name"])))),128))])),_:1},8,["modelValue"])])),_:1}),e.createVNode(u,{label:"排序",prop:"sortCode",required:""},{default:e.withCtx((()=>[e.createVNode(f,{modelValue:o.value.sortCode,"onUpdate:modelValue":l[3]||(l[3]=e=>o.value.sortCode=e),"button-size":"30",step:1,max:100},null,8,["modelValue"])])),_:1}),e.createVNode(u,{label:"指定主管",prop:"directorId"},{default:e.withCtx((()=>[e.createVNode(m,{modelValue:o.value.directorId,"onUpdate:modelValue":l[4]||(l[4]=e=>o.value.directorId=e),placeholder:"请选择主管","org-tree-api":a.orgTreeSelectorApi,"user-page-api":a.userPageApi,"checked-user-list-api":a.checkedUserListApi},null,8,["modelValue","org-tree-api","user-page-api","checked-user-list-api"])])),_:1})])),_:1},8,["model","rules"]),e.createVNode(y,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:s},{default:e.withCtx((()=>[e.createTextVNode("提交")])),_:1})])}}},[["__scopeId","data-v-f25929d7"]]);const gc=Ve({__name:"more",emits:["handleOk"],setup(t,{expose:n,emit:o}){const r=e.ref(),i=e.ref({}),a=()=>{uni.navigateTo({url:"/pages/biz/position/form?id="+i.value.id}),r.value.close()},s=()=>{uni.$snowy.modal.confirm(`是否确认删除【${i.value.name}】职位？`).then((()=>{var e;(e=[{id:i.value.id}],ro({url:"/biz/position/delete",method:"post",data:e})).then((e=>{o("handleOk"),r.value.close()}))}))},l=()=>{r.value.close()};return n({open:e=>{i.value=e,r.value.open()}}),(t,n)=>{const o=ee(e.resolveDynamicComponent("tui-list-cell"),Jl),i=ee(e.resolveDynamicComponent("tui-list-view"),Zl),c=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createBlock(c,{ref_key:"popRef",ref:r,mode:"bottom","bg-color":"null","z-index":"99"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"container"},[e.createVNode(i,{unlined:"all","background-color":"transparent"},{default:e.withCtx((()=>[t.$snowy.hasPerm("mobileBizPositionEdit")?(e.openBlock(),e.createBlock(o,{key:0,hover:!0,arrow:!1,onClick:a,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 编辑 ")])),_:1})):e.createCommentVNode("",!0),t.$snowy.hasPerm("mobileBizPositionDelete")?(e.openBlock(),e.createBlock(o,{key:1,hover:!0,arrow:!1,onClick:s,radius:10,"margin-top":2},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 刪除 ")])),_:1})):e.createCommentVNode("",!0),e.createVNode(o,{hover:!0,arrow:!1,onClick:l,"margin-top":10,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 取消 ")])),_:1})])),_:1})])])),_:1},512)}}},[["__scopeId","data-v-eaf3a4c4"]]),yc=Ve({__name:"index",setup(t){const n=e.ref([]),o=e.ref([]);oc().then((e=>{o.value=(null==e?void 0:e.data)||[],n.value.push({id:"0",name:"全部",children:(null==e?void 0:e.data)||[]})}));const r=e.reactive({}),i=e.reactive({current:1,size:10}),a=e.ref([]),s=e=>{var t;e&&(i.current=1,a.value=[]),Object.assign(i,r),(t=i,ro({url:"/biz/position/page",method:"get",data:t})).then((e=>{var t;uni.$xeu.isEmpty(null==(t=null==e?void 0:e.data)?void 0:t.records)||(a.value=a.value.concat(e.data.records),i.current++)})).finally((()=>{uni.stopPullDownRefresh()}))};s(!0);ne((()=>{uni.$once("formBack",(e=>{s(!0)}))})),ie((()=>{s(!0)})),re((()=>{s()}));const l=()=>{uni.navigateTo({url:"/pages/biz/position/form"})};return(t,i)=>{const c=ee(e.resolveDynamicComponent("tui-list-cell"),Jl),u=ee(e.resolveDynamicComponent("tui-list-view"),Zl),d=ee(e.resolveDynamicComponent("snowy-icon"),Bl),h=ee(e.resolveDynamicComponent("snowy-empty"),zl),p=ee(e.resolveDynamicComponent("snowy-float-btn"),Gl);return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"crumb snowy-shadow"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((t,i)=>(e.openBlock(),e.createElementBlock("text",{class:e.normalizeClass(["crumb-text",i===n.value.length-1?"uni-secondary-color":"uni-primary"]),key:i,onClick:e=>((e,t)=>{o.value=e.children,n.value.splice(t+1,n.value.length-(t+1)),r.orgId="0"===e.id?"":e.id,s(!0)})(t,i)},e.toDisplayString(t.name+(i===n.value.length-1?"":" | ")),11,["onClick"])))),128))]),e.createElementVNode("view",{class:"org-list snowy-shadow"},[e.createVNode(u,{unlined:"all"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,((t,i)=>(e.openBlock(),e.createBlock(c,{key:i,"line-left":0,hover:!!t.children,arrow:!!t.children,onClick:e=>((e,t)=>{o.value=e.children,n.value.push(e),r.orgId=e.id,s(!0)})(t)},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"},["COMPANY"===t.category?(e.openBlock(),e.createElementBlock("image",{key:0,class:"item-img",src:"/static/svg/org/company.svg",mode:"widthFix"})):e.createCommentVNode("",!0),"DEPT"===t.category?(e.openBlock(),e.createElementBlock("image",{key:1,class:"item-img",src:"/static/svg/org/department.svg",mode:"widthFix"})):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"item-left"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"item-right"})])])),_:2},1032,["hover","arrow","onClick"])))),128))])),_:1})]),e.createElementVNode("view",{class:"biz-list snowy-shadow"},[e.createVNode(u,{unlined:"all"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.value,((n,o)=>(e.openBlock(),e.createBlock(c,{key:o,"line-left":0,hover:!0,arrow:!1,onClick:e=>t.$refs.moreRef.open(n)},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"},["HIGH"==n.category?(e.openBlock(),e.createElementBlock("view",{key:0,style:{width:"42px",height:"42px"}},[e.createVNode(d,{backgroundColor:"#f3a73f",name:"integral",size:"20",color:"#FFFFFF"})])):"MIDDLE"==n.category?(e.openBlock(),e.createElementBlock("view",{key:1,style:{width:"42px",height:"42px"}},[e.createVNode(d,{backgroundColor:"#2979ff",name:"pushpin",size:"20",color:"#FFFFFF"})])):(e.openBlock(),e.createElementBlock("view",{key:2,style:{width:"42px",height:"42px"}},[e.createVNode(d,{backgroundColor:"#18bc37",name:"account",size:"20",color:"#FFFFFF"})])),e.createElementVNode("view",{class:"item-left"},e.toDisplayString(n.name),1),e.createElementVNode("view",{class:"item-right"},e.toDisplayString(t.$snowy.tool.dictTypeData("POSITION_CATEGORY",n.category)),1)])])),_:2},1032,["onClick"])))),128))])),_:1}),e.withDirectives(e.createVNode(h,null,null,512),[[e.vShow,t.$xeu.isEmpty(a.value)]])]),t.$snowy.hasPerm("mobileBizPositionAdd")?(e.openBlock(),e.createBlock(p,{key:0,onClick:l})):e.createCommentVNode("",!0),e.createVNode(gc,{ref:"moreRef",onHandleOk:i[0]||(i[0]=e=>s(!0))},null,512)],64)}}},[["__scopeId","data-v-b8074347"]]),vc=Ve({__name:"snowy-sel-picker",props:{modelValue:[String,Array,Number],isMultiple:{type:Boolean,default:!1,required:!1},placeholder:{type:String,default:"请选择",required:!1},map:{type:Object,default:{key:"key",label:"label"},required:!1},rangeData:{type:Array,default:[],required:!1},disabled:{type:Boolean,default:!1,required:!1},isBigData:{type:Boolean,default:!1,required:!1},enableSearch:{type:Boolean,default:!1,required:!1}},emits:["update:modelValue","queryCurSelData","scrollToLower","cancel","confirm","searchConfirm","searchClear"],setup(t,{expose:n,emit:o}){const r=t,{proxy:i}=e.getCurrentInstance(),a=e.ref(),s=r.isMultiple?e.ref([]):e.ref(""),l=r.isMultiple?e.ref([]):e.ref({}),c=()=>{if(!r.isMultiple)if(s.value=r.modelValue?uni.$xeu.clone(r.modelValue,!0):"",r.isBigData)o("queryCurSelData",s.value,(e=>{l.value=e}));else if(!uni.$xeu.isEmpty(s.value)||uni.$xeu.isNumber(s.value)){const e=uni.$xeu.filterTree(r.rangeData,(e=>s.value==e[r.map.key]));e&&1===e.length&&(l.value=e[0])}else l.value={};r.isMultiple&&(s.value=r.modelValue?uni.$xeu.clone(r.modelValue,!0):[],r.isBigData?o("queryCurSelData",s.value,(e=>{l.value=e})):uni.$xeu.isEmpty(s.value)?l.value=[]:l.value=uni.$xeu.filterTree(r.rangeData,(e=>s.value.includes(e[r.map.key]))))},u=e.ref(!0);e.watch((()=>r.modelValue),((e,t)=>{u.value&&c(),u.value=!0}),{deep:!1,immediate:!0}),e.watch((()=>r.rangeData),((e,t)=>{c()}),{deep:!1,immediate:!0});const d=e=>{o("searchConfirm",e)},h=()=>{o("searchClear")},p=()=>{a.value.open()},f=(e,t)=>{r.isMultiple?(s.value.push(e[r.map.key]),l.value.push(e)):(s.value=e[r.map.key],l.value=e)},m=(e,t)=>{r.isMultiple?(s.value.splice(s.value.findIndex((t=>t===e[r.map.key])),1),l.value.splice(l.value.findIndex((t=>t[r.map.key]===e[r.map.key])),1)):(s.value="",l.value={})},g=()=>{c(),a.value.close()},y=()=>{u.value=!1,o("update:modelValue",s.value),o("confirm",{curSelDataKey:s.value,curSelData:l.value}),uni.$uv.formValidate(i,"change"),a.value.close()},v=()=>{o("scrollToLower")};return n({initOrResetData:c}),(n,o)=>{const i=ee(e.resolveDynamicComponent("snowy-search"),Yl),c=ee(e.resolveDynamicComponent("uv-icon"),Ae),u=ee(e.resolveDynamicComponent("uv-col"),Ks),b=ee(e.resolveDynamicComponent("uv-row"),Gs),w=ee(e.resolveDynamicComponent("snowy-empty"),zl),k=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createElementBlock("view",{class:"snowy-sel-picker"},[e.createElementVNode("view",{onClick:p,class:e.normalizeClass({"input-disabled":r.disabled})},[e.createElementVNode("view",{class:"input-value"},[!t.isMultiple&&e.unref(s)&&""!==e.unref(s)?(e.openBlock(),e.createElementBlock("view",{key:0},e.toDisplayString(e.unref(l)[t.map.label]),1)):t.isMultiple&&e.unref(s)&&e.unref(s).length>0?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(e.unref(l),((n,o,r)=>(e.openBlock(),e.createElementBlock("view",{class:"multiple",key:r},e.toDisplayString(n[t.map.label]),1)))),128)):(e.openBlock(),e.createElementBlock("view",{key:2,class:"placeholder"},e.toDisplayString(t.placeholder),1))])],2),e.createVNode(k,{ref_key:"popRef",ref:a,mode:"bottom","bg-color":"null","z-index":"99",onMaskClick:g},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"pop-container"},[e.createElementVNode("view",{class:"action"},[e.createElementVNode("view",{class:"cancel snowy-bold",onClick:g}," 取消 "),e.createElementVNode("view",{class:"confirm snowy-bold",onClick:y}," 确定 ")]),e.withDirectives(e.createElementVNode("scroll-view",{"scroll-y":!0,style:e.normalizeStyle({maxHeight:t.isMultiple?"10vh":"5vh"}),class:"choiced"},[t.isMultiple?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"single"},[e.createElementVNode("view",{class:"label",onClick:o[0]||(o[0]=t=>m(e.unref(l)))},e.toDisplayString(e.unref(l)[t.map.label]),1),e.createElementVNode("view",{class:"icon"},[e.createElementVNode("icon",{type:"clear",onClick:o[1]||(o[1]=t=>m(e.unref(l))),color:"#ffffff",size:"15"})])])),t.isMultiple?(e.openBlock(!0),e.createElementBlock(e.Fragment,{key:1},e.renderList(e.unref(l),((n,r)=>(e.openBlock(),e.createElementBlock("view",{class:"multiple",key:r},[e.createElementVNode("view",{class:"label",onClick:e=>m(n)},e.toDisplayString(n[t.map.label]),9,["onClick"]),e.createElementVNode("view",{class:"icon"},[e.createElementVNode("icon",{type:"clear",onClick:o[2]||(o[2]=t=>m(e.unref(l))),color:"#ffffff",size:"15"})])])))),128)):e.createCommentVNode("",!0)],4),[[e.vShow,!!e.unref(s)&&(!t.isMultiple||e.unref(s).length>0)]]),t.enableSearch?(e.openBlock(),e.createBlock(i,{key:0,onConfirm:d,onClear:h,customStyle:{padding:0}})):e.createCommentVNode("",!0),e.createElementVNode("scroll-view",{class:"data","scroll-y":!0,onScrolltolower:v},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.rangeData,((n,o)=>(e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["item",{"item-sel":t.isMultiple?-1!=e.unref(s).indexOf(n[t.map.key]):n[t.map.key]==e.unref(s)}]),key:o,index:o,onClick:e=>((e,t)=>{r.isMultiple?s.value.includes(e[r.map.key])?m(e):f(e):e[r.map.key]!=s.value?f(e):m(e)})(n)},[e.createVNode(b,null,{default:e.withCtx((()=>[e.createVNode(u,{span:1.5},{default:e.withCtx((()=>[e.withDirectives(e.createElementVNode("view",null,[e.createVNode(c,{name:"checkmark-circle",size:20,color:"#999"})],512),[[e.vShow,t.isMultiple?-1==e.unref(s).indexOf(n[t.map.key]):n[t.map.key]!=e.unref(s)]]),e.withDirectives(e.createElementVNode("view",null,[e.createVNode(c,{name:"checkmark-circle-fill",size:20,color:"#2979ff"})],512),[[e.vShow,t.isMultiple?-1!=e.unref(s).indexOf(n[t.map.key]):n[t.map.key]==e.unref(s)]])])),_:2},1032,["span"]),e.createVNode(u,{span:5.5},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},e.toDisplayString(n[t.map.label]),1)])),_:2},1032,["span"]),e.createVNode(u,{span:5},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(n[t.map.label]),1)])),_:2},1024)])),_:2},1024)],10,["index","onClick"])))),128)),e.withDirectives(e.createVNode(w,{fixed:!0},null,512),[[e.vShow,n.$xeu.isEmpty(t.rangeData)]])],32)])])),_:1},512)])}}},[["__scopeId","data-v-dbcbf3a4"]]),bc=Ve({__name:"form",setup(t){const n=e.ref(),o=e.ref({orgId:"",name:"",category:"",sortCode:99}),r=e.reactive({orgId:[{type:"string",required:!0,message:"请选择所属机构",trigger:["change"]}],name:[{type:"string",required:!0,message:"请输入岗位名称",trigger:["blur","change"]}],category:[{type:"string",required:!0,message:"请选择岗位分类",trigger:["change"]}],sortCode:[{type:"number",required:!0,message:"请输入排序",trigger:["blur","change"]}]}),i={orgTreeApi:e=>{return(t=e,ro({url:"/biz/position/orgTreeSelector",method:"get",data:t})).then((e=>Promise.resolve(e)));var t}},a=uni.$snowy.tool.dictList("POSITION_CATEGORY");oe((e=>{var t;e.id&&(t={id:e.id},ro({url:"/biz/position/detail",method:"get",data:t})).then((e=>{o.value=null==e?void 0:e.data}))}));const s=()=>{n.value.validate().then((e=>{(function(e,t=!0){return ro({url:"/biz/position/"+(t?"add":"edit"),method:"post",data:e})})(o.value,!o.value.id).then((e=>{uni.$emit("formBack",{data:e.data}),uni.navigateBack({delta:1})}))}))};return(t,l)=>{const c=ee(e.resolveDynamicComponent("snowy-org-picker"),ic),u=ee(e.resolveDynamicComponent("uv-form-item"),Qs),d=ee(e.resolveDynamicComponent("uv-input"),nl),h=ee(e.resolveDynamicComponent("snowy-sel-picker"),vc),p=ee(e.resolveDynamicComponent("uv-number-box"),pc),f=ee(e.resolveDynamicComponent("uv-form"),xl),m=ee(e.resolveDynamicComponent("tui-button"),Sl);return e.openBlock(),e.createElementBlock("view",{class:"container snowy-shadow"},[e.createVNode(f,{ref_key:"formRef",ref:n,model:o.value,rules:r,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:e.withCtx((()=>[e.createVNode(u,{label:"所属机构",prop:"orgId",required:""},{default:e.withCtx((()=>[e.createVNode(c,{modelValue:o.value.orgId,"onUpdate:modelValue":l[0]||(l[0]=e=>o.value.orgId=e),placeholder:"请选择所属机构","org-tree-api":i.orgTreeApi},null,8,["modelValue","org-tree-api"])])),_:1}),e.createVNode(u,{label:"岗位名称",prop:"name",required:""},{default:e.withCtx((()=>[e.createVNode(d,{modelValue:o.value.name,"onUpdate:modelValue":l[1]||(l[1]=e=>o.value.name=e),placeholder:"请输入岗位名称",fontSize:"27rpx"},null,8,["modelValue"])])),_:1}),e.createVNode(u,{label:"岗位分类",prop:"category",required:""},{default:e.withCtx((()=>[e.createVNode(h,{map:{key:"value",label:"text"},modelValue:o.value.category,"onUpdate:modelValue":l[2]||(l[2]=e=>o.value.category=e),rangeData:e.unref(a),placeholder:"请选择岗位分类"},null,8,["modelValue","rangeData"])])),_:1}),e.createVNode(u,{label:"排序",prop:"sortCode",required:""},{default:e.withCtx((()=>[e.createVNode(p,{modelValue:o.value.sortCode,"onUpdate:modelValue":l[3]||(l[3]=e=>o.value.sortCode=e),"button-size":"30",color:"#ffffff",bgColor:"#5677fc",iconStyle:"color: #fff",step:1,max:100},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),e.createVNode(m,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:s},{default:e.withCtx((()=>[e.createTextVNode("提交")])),_:1})])}}},[["__scopeId","data-v-c1d83b5f"]]);function wc(e){return ro({url:"/biz/user/orgTreeSelector",method:"get",data:e})}function kc(e){return ro({url:"/biz/user/positionSelector",method:"get",data:e})}function xc(e){return ro({url:"/biz/user/userSelector",method:"get",data:e})}const Sc=Ve({__name:"more",emits:["handleOk"],setup(t,{expose:n,emit:o}){const r=e.ref(),i=e.ref({}),a=()=>{uni.navigateTo({url:"/pages/biz/user/form?id="+i.value.id}),r.value.close()},s=()=>{uni.$snowy.modal.confirm(`是否确认删除【${i.value.name}】用户？`).then((()=>{var e;(e=[{id:i.value.id}],ro({url:"/biz/user/delete",method:"post",data:e})).then((e=>{o("handleOk"),r.value.close()}))}))},l=()=>{r.value.close()};return n({open:e=>{i.value=e,r.value.open()}}),(t,n)=>{const o=ee(e.resolveDynamicComponent("tui-list-cell"),Jl),i=ee(e.resolveDynamicComponent("tui-list-view"),Zl),c=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createBlock(c,{ref_key:"popRef",ref:r,mode:"bottom","bg-color":"null","z-index":"99"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"container"},[e.createVNode(i,{unlined:"all","background-color":"transparent"},{default:e.withCtx((()=>[t.$snowy.hasPerm("mobileBizUserEdit")?(e.openBlock(),e.createBlock(o,{key:0,hover:!0,arrow:!1,onClick:a,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 编辑 ")])),_:1})):e.createCommentVNode("",!0),t.$snowy.hasPerm("mobileBizUserDelete")?(e.openBlock(),e.createBlock(o,{key:1,hover:!0,arrow:!1,onClick:s,radius:10,"margin-top":2},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 刪除 ")])),_:1})):e.createCommentVNode("",!0),e.createVNode(o,{hover:!0,arrow:!1,onClick:l,"margin-top":10,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 取消 ")])),_:1})])),_:1})])])),_:1},512)}}},[["__scopeId","data-v-c4735759"]]),Cc=Ve({__name:"index",setup(t){const n=()=>{uni.navigateTo({url:"/pages/biz/user/form"})},o=e.ref(),r=e.ref([]),i=e.ref([]);oc().then((e=>{i.value=e.data,r.value.push({id:"0",name:"顶级",children:e.data})}));const a=e.reactive({}),s=e.reactive({current:1,size:10}),l=e.ref([]),c=e=>{var t;e&&(s.current=1,l.value=[]),Object.assign(s,a),(t=s,ro({url:"/biz/user/page",method:"get",data:t})).then((e=>{var t;uni.$xeu.isEmpty(null==(t=null==e?void 0:e.data)?void 0:t.records)||(l.value=l.value.concat(e.data.records),s.current++)})).finally((()=>{uni.stopPullDownRefresh()}))};c(!0);return ne((()=>{uni.$once("formBack",(e=>{c(!0)}))})),ie((()=>{c(!0)})),re((()=>{c()})),(t,s)=>{const u=ee(e.resolveDynamicComponent("snowy-search"),Yl),d=ee(e.resolveDynamicComponent("tui-list-cell"),Jl),h=ee(e.resolveDynamicComponent("tui-list-view"),Zl),p=ee(e.resolveDynamicComponent("snowy-empty"),zl),f=ee(e.resolveDynamicComponent("snowy-float-btn"),Gl);return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(u,{placeholder:"请输入流程名称",modelValue:a.searchKey,"onUpdate:modelValue":s[0]||(s[0]=e=>a.searchKey=e),onConfirm:s[1]||(s[1]=e=>c(!0)),onClear:s[2]||(s[2]=e=>c(!0))},null,8,["modelValue"]),e.createElementVNode("view",{class:"crumb snowy-shadow"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(r.value,((t,n)=>(e.openBlock(),e.createElementBlock("text",{key:n,class:e.normalizeClass(["text-center",n===r.value.length-1?"uni-secondary-color":"uni-primary"]),onClick:e=>((e,t)=>{i.value=e.children,r.value.splice(t+1,r.value.length-(t+1)),a.orgId="0"===e.id?"":e.id,c(!0)})(t,n)},e.toDisplayString(t.name+(n===r.value.length-1?"":" | ")),11,["onClick"])))),128))]),e.createElementVNode("view",{class:"org-list snowy-shadow"},[e.createVNode(h,{unlined:"all"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.value,((t,n)=>(e.openBlock(),e.createBlock(d,{key:n,"line-left":0,hover:!!t.children,arrow:!!t.children,onClick:e=>((e,t)=>{i.value=e.children,r.value.push(e),a.orgId=e.id,c(!0)})(t)},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"},["COMPANY"===t.category?(e.openBlock(),e.createElementBlock("image",{key:0,class:"item-img",src:"/static/svg/org/company.svg",mode:"widthFix"})):e.createCommentVNode("",!0),"DEPT"===t.category?(e.openBlock(),e.createElementBlock("image",{key:1,class:"item-img",src:"/static/svg/org/department.svg",mode:"widthFix"})):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"item-left"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"item-right"})])])),_:2},1032,["hover","arrow","onClick"])))),128))])),_:1})]),e.createElementVNode("view",{class:"user-list snowy-shadow"},[e.createVNode(h,{unlined:"all"},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((t,n)=>(e.openBlock(),e.createBlock(d,{key:n,"line-left":0,hover:!0,arrow:!1,onClick:e=>{return n=t,void o.value.open(n);var n}},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"},[e.createElementVNode("image",{class:"item-img",src:t.avatar,mode:"widthFix"},null,8,["src"]),e.createElementVNode("view",{class:"item-left"},e.toDisplayString(t.name),1),e.createElementVNode("view",{class:"item-right"},e.toDisplayString(t.orgName+" | "+t.positionName+" | "+t.genderName),1)])])),_:2},1032,["onClick"])))),128))])),_:1}),e.withDirectives(e.createVNode(p,null,null,512),[[e.vShow,t.$xeu.isEmpty(l.value)]])]),t.$snowy.hasPerm("mobileBizUserAdd")?(e.openBlock(),e.createBlock(f,{key:0,onClick:n})):e.createCommentVNode("",!0),e.createVNode(Sc,{ref_key:"moreRef",ref:o,onHandleOk:s[3]||(s[3]=e=>c(!0))},null,512)],64)}}},[["__scopeId","data-v-a8600030"]]),_c={__name:"snowy-calendar",props:{modelValue:[String,Array],border:{type:Boolean,default:!0,required:!1},placeholder:{type:String,default:"请选择",required:!1},disabled:{type:Boolean,default:!1,required:!1}},emits:["update:modelValue","cancel","confirm"],setup(t,{emit:n}){const o=t,{proxy:r}=e.getCurrentInstance(),i=e.ref(""),a=e.ref();i.value=o.modelValue;const s=()=>{a.value.open()},l=e=>{i.value=e.fulldate,n("update:modelValue",e.fulldate),uni.$uv.formValidate(r,"change")};return(n,r)=>{const c=ee(e.resolveDynamicComponent("uv-calendars"),Hs);return e.openBlock(),e.createElementBlock("view",{class:"snowy-calendar"},[e.createElementVNode("view",{class:e.normalizeClass(["input",{"input-disabled":o.disabled}]),onClick:s},[e.createElementVNode("view",{class:e.normalizeClass(["input-value",{"input-value-border":o.border,"input-value-disabled":o.disabled}])},[n.$xeu.isEmpty(i.value)?(e.openBlock(),e.createElementBlock("view",{key:1,class:"placeholder"},e.toDisplayString(t.placeholder),1)):(e.openBlock(),e.createElementBlock("view",{key:0},e.toDisplayString(i.value),1))],2)],2),e.createVNode(c,{ref_key:"calendarRef",ref:a,date:i.value,onConfirm:l},null,8,["date"])])}}},Nc=Ve(_c,[["__scopeId","data-v-5fa31f5b"]]),Dc={props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},placeholder:{type:[String,Number],default:""},placeholderClass:{type:String,default:"textarea-placeholder"},placeholderStyle:{type:[String,Object],default:"color: #c0c4cc"},height:{type:[String,Number],default:70},confirmType:{type:String,default:"done"},disabled:{type:Boolean,default:!1},count:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},autoHeight:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1},cursorSpacing:{type:Number,default:0},cursor:{type:[String,Number],default:""},showConfirmBar:{type:Boolean,default:!0},selectionStart:{type:Number,default:-1},selectionEnd:{type:Number,default:-1},adjustPosition:{type:Boolean,default:!0},disableDefaultPadding:{type:Boolean,default:!1},holdKeyboard:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:140},border:{type:String,default:"surround"},formatter:{type:[Function,null],default:null},ignoreCompositionEvent:{type:Boolean,default:!0},confirmHold:{type:Boolean,default:!1},textStyle:{type:[Object,String],default:()=>{}},countStyle:{type:[Object,String],default:()=>{}},...null==(H=null==(j=uni.$uv)?void 0:j.props)?void 0:H.textarea}},Ec={name:"uv-textarea",mixins:[X,Ee,Dc],data:()=>({innerValue:"",focused:!1,innerFormatter:e=>e}),created(){this.innerValue=this.modelValue},watch:{value(e){this.innerValue=e},modelValue(e){this.innerValue=e}},computed:{textareaClass(){let e=[],{border:t,disabled:n}=this;return"surround"===t&&(e=e.concat(["uv-border","uv-textarea--radius"])),"bottom"===t&&(e=e.concat(["uv-border-bottom","uv-textarea--no-radius"])),n&&e.push("uv-textarea--disabled"),e.join(" ")},textareaStyle(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))},maxlen(){return this.maxlength<0?this.maxlength<0?-1:140:this.maxlength},getCount(){try{return this.innerValue.length>this.maxlen?this.maxlen:this.innerValue.length}catch(Er){return 0}}},methods:{setFormatter(e){this.innerFormatter=e},onFocus(e){this.$emit("focus",e)},onBlur(e){this.$emit("blur",e),this.$uv.formValidate(this,"blur")},onLinechange(e){this.$emit("linechange",e)},onInput(e){let{value:t=""}=e.detail||{};const n=(this.formatter||this.innerFormatter)(t);this.innerValue=t,this.$nextTick((()=>{this.innerValue=n,this.valueChange()}))},valueChange(){const e=this.innerValue;this.$nextTick((()=>{this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.$uv.formValidate(this,"change")}))},onConfirm(e){this.$emit("confirm",e)},onKeyboardheightchange(e){this.$emit("keyboardheightchange",e)}}};const Tc=Ve(Ec,[["render",function(t,n,o,r,i,a){return e.openBlock(),e.createElementBlock("view",{class:e.normalizeClass(["uv-textarea",a.textareaClass]),style:e.normalizeStyle([a.textareaStyle])},[e.createElementVNode("textarea",{class:"uv-textarea__field",value:i.innerValue,style:e.normalizeStyle([{height:t.autoHeight?"auto":t.$uv.addUnit(t.height)},t.$uv.addStyle(t.textStyle)]),placeholder:t.placeholder,"placeholder-style":t.$uv.addStyle(t.placeholderStyle,"string"),"placeholder-class":t.placeholderClass,disabled:t.disabled,focus:t.focus,autoHeight:t.autoHeight,fixed:t.fixed,cursorSpacing:t.cursorSpacing,cursor:t.cursor,showConfirmBar:t.showConfirmBar,selectionStart:t.selectionStart,selectionEnd:t.selectionEnd,adjustPosition:t.adjustPosition,disableDefaultPadding:t.disableDefaultPadding,holdKeyboard:t.holdKeyboard,maxlength:a.maxlen,confirmType:t.confirmType,ignoreCompositionEvent:t.ignoreCompositionEvent,"confirm-hold":t.confirmHold,onFocus:n[0]||(n[0]=(...e)=>a.onFocus&&a.onFocus(...e)),onBlur:n[1]||(n[1]=(...e)=>a.onBlur&&a.onBlur(...e)),onLinechange:n[2]||(n[2]=(...e)=>a.onLinechange&&a.onLinechange(...e)),onInput:n[3]||(n[3]=(...e)=>a.onInput&&a.onInput(...e)),onConfirm:n[4]||(n[4]=(...e)=>a.onConfirm&&a.onConfirm(...e)),onKeyboardheightchange:n[5]||(n[5]=(...e)=>a.onKeyboardheightchange&&a.onKeyboardheightchange(...e))},null,44,["value","placeholder","placeholder-style","placeholder-class","disabled","focus","autoHeight","fixed","cursorSpacing","cursor","showConfirmBar","selectionStart","selectionEnd","adjustPosition","disableDefaultPadding","holdKeyboard","maxlength","confirmType","ignoreCompositionEvent","confirm-hold"]),t.count&&-1!=a.maxlen?(e.openBlock(),e.createElementBlock("text",{key:0,class:"uv-textarea__count",style:e.normalizeStyle([{"background-color":t.disabled?"transparent":"#fff"},t.$uv.addStyle(t.countStyle)])},e.toDisplayString(a.getCount)+"/"+e.toDisplayString(a.maxlen),5)):e.createCommentVNode("",!0)],6)}],["__scopeId","data-v-7e2cf142"]]),Bc={__name:"form-position",props:{modelValue:{type:String,default:"",required:!1}},emits:["update:modelValue"],setup(t,{expose:n,emit:o}){const r=t,{proxy:i}=e.getCurrentInstance(),a=e.reactive({orgId:[{type:"string",required:!0,message:"请选择机构",trigger:["change"]}],positionId:[{type:"string",required:!0,message:"请选择职位",trigger:["change"]}]}),s=e.ref([]),l={orgTreeApi:e=>wc(e).then((e=>Promise.resolve(e))),userPageApi:e=>xc(e).then((e=>Promise.resolve(e))),checkedUserListApi:e=>Wn(e).then((e=>Promise.resolve(e)))},c=e.ref([]),u=e.ref([]);e.watch((()=>r.modelValue),((e,t)=>{d()}),{deep:!1,immediate:!1});const d=()=>{r.modelValue?s.value=uni.$xeu.clone(JSON.parse(r.modelValue),!0):s.value=[],s.value.forEach(((t,n)=>{void 0!==c.value[n]&&null!==c.value[n]||(c.value[n]={}),c.value[n].orgId=t.orgId,p(!0,n),e.nextTick((()=>{i.$refs[`positionRef${n}`][0].initData(),i.$refs[`directorRef${n}`][0].initData()}))}))},h=(e,t)=>{uni.$xeu.isEmpty(e)||Hn({idList:[e]}).then((e=>{t(e.data[0])}))},p=(e,t)=>{e&&(c.value[t].current=1,c.value[t].size=10,u.value[t]=[]),kc(c.value[t]).then((e=>{var n;uni.$xeu.isEmpty(null==(n=null==e?void 0:e.data)?void 0:n.records)||(u.value[t]=u.value[t].concat(e.data.records),c.value[t].current++)}))},f=()=>{s.value.push({orgId:"",positionId:"",directorId:""}),u.value.push([]),c.value.push({})};return n({formListEmitAndValidate:()=>{s.value&&s.value.length>0&&o("update:modelValue",JSON.stringify(s.value));const e=[];return s.value.forEach(((t,n)=>{e.push(new Promise(((e,t)=>{i.$refs[`formRef${n}`][0].validate().then((t=>{e(t)})).catch((e=>{t(e)}))})))})),new Promise(((t,n)=>{Promise.all(e).then((e=>{t(e)})).catch((e=>{n(e)}))}))}}),(t,n)=>{const o=ee(e.resolveDynamicComponent("tui-button"),Sl),r=ee(e.resolveDynamicComponent("snowy-org-picker"),ic),i=ee(e.resolveDynamicComponent("uv-form-item"),Qs),d=ee(e.resolveDynamicComponent("snowy-sel-picker"),vc),m=ee(e.resolveDynamicComponent("snowy-user-picker"),fc),g=ee(e.resolveDynamicComponent("uv-form"),xl);return e.openBlock(),e.createElementBlock("view",{style:{width:"100%"}},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(s.value,((n,f)=>(e.openBlock(),e.createElementBlock("view",{key:f},[e.createVNode(o,{margin:"0 0 50rpx 0",preventClick:!0,type:"danger",onClick:e=>(e=>{s.value.splice(e,1),u.value.splice(e,1),c.value.splice(e,1)})(f)},{default:e.withCtx((()=>[e.createTextVNode("删除任职")])),_:2},1032,["onClick"]),(e.openBlock(),e.createBlock(g,{key:f,ref_for:!0,ref:`formRef${f}`,model:n,rules:a,"label-position":"top",labelWidth:"auto"},{default:e.withCtx((()=>[e.createVNode(i,{label:"机构",prop:"orgId",required:""},{default:e.withCtx((()=>[e.createVNode(r,{modelValue:n.orgId,"onUpdate:modelValue":e=>n.orgId=e,placeholder:"请选择机构",onConfirm:e=>{(({curSelOrgId:e,curSelOrg:t},n)=>{s.value[n].positionId=null,c.value[n].orgId=e,p(!0,n)})(e,f)},"org-tree-api":l.orgTreeApi,onValidateField:e=>t.$refs[`formRef${f}`][0].validateField("orgId")},null,8,["modelValue","onUpdate:modelValue","onConfirm","org-tree-api","onValidateField"])])),_:2},1024),e.createVNode(i,{label:"职位",prop:"positionId",required:""},{default:e.withCtx((()=>[e.createVNode(d,{ref_for:!0,ref:`positionRef${f}`,map:{key:"id",label:"name"},modelValue:n.positionId,"onUpdate:modelValue":e=>n.positionId=e,rangeData:u.value[f],placeholder:"请选择选择职位",isBigData:!0,onScrollToLower:e=>(e=>{p(!1,e)})(f),onQueryCurSelData:h,onValidateField:e=>t.$refs[`formRef${f}`][0].validateField("positionId")},null,8,["modelValue","onUpdate:modelValue","rangeData","onScrollToLower","onValidateField"])])),_:2},1024),e.createVNode(i,{label:"主管",prop:"directorId"},{default:e.withCtx((()=>[e.createVNode(m,{ref_for:!0,ref:`directorRef${f}`,modelValue:n.directorId,"onUpdate:modelValue":e=>n.directorId=e,placeholder:"请选择主管","org-tree-api":l.orgTreeApi,"user-page-api":l.userPageApi,"checked-user-list-api":l.checkedUserListApi},null,8,["modelValue","onUpdate:modelValue","org-tree-api","user-page-api","checked-user-list-api"])])),_:2},1024)])),_:2},1032,["model","rules"]))])))),128)),e.createVNode(o,{margin:"50rpx 0 0 0",preventClick:!0,type:"primary",onClick:f},{default:e.withCtx((()=>[e.createTextVNode("增加任职")])),_:1})])}}},Vc={__name:"form",setup(t){const n=e.ref(0),o=e.ref(),r=e.ref({account:"",name:"",gender:"",nickname:"",phone:"",email:"",birthday:"",orgId:"",positionId:"",directorId:"",empNo:"",positionLevel:"",entryDate:"",positionJson:"",nation:"",nativePlace:"",homeAddress:"",mailingAddress:"",idCardType:"",idCardNumber:"",cultureLevel:"",politicalOutlook:"",college:"",education:"",eduLength:"",degree:"",homeTel:"",officeTel:"",emergencyContact:"",emergencyPhone:"",emergencyAddress:""}),i=e.reactive({account:[{type:"string",required:!0,message:"请输入账号",trigger:["blur","change"]}],name:[{type:"string",required:!0,message:"请输入姓名",trigger:["blur","change"]}],phone:[{pattern:/^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,message:"请填写符合要求的11位手机号",trigger:["blur","change"]}],email:[{pattern:/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,message:"请填写正确的邮箱号",trigger:["blur","change"]}],orgId:[{type:"string",required:!0,message:"选择组织",trigger:["change"]}],positionId:[{type:"string",required:!0,message:"选择职位",trigger:["change"]}]}),a=uni.$snowy.tool.dictList("GENDER"),s=e.ref([]),l=uni.$snowy.tool.dictList("NATION"),c=uni.$snowy.tool.dictList("IDCARD_TYPE"),u=uni.$snowy.tool.dictList("CULTURE_LEVEL"),d=e.ref(),h={orgTreeApi:e=>wc(e).then((e=>Promise.resolve(e))),userPageApi:e=>xc(e).then((e=>Promise.resolve(e))),checkedUserListApi:e=>Wn(e).then((e=>Promise.resolve(e)))},p=e.reactive({current:1,size:10}),f=e=>{e&&(p.current=1,s.value=[]),kc(p).then((e=>{var t;uni.$xeu.isEmpty(null==(t=null==e?void 0:e.data)?void 0:t.records)||(s.value=s.value.concat(e.data.records),p.current++)}))};oe((e=>{var t;e.id&&(t={id:e.id},ro({url:"/biz/user/detail",method:"get",data:t})).then((e=>{r.value=null==e?void 0:e.data,r.value.orgId&&(p.orgId=r.value.orgId,f(!0))}))}));const m=({curSelOrgId:e,curSelOrg:t})=>{r.value.positionId=null,p.orgId=e,f(!0)},g=(e,t)=>{uni.$xeu.isEmpty(e)||Hn({idList:[e]}).then((e=>{t(e.data[0])}))},y=()=>{f()},v=()=>{d.value.formListEmitAndValidate().then((e=>{o.value.validate().then((e=>{(function(e,t=!0){return ro({url:"/biz/user/"+(t?"add":"edit"),method:"post",data:e})})(r.value,!r.value.id).then((e=>{uni.$emit("formBack",{data:e.data}),uni.navigateBack({delta:1})}))})).catch((e=>{Q("error","at pages/biz/user/form.vue:314","父表单错误信息：",e)}))})).catch((e=>{Q("error","at pages/biz/user/form.vue:317","子表单错误信息：",e)}))};return(t,p)=>{const f=ee(e.resolveDynamicComponent("tui-tabs"),Ol),b=ee(e.resolveDynamicComponent("uv-input"),nl),w=ee(e.resolveDynamicComponent("uv-form-item"),Qs),k=ee(e.resolveDynamicComponent("uv-radio"),lc),x=ee(e.resolveDynamicComponent("uv-radio-group"),uc),S=ee(e.resolveDynamicComponent("snowy-calendar"),Nc),C=ee(e.resolveDynamicComponent("snowy-org-picker"),ic),_=ee(e.resolveDynamicComponent("snowy-sel-picker"),vc),N=ee(e.resolveDynamicComponent("snowy-user-picker"),fc),D=ee(e.resolveDynamicComponent("uv-textarea"),Tc),E=ee(e.resolveDynamicComponent("uv-form"),xl),T=ee(e.resolveDynamicComponent("tui-button"),Sl);return e.openBlock(),e.createElementBlock("view",null,[e.createElementVNode("view",null,[e.createVNode(f,{top:0,isFixed:!0,tabs:[{name:"基础信息"},{name:"更多信息"}],currentTab:n.value,onChange:p[0]||(p[0]=e=>{n.value!=e.index&&(n.value=e.index)})},null,8,["currentTab"])]),e.createElementVNode("view",{class:"container snowy-shadow"},[e.createVNode(E,{ref_key:"formRef",ref:o,model:r.value,"label-position":"top",rules:i,labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:e.withCtx((()=>[e.withDirectives(e.createElementVNode("view",null,[e.createVNode(w,{label:"账号",prop:"account",required:""},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.account,"onUpdate:modelValue":p[1]||(p[1]=e=>r.value.account=e),placeholder:"请输入账号"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"姓名",prop:"name",required:""},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.name,"onUpdate:modelValue":p[2]||(p[2]=e=>r.value.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"性别",prop:"gender"},{default:e.withCtx((()=>[e.createVNode(x,{modelValue:r.value.category,"onUpdate:modelValue":p[3]||(p[3]=e=>r.value.category=e)},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(a),((t,n)=>(e.openBlock(),e.createBlock(k,{customStyle:{marginRight:"50rpx"},key:n,label:t.text,name:t.value},null,8,["label","name"])))),128))])),_:1},8,["modelValue"])])),_:1}),e.createVNode(w,{label:"昵称",prop:"nickname"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.nickname,"onUpdate:modelValue":p[4]||(p[4]=e=>r.value.nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"手机号",prop:"phone"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.phone,"onUpdate:modelValue":p[5]||(p[5]=e=>r.value.phone=e),placeholder:"请输入手机"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"邮箱",prop:"email"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.email,"onUpdate:modelValue":p[6]||(p[6]=e=>r.value.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"出生日期",prop:"birthday"},{default:e.withCtx((()=>[e.createVNode(S,{modelValue:r.value.birthday,"onUpdate:modelValue":p[7]||(p[7]=e=>r.value.birthday=e),placeholder:"请选择出生日期"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"选择组织",prop:"orgId",required:""},{default:e.withCtx((()=>[e.createVNode(C,{modelValue:r.value.orgId,"onUpdate:modelValue":p[8]||(p[8]=e=>r.value.orgId=e),placeholder:"请选择组织",onConfirm:m,"org-tree-api":h.orgTreeApi},null,8,["modelValue","org-tree-api"])])),_:1}),e.createVNode(w,{label:"选择职位",prop:"positionId",required:""},{default:e.withCtx((()=>[e.createVNode(_,{map:{key:"id",label:"name"},modelValue:r.value.positionId,"onUpdate:modelValue":p[9]||(p[9]=e=>r.value.positionId=e),rangeData:s.value,placeholder:"请选择选择职位",isBigData:!0,onQueryCurSelData:g,onScrollToLower:y},null,8,["modelValue","rangeData"])])),_:1}),e.createVNode(w,{label:"选择主管",prop:"directorId"},{default:e.withCtx((()=>[e.createVNode(N,{modelValue:r.value.directorId,"onUpdate:modelValue":p[10]||(p[10]=e=>r.value.directorId=e),placeholder:"请选择主管","org-tree-api":h.orgTreeApi,"user-page-api":h.userPageApi,"checked-user-list-api":h.checkedUserListApi},null,8,["modelValue","org-tree-api","user-page-api","checked-user-list-api"])])),_:1}),e.createVNode(w,{label:"员工编号",prop:"empNo"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.empNo,"onUpdate:modelValue":p[11]||(p[11]=e=>r.value.empNo=e),placeholder:"请输入员工编号"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"职位级别",prop:"positionLevel"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.positionLevel,"onUpdate:modelValue":p[12]||(p[12]=e=>r.value.positionLevel=e),placeholder:"请输入职位级别"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"入职日期",prop:"entryDate"},{default:e.withCtx((()=>[e.createVNode(S,{modelValue:r.value.entryDate,"onUpdate:modelValue":p[13]||(p[13]=e=>r.value.entryDate=e),placeholder:"请选择入职日期"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"任职信息",prop:"positionJson"},{default:e.withCtx((()=>[e.createVNode(Bc,{modelValue:r.value.positionJson,"onUpdate:modelValue":p[14]||(p[14]=e=>r.value.positionJson=e),ref_key:"positionJsonRef",ref:d},null,8,["modelValue"])])),_:1})],512),[[e.vShow,0===n.value]]),e.withDirectives(e.createElementVNode("view",null,[e.createVNode(w,{label:"民族",prop:"nation"},{default:e.withCtx((()=>[e.createVNode(_,{map:{key:"value",label:"text"},modelValue:r.value.nation,"onUpdate:modelValue":p[15]||(p[15]=e=>r.value.nation=e),rangeData:e.unref(l),placeholder:"请选择民族"},null,8,["modelValue","rangeData"])])),_:1}),e.createVNode(w,{label:"籍贯",prop:"nativePlace"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.nativePlace,"onUpdate:modelValue":p[16]||(p[16]=e=>r.value.nativePlace=e),placeholder:"请输入籍贯"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"家庭住址",prop:"homeAddress"},{default:e.withCtx((()=>[e.createVNode(D,{modelValue:r.value.homeAddress,"onUpdate:modelValue":p[17]||(p[17]=e=>r.value.homeAddress=e),placeholder:"请输入家庭住址"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"通信地址",prop:"mailingAddress"},{default:e.withCtx((()=>[e.createVNode(D,{modelValue:r.value.mailingAddress,"onUpdate:modelValue":p[18]||(p[18]=e=>r.value.mailingAddress=e),placeholder:"请输入通信地址"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"证件类型",prop:"idCardType"},{default:e.withCtx((()=>[e.createVNode(_,{map:{key:"value",label:"text"},modelValue:r.value.idCardType,"onUpdate:modelValue":p[19]||(p[19]=e=>r.value.idCardType=e),rangeData:e.unref(c),placeholder:"请选择证件类型"},null,8,["modelValue","rangeData"])])),_:1}),e.createVNode(w,{label:"证件号码",prop:"idCardNumber"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.idCardNumber,"onUpdate:modelValue":p[20]||(p[20]=e=>r.value.idCardNumber=e),placeholder:"请输入证件号码"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"文化程度",prop:"cultureLevel"},{default:e.withCtx((()=>[e.createVNode(_,{map:{key:"value",label:"text"},modelValue:r.value.cultureLevel,"onUpdate:modelValue":p[21]||(p[21]=e=>r.value.cultureLevel=e),rangeData:e.unref(u),placeholder:"请选择文化程度"},null,8,["modelValue","rangeData"])])),_:1}),e.createVNode(w,{label:"政治面貌",prop:"politicalOutlook"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.politicalOutlook,"onUpdate:modelValue":p[22]||(p[22]=e=>r.value.politicalOutlook=e),placeholder:"请输入政治面貌"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"毕业学校",prop:"college"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.college,"onUpdate:modelValue":p[23]||(p[23]=e=>r.value.college=e),placeholder:"请输入毕业学校"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"学历",prop:"education"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.education,"onUpdate:modelValue":p[24]||(p[24]=e=>r.value.education=e),placeholder:"请输入学历"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"学制",prop:"eduLength"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.eduLength,"onUpdate:modelValue":p[25]||(p[25]=e=>r.value.eduLength=e),placeholder:"请输入学制"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"学位",prop:"degree"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.degree,"onUpdate:modelValue":p[26]||(p[26]=e=>r.value.degree=e),placeholder:"请输入学位"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"家庭电话",prop:"homeTel"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.homeTel,"onUpdate:modelValue":p[27]||(p[27]=e=>r.value.homeTel=e),placeholder:"请输入家庭电话"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"办公电话",prop:"officeTel"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.officeTel,"onUpdate:modelValue":p[28]||(p[28]=e=>r.value.officeTel=e),placeholder:"请输入办公电话"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"紧急联系人",prop:"emergencyContact"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.emergencyContact,"onUpdate:modelValue":p[29]||(p[29]=e=>r.value.emergencyContact=e),placeholder:"请输入紧急联系人"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"紧急联系电话",prop:"emergencyPhone"},{default:e.withCtx((()=>[e.createVNode(b,{modelValue:r.value.emergencyPhone,"onUpdate:modelValue":p[30]||(p[30]=e=>r.value.emergencyPhone=e),placeholder:"请输入紧急联系电话"},null,8,["modelValue"])])),_:1}),e.createVNode(w,{label:"紧急联系人地址",prop:"emergencyAddress"},{default:e.withCtx((()=>[e.createVNode(D,{modelValue:r.value.emergencyAddress,"onUpdate:modelValue":p[31]||(p[31]=e=>r.value.emergencyAddress=e),placeholder:"请输入紧急联系人地址"},null,8,["modelValue"])])),_:1})],512),[[e.vShow,1===n.value]])])),_:1},8,["model","rules"]),e.createVNode(T,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:v},{default:e.withCtx((()=>[e.createTextVNode("提交")])),_:1})])])}}},Ic=Ve(Vc,[["__scopeId","data-v-3ecad75a"]]),Ac=Ve({__name:"index",setup(t){const n=e.computed((()=>Zn.getters.userInfo));return(t,o)=>{const r=ee(e.resolveDynamicComponent("uv-icon"),Ae),i=ee(e.resolveDynamicComponent("uv-col"),Ks),a=ee(e.resolveDynamicComponent("uv-row"),Gs);return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"1"},{default:e.withCtx((()=>[e.createVNode(r,{size:"18",name:"tags-fill",color:"#5677fc"})])),_:1}),e.createVNode(i,{span:"5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"账户")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(e.unref(n).account),1)])),_:1})])),_:1})]),e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"1"},{default:e.withCtx((()=>[e.createVNode(r,{size:"18",name:"tags-fill",color:"#5677fc"})])),_:1}),e.createVNode(i,{span:"5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"姓名")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(e.unref(n).name),1)])),_:1})])),_:1})]),e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"1"},{default:e.withCtx((()=>[e.createVNode(r,{size:"18",name:"tags-fill",color:"#5677fc"})])),_:1}),e.createVNode(i,{span:"5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"手机")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(e.unref(n).phone),1)])),_:1})])),_:1})]),e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"1"},{default:e.withCtx((()=>[e.createVNode(r,{size:"18",name:"tags-fill",color:"#5677fc"})])),_:1}),e.createVNode(i,{span:"5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"昵称")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(e.unref(n).nickname),1)])),_:1})])),_:1})]),e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"1"},{default:e.withCtx((()=>[e.createVNode(r,{size:"18",name:"tags-fill",color:"#5677fc"})])),_:1}),e.createVNode(i,{span:"5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"性别")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(e.unref(n).gender),1)])),_:1})])),_:1})]),e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"1"},{default:e.withCtx((()=>[e.createVNode(r,{size:"18",name:"tags-fill",color:"#5677fc"})])),_:1}),e.createVNode(i,{span:"5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"生日")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(e.unref(n).birthday),1)])),_:1})])),_:1})]),e.createElementVNode("view",{class:"item"},[e.createVNode(a,null,{default:e.withCtx((()=>[e.createVNode(i,{span:"1"},{default:e.withCtx((()=>[e.createVNode(r,{size:"18",name:"tags-fill",color:"#5677fc"})])),_:1}),e.createVNode(i,{span:"5"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},"邮箱")])),_:1}),e.createVNode(i,{span:"6",textAlign:"right"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-right snowy-bold snowy-ellipsis"},e.toDisplayString(e.unref(n).email),1)])),_:1})])),_:1})])],64)}}},[["__scopeId","data-v-75b63e1b"]]),Pc=Ve({__name:"edit",setup(t){const n=e.ref(),o=e.ref(uni.$xeu.clone(Zn.getters.userInfo,!0)),r=e.reactive({name:[{type:"string",required:!0,message:"请输入姓名",trigger:["blur","change"]}],phone:[{pattern:/^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/,message:"请填写符合要求的11位手机号",trigger:["blur","change"]}],email:[{pattern:/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,message:"请填写正确的邮箱号",trigger:["blur","change"]}]}),i=uni.$snowy.tool.dictList("GENDER"),a=()=>{n.value.validate().then((e=>{var t;(t=o.value,ro({url:"/sys/userCenter/updateUserInfo",method:"post",data:t})).then((e=>{Zn.commit("SET_userInfo",o.value)}))}))};return(t,s)=>{const l=ee(e.resolveDynamicComponent("uv-input"),nl),c=ee(e.resolveDynamicComponent("uv-form-item"),Qs),u=ee(e.resolveDynamicComponent("uv-radio"),lc),d=ee(e.resolveDynamicComponent("uv-radio-group"),uc),h=ee(e.resolveDynamicComponent("snowy-calendar"),Nc),p=ee(e.resolveDynamicComponent("uv-form"),xl),f=ee(e.resolveDynamicComponent("tui-button"),Sl);return e.openBlock(),e.createElementBlock("view",{class:"edit-container snowy-shadow"},[e.createVNode(p,{ref_key:"formRef",ref:n,model:o.value,rules:r,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:e.withCtx((()=>[e.createVNode(c,{label:"姓名",prop:"name",required:""},{default:e.withCtx((()=>[e.createVNode(l,{modelValue:o.value.name,"onUpdate:modelValue":s[0]||(s[0]=e=>o.value.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),e.createVNode(c,{label:"手机",prop:"phone"},{default:e.withCtx((()=>[e.createVNode(l,{modelValue:o.value.phone,"onUpdate:modelValue":s[1]||(s[1]=e=>o.value.phone=e),placeholder:"请输入手机"},null,8,["modelValue"])])),_:1}),e.createVNode(c,{label:"昵称",prop:"nickname"},{default:e.withCtx((()=>[e.createVNode(l,{modelValue:o.value.nickname,"onUpdate:modelValue":s[2]||(s[2]=e=>o.value.nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),e.createVNode(c,{label:"性别",prop:"gender"},{default:e.withCtx((()=>[e.createVNode(d,{modelValue:o.value.gender,"onUpdate:modelValue":s[3]||(s[3]=e=>o.value.gender=e)},{default:e.withCtx((()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(i),((t,n)=>(e.openBlock(),e.createBlock(u,{customStyle:{marginRight:"50rpx"},key:n,label:t.text,name:t.value},null,8,["label","name"])))),128))])),_:1},8,["modelValue"])])),_:1}),e.createVNode(c,{label:"生日",prop:"birthday"},{default:e.withCtx((()=>[e.createVNode(h,{modelValue:o.value.birthday,"onUpdate:modelValue":s[4]||(s[4]=e=>o.value.birthday=e),placeholder:"请选择出生日期"},null,8,["modelValue"])])),_:1}),e.createVNode(c,{label:"邮箱",prop:"email"},{default:e.withCtx((()=>[e.createVNode(l,{modelValue:o.value.email,"onUpdate:modelValue":s[5]||(s[5]=e=>o.value.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),e.createVNode(f,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:a},{default:e.withCtx((()=>[e.createTextVNode("提交")])),_:1})])}}},[["__scopeId","data-v-301f7696"]]),$c={props:{text:{type:[Array],default:""},icon:{type:[String,Boolean,null],default:"volume"},mode:{type:String,default:""},color:{type:String,default:"#f9ae3d"},bgColor:{type:String,default:"#fdf6ec"},fontSize:{type:[String,Number],default:14},speed:{type:[String,Number],default:80},step:{type:Boolean,default:!1},duration:{type:[String,Number],default:1500},disableTouch:{type:Boolean,default:!0},disableScroll:{type:Boolean,default:!1},...null==(K=null==(W=uni.$uv)?void 0:W.props)?void 0:K.columnNotice}},Oc={emits:["click","close","change"],mixins:[X,Ee,$c],watch:{text:{immediate:!0,handler(e,t){this.$uv.test.array(e)||this.$uv.error("noticebar组件direction为column时，要求text参数为数组形式")}}},computed:{textStyle(){let e={};return e.color=this.color,e.fontSize=this.$uv.addUnit(this.fontSize),e},vertical(){return"horizontal"!=this.mode},swiperStyle:()=>({})},data:()=>({index:0}),methods:{noticeChange(e){this.index=e.detail.current,this.$emit("change",this.index)},clickHandler(){this.$emit("click",this.index)},close(){this.$emit("close")}}};const Fc=Ve(Oc,[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:"uv-notice",onClick:n[1]||(n[1]=(...e)=>a.clickHandler&&a.clickHandler(...e))},[e.renderSlot(t.$slots,"icon",{},(()=>[t.icon?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-notice__left-icon"},[e.createVNode(s,{name:t.icon,color:t.color,size:"19"},null,8,["name","color"])])):e.createCommentVNode("",!0)]),!0),e.createElementVNode("swiper",{"disable-touch":t.disableTouch,vertical:!t.step,circular:"",interval:t.duration,autoplay:!t.disableScroll,class:"uv-notice__swiper",style:e.normalizeStyle([a.swiperStyle]),onChange:n[0]||(n[0]=(...e)=>a.noticeChange&&a.noticeChange(...e))},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.text,((t,n)=>(e.openBlock(),e.createElementBlock("swiper-item",{key:n,class:"uv-notice__swiper__item"},[e.createElementVNode("text",{class:"uv-notice__swiper__item__text uv-line-1",style:e.normalizeStyle([a.textStyle])},e.toDisplayString(t),5)])))),128))],44,["disable-touch","vertical","interval","autoplay"]),["link","closable"].includes(t.mode)?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-notice__right-icon"},["link"===t.mode?(e.openBlock(),e.createBlock(s,{key:0,name:"arrow-right",size:17,color:t.color},null,8,["color"])):e.createCommentVNode("",!0),"closable"===t.mode?(e.openBlock(),e.createBlock(s,{key:1,name:"close",size:16,color:t.color,onClick:a.close},null,8,["color","onClick"])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-824d0701"]]),Mc={props:{text:{type:String,default:""},icon:{type:[String,Boolean,null],default:"volume"},mode:{type:String,default:""},color:{type:String,default:"#f9ae3d"},bgColor:{type:String,default:"#fdf6ec"},fontSize:{type:[String,Number],default:14},speed:{type:[String,Number],default:80},...null==(G=null==(Y=uni.$uv)?void 0:Y.props)?void 0:G.rowNotice}};const Lc=Ve({name:"uv-row-notice",emits:["click","close"],mixins:[X,Ee,Mc],data:()=>({animationDuration:"0",animationPlayState:"paused",nvueInit:!0,show:!0}),watch:{text:{immediate:!0,handler(e,t){this.vue(),this.$uv.test.string(e)||this.$uv.error("noticebar组件direction为row时，要求text参数为字符串形式")}},fontSize(){this.vue()},speed(){this.vue()}},computed:{textStyle(){let e={};return e.color=this.color,e.fontSize=this.$uv.addUnit(this.fontSize),e},animationStyle(){let e={};return e.animationDuration=this.animationDuration,e.animationPlayState=this.animationPlayState,e},innerText(){let e=[];const t=this.text?this.text.split(""):[];for(let n=0;n<t.length;n+=20)e.push(t.slice(n,n+20).join(""));return e}},mounted(){var e=getCurrentPages(),t=e[e.length-1].$getAppWebview();t.addEventListener("hide",(()=>{this.webviewHide=!0})),t.addEventListener("show",(()=>{this.webviewHide=!1})),this.init()},methods:{init(){this.vue(),this.$uv.test.string(this.text)||this.$uv.error("noticebar组件direction为row时，要求text参数为字符串形式")},async vue(){let e=0;await this.$uv.sleep(),e=(await this.$uvGetRect(".uv-notice__content__text")).width,(await this.$uvGetRect(".uv-notice__content")).width,this.animationDuration=e/this.$uv.getPx(this.speed)+"s",this.animationPlayState="paused",setTimeout((()=>{this.animationPlayState="running"}),10)},async nvue(){},loopAnimation(e,t){},getNvueRect(e){},clickHandler(e){this.$emit("click")},close(){this.$emit("close")}}},[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-icon"),Ae);return e.openBlock(),e.createElementBlock("view",{class:"uv-notice",onClick:n[0]||(n[0]=(...e)=>a.clickHandler&&a.clickHandler(...e))},[e.renderSlot(t.$slots,"icon",{},(()=>[t.icon?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-notice__left-icon"},[e.createVNode(s,{name:t.icon,color:t.color,size:"19"},null,8,["name","color"])])):e.createCommentVNode("",!0)]),!0),e.createElementVNode("view",{class:"uv-notice__content",ref:"uv-notice__content"},[e.createElementVNode("view",{ref:"uv-notice__content__text",class:"uv-notice__content__text",style:e.normalizeStyle([a.animationStyle])},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.innerText,((t,n)=>(e.openBlock(),e.createElementBlock("text",{key:n,style:e.normalizeStyle([a.textStyle])},e.toDisplayString(t),5)))),128))],4)],512),["link","closable"].includes(t.mode)?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-notice__right-icon"},["link"===t.mode?(e.openBlock(),e.createBlock(s,{key:0,name:"arrow-right",size:17,color:t.color},null,8,["color"])):e.createCommentVNode("",!0),"closable"===t.mode?(e.openBlock(),e.createBlock(s,{key:1,onClick:a.close,name:"close",size:16,color:t.color},null,8,["onClick","color"])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)])}],["__scopeId","data-v-878cb1c1"]]),zc={props:{text:{type:[Array,String],default:()=>[]},direction:{type:String,default:"row"},step:{type:Boolean,default:!1},icon:{type:[String,Boolean,null],default:"volume"},mode:{type:String,default:""},color:{type:String,default:"#f9ae3d"},bgColor:{type:String,default:"#fdf6ec"},speed:{type:[String,Number],default:80},fontSize:{type:[String,Number],default:14},duration:{type:[String,Number],default:2e3},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"},disableTouch:{type:Boolean,default:!0},disableScroll:{type:Boolean,default:!1},...null==(Z=null==(J=uni.$uv)?void 0:J.props)?void 0:Z.noticeBar}};const Rc=Ve({name:"uv-notice-bar",emits:["click","close","change"],mixins:[X,Ee,zc],data:()=>({show:!0}),methods:{click(e){this.$emit("click",e),this.url&&this.linkType&&this.openPage()},close(){this.show=!1,this.$emit("close")},change(e){this.$emit("change",e)}}},[["render",function(t,n,o,r,i,a){const s=ee(e.resolveDynamicComponent("uv-column-notice"),Fc),l=ee(e.resolveDynamicComponent("uv-row-notice"),Lc);return i.show?(e.openBlock(),e.createElementBlock("view",{key:0,class:"uv-notice-bar",style:e.normalizeStyle([{backgroundColor:t.bgColor},t.$uv.addStyle(t.customStyle)])},["column"===t.direction||"row"===t.direction&&t.step?(e.openBlock(),e.createBlock(s,{key:0,color:t.color,bgColor:t.bgColor,text:t.text,mode:t.mode,step:t.step,icon:t.icon,"disable-touch":t.disableTouch,"disable-scroll":t.disableScroll,fontSize:t.fontSize,duration:t.duration,onClose:a.close,onClick:a.click,onChange:a.change},null,8,["color","bgColor","text","mode","step","icon","disable-touch","disable-scroll","fontSize","duration","onClose","onClick","onChange"])):(e.openBlock(),e.createBlock(l,{key:1,color:t.color,bgColor:t.bgColor,text:t.text,mode:t.mode,fontSize:t.fontSize,speed:t.speed,url:t.url,linkType:t.linkType,icon:t.icon,onClose:a.close,onClick:a.click},null,8,["color","bgColor","text","mode","fontSize","speed","url","linkType","icon","onClose","onClick"]))],4)):e.createCommentVNode("",!0)}],["__scopeId","data-v-c11b4dfc"]]),Uc=Ve({__name:"more",setup(t,{expose:n}){const o=e.ref(),r=e.ref([]),i=e.ref(),a=()=>{l(r.value,i.value-1,i.value),Zn.commit("SET_homeConfigs",r.value),o.value.close()},s=()=>{l(r.value,i.value,i.value+1),Zn.commit("SET_homeConfigs",r.value),o.value.close()},l=(e,t,n)=>(e[t]=e.splice(n,1,e[t])[0],e),c=()=>{o.value.close()};return n({open:(e,t)=>{r.value=e,i.value=t,o.value.open()}}),(t,n)=>{const l=ee(e.resolveDynamicComponent("tui-list-cell"),Jl),u=ee(e.resolveDynamicComponent("tui-list-view"),Zl),d=ee(e.resolveDynamicComponent("uv-popup"),xs);return e.openBlock(),e.createBlock(d,{ref_key:"popRef",ref:o,mode:"bottom","bg-color":"null","z-index":"99"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"container"},[e.createVNode(u,{unlined:"all","background-color":"transparent"},{default:e.withCtx((()=>[0!=i.value?(e.openBlock(),e.createBlock(l,{key:0,hover:!0,arrow:!1,onClick:a,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 上移 ")])),_:1})):e.createCommentVNode("",!0),i.value!=r.value.length-1?(e.openBlock(),e.createBlock(l,{key:1,hover:!0,arrow:!1,onClick:s,radius:10,"margin-top":2},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 下移 ")])),_:1})):e.createCommentVNode("",!0),e.createVNode(l,{hover:!0,arrow:!1,onClick:c,"margin-top":10,radius:10},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item"}," 取消 ")])),_:1})])),_:1})])])),_:1},512)}}},[["__scopeId","data-v-3c0164c0"]]),qc={__name:"index",setup(t){const n=e.computed((()=>Zn.getters.homeConfigs)),o=e.ref(),r=(e,t)=>{o.value.open(n.value,t)};return(t,i)=>{const a=ee(e.resolveDynamicComponent("uv-notice-bar"),Rc),s=ee(e.resolveDynamicComponent("uv-icon"),Ae),l=ee(e.resolveDynamicComponent("uv-col"),Ks),c=ee(e.resolveDynamicComponent("uv-row"),Gs);return e.openBlock(),e.createElementBlock("view",{class:"home-config"},[e.createVNode(a,{direction:"column",disableScroll:"",text:["首页调整完成后,请重新登录,谢谢!"]}),e.createElementVNode("view",{style:{"margin-top":"20rpx"}},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(n),((t,o)=>(e.openBlock(),e.createElementBlock("view",{class:"item",key:o,index:o},[e.createVNode(c,null,{default:e.withCtx((()=>[e.createVNode(l,{span:"1",onClick:e=>r(0,o)},{default:e.withCtx((()=>[e.createVNode(s,{size:"20",name:"tags-fill",color:"#5677fc"})])),_:2},1032,["onClick"]),e.createVNode(l,{span:"9",onClick:e=>r(0,o)},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"item-left"},e.toDisplayString(t.name),1)])),_:2},1032,["onClick"]),e.createVNode(l,{span:"2"},{default:e.withCtx((()=>[e.createElementVNode("switch",{checked:t.isShow,style:{transform:"scale(0.7)"},onChange:e=>((e,t)=>{e.isShow=t.detail.value,Zn.commit("SET_homeConfigs",n.value)})(t,e)},null,40,["checked","onChange"])])),_:2},1024)])),_:2},1024)],8,["index"])))),128))]),e.createVNode(Uc,{ref_key:"moreRef",ref:o},null,512)])}}},jc=Ve(qc,[["__scopeId","data-v-def8c7da"]]),Hc=Ve({__name:"index",setup(t){const n=e.ref(),o=e.ref({oldPassword:"",newPassword:"",confirmPassword:""}),r={oldPassword:[{type:"string",required:!0,message:"旧密码不能为空",trigger:["blur","change"]}],newPassword:[{type:"string",required:!0,message:"新密码不能为空",trigger:["blur","change"]},{minLength:6,maxLength:20,message:"长度在 6 到 20 个字符"}],confirmPassword:[{type:"string",required:!0,message:"确认密码不能为空",trigger:["blur","change"]},{validator:(e,t,n)=>o.value.newPassword===t,message:"两次输入的密码不一致",trigger:["blur","change"]}]},i=()=>{n.value.validate().then((e=>{var t;(t={password:o.value.oldPassword,newPassword:o.value.newPassword},ro({url:"/sys/userCenter/updatePassword",method:"post",data:t})).then((e=>{}))}))};return(t,a)=>{const s=ee(e.resolveDynamicComponent("uv-input"),nl),l=ee(e.resolveDynamicComponent("uv-form-item"),Qs),c=ee(e.resolveDynamicComponent("tui-button"),Sl),u=ee(e.resolveDynamicComponent("uv-form"),xl);return e.openBlock(),e.createElementBlock("view",{class:"pwd-container snowy-shadow"},[e.createVNode(u,{ref_key:"formRef",ref:n,model:o.value,rules:r,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:e.withCtx((()=>[e.createVNode(l,{prop:"oldPassword",label:"旧密码",required:""},{default:e.withCtx((()=>[e.createVNode(s,{type:"password",modelValue:o.value.oldPassword,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value.oldPassword=e),placeholder:"请输入旧密码"},null,8,["modelValue"])])),_:1}),e.createVNode(l,{prop:"newPassword",label:"新密码",required:""},{default:e.withCtx((()=>[e.createVNode(s,{type:"password",modelValue:o.value.newPassword,"onUpdate:modelValue":a[1]||(a[1]=e=>o.value.newPassword=e),placeholder:"请输入新密码"},null,8,["modelValue"])])),_:1}),e.createVNode(l,{prop:"confirmPassword",label:"确认密码",required:""},{default:e.withCtx((()=>[e.createVNode(s,{type:"password",modelValue:o.value.confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=e=>o.value.confirmPassword=e),placeholder:"请确认新密码"},null,8,["modelValue"])])),_:1}),e.createVNode(c,{margin:"50rpx 0",preventClick:!0,shadow:!0,onClick:i},{default:e.withCtx((()=>[e.createTextVNode("提交")])),_:1})])),_:1},8,["model"])])}}},[["__scopeId","data-v-70dee2a4"]]);__definePage("pages/login",ao),__definePage("pages/home/<USER>",Tl),__definePage("pages/work/index",$l),__definePage("pages/msg/index",Ul),__definePage("pages/msg/detail",Hl),__definePage("pages/mine/index",Wl),__definePage("pages/config/index",Ql),__definePage("pages/config/form",ec),__definePage("pages/common/webview/index",tc),__definePage("pages/biz/org/index",rc),__definePage("pages/biz/org/form",mc),__definePage("pages/biz/position/index",yc),__definePage("pages/biz/position/form",bc),__definePage("pages/biz/user/index",Cc),__definePage("pages/biz/user/form",Ic),__definePage("pages/mine/info/index",Ac),__definePage("pages/mine/info/edit",Pc),__definePage("pages/mine/home-config/index",jc),__definePage("pages/mine/pwd/index",Hc);const Wc={__name:"App",setup:e=>(Zn.dispatch("GetSysBaseConfig"),ne((()=>{if("shortcut"==plus.runtime.launcher){const e=JSON.parse(plus.runtime.arguments);e&&e.type,plus.runtime.arguments=null}})),()=>{})};["navigateTo","redirectTo","reLaunch","switchTab"].forEach((e=>{uni.addInterceptor(e,{invoke:e=>function(e){if(Yn())return!(!dt.NO_TOKEN_WHITE_LIST.includes(e)&&!dt.HAS_TOKEN_WHITE_LIST.includes(e))||(!!uni.$xeu.findTree(Zn.getters.userMobileMenus,(t=>{if("MENU"===t.category&&"MENU"===t.menuType){const n=t.path;if("NO"===t.regType)return e===n;if("YES"===t.regType)return new RegExp("^"+n.substr(0,n.lastIndexOf("/")+1)).test(e)}}))||(uni.$snowy.modal.alert("页面【"+e+"】需要进行授权，才能进行访问！"),uni.reLaunch({url:dt.HAS_TOKEN_BACK_URL}),!1));return!!dt.NO_TOKEN_WHITE_LIST.includes(e)||(uni.$snowy.modal.alert("页面【"+e+"】需要进行登录，才能进行访问！"),uni.reLaunch({url:dt.NO_TOKEN_BACK_URL}),!1)}(e.url.split("?")[0]),fail(e){Q("log","at interceptor.js:10",e)}})}));const{toString:Kc}=Object.prototype;function Yc(e){return"[object Array]"===Kc.call(e)}function Gc(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),Yc(e))for(let n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.call(null,e[n],n,e)}function Jc(){const e={};function t(t,n){"object"==typeof e[n]&&"object"==typeof t?e[n]=Jc(e[n],t):e[n]="object"==typeof t?Jc({},t):t}for(let n=0,o=arguments.length;n<o;n++)Gc(arguments[n],t);return e}function Zc(e){return void 0===e}function Xc(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Qc(e,t){if(!t)return e;let n;if(o=t,"undefined"!=typeof URLSearchParams&&o instanceof URLSearchParams)n=t.toString();else{const e=[];Gc(t,((t,n)=>{null!=t&&(Yc(t)?n=`${n}[]`:t=[t],Gc(t,(t=>{!function(e){return"[object Date]"===Kc.call(e)}(t)?function(e){return null!==e&&"object"==typeof e}(t)&&(t=JSON.stringify(t)):t=t.toISOString(),e.push(`${Xc(n)}=${Xc(t)}`)})))})),n=e.join("&")}var o;if(n){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}const eu=(e,t)=>{const n={};return e.forEach((e=>{Zc(t[e])||(n[e]=t[e])})),n},tu=e=>(e=>new Promise(((t,n)=>{const o=Qc((r=e.baseURL,i=e.url,r&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)?function(e,t){return t?`${e.replace(/\/+$/,"")}/${t.replace(/^\/+/,"")}`:e}(r,i):i),e.params);var r,i;const a={url:o,header:e.header,complete:r=>{e.fullPath=o,r.config=e;try{"string"==typeof r.data&&(r.data=JSON.parse(r.data))}catch(Er){}!function(e,t,n){const{validateStatus:o}=n.config,r=n.statusCode;!r||o&&!o(r)?t(n):e(n)}(t,n,r)}};let s;if("UPLOAD"===e.method){delete a.header["content-type"],delete a.header["Content-Type"];const t={filePath:e.filePath,name:e.name},n=["files","timeout","formData"];s=uni.uploadFile({...a,...t,...eu(n,e)})}else if("DOWNLOAD"===e.method)Zc(e.timeout)||(a.timeout=e.timeout),s=uni.downloadFile(a);else{const t=["data","method","timeout","dataType","responseType","sslVerify","firstIpv4"];s=uni.request({...a,...eu(t,e)})}e.getTask&&e.getTask(s,e)})))(e);function nu(){this.handlers=[]}nu.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},nu.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},nu.prototype.forEach=function(e){this.handlers.forEach((t=>{null!==t&&e(t)}))};const ou=(e,t,n)=>{const o={};return e.forEach((e=>{Zc(n[e])?Zc(t[e])||(o[e]=t[e]):o[e]=n[e]})),o},ru={baseURL:"",header:{},method:"GET",dataType:"json",responseType:"text",custom:{},timeout:6e4,sslVerify:!0,firstIpv4:!1,validateStatus:function(e){return e>=200&&e<300}};var iu=function(){function e(e,t){return null!=t&&e instanceof t}var t,n,o;try{t=Map}catch(s){t=function(){}}try{n=Set}catch(s){n=function(){}}try{o=Promise}catch(s){o=function(){}}function r(i,s,l,c,u){"object"==typeof s&&(l=s.depth,c=s.prototype,u=s.includeNonEnumerable,s=s.circular);var d=[],h=[],p="undefined"!=typeof Buffer;return void 0===s&&(s=!0),void 0===l&&(l=1/0),function i(l,f){if(null===l)return null;if(0===f)return l;var m,g;if("object"!=typeof l)return l;if(e(l,t))m=new t;else if(e(l,n))m=new n;else if(e(l,o))m=new o((function(e,t){l.then((function(t){e(i(t,f-1))}),(function(e){t(i(e,f-1))}))}));else if(r.__isArray(l))m=[];else if(r.__isRegExp(l))m=new RegExp(l.source,a(l)),l.lastIndex&&(m.lastIndex=l.lastIndex);else if(r.__isDate(l))m=new Date(l.getTime());else{if(p&&Buffer.isBuffer(l))return Buffer.from?m=Buffer.from(l):(m=new Buffer(l.length),l.copy(m)),m;e(l,Error)?m=Object.create(l):void 0===c?(g=Object.getPrototypeOf(l),m=Object.create(g)):(m=Object.create(c),g=c)}if(s){var y=d.indexOf(l);if(-1!=y)return h[y];d.push(l),h.push(m)}for(var v in e(l,t)&&l.forEach((function(e,t){var n=i(t,f-1),o=i(e,f-1);m.set(n,o)})),e(l,n)&&l.forEach((function(e){var t=i(e,f-1);m.add(t)})),l){Object.getOwnPropertyDescriptor(l,v)&&(m[v]=i(l[v],f-1));try{if("undefined"===Object.getOwnPropertyDescriptor(l,v).set)continue;m[v]=i(l[v],f-1)}catch(Er){if(Er instanceof TypeError)continue;if(Er instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(l);for(v=0;v<b.length;v++){var w=b[v];(!(x=Object.getOwnPropertyDescriptor(l,w))||x.enumerable||u)&&(m[w]=i(l[w],f-1),Object.defineProperty(m,w,x))}}if(u){var k=Object.getOwnPropertyNames(l);for(v=0;v<k.length;v++){var x,S=k[v];(x=Object.getOwnPropertyDescriptor(l,S))&&x.enumerable||(m[S]=i(l[S],f-1),Object.defineProperty(m,S,x))}}return m}(i,l)}function i(e){return Object.prototype.toString.call(e)}function a(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=i,r.__isDate=function(e){return"object"==typeof e&&"[object Date]"===i(e)},r.__isArray=function(e){return"object"==typeof e&&"[object Array]"===i(e)},r.__isRegExp=function(e){return"object"==typeof e&&"[object RegExp]"===i(e)},r.__getRegExpFlags=a,r}();const au=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1,events:{}},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,t){e=e&&this.addRootPath(e);let n="";return/.*\/.*\?.*=.*/.test(e)?(n=_e(t,!1),e+`&${n}`):(n=_e(t),e+n)}async route(e={},t={}){let n={};if("string"==typeof e?(n.url=this.mixinParam(e,t),n.type="navigateTo"):(n=xe(this.config,e),n.url=this.mixinParam(e.url,e.params)),n.url!==Ne())if(t.intercept&&(n.intercept=t.intercept),n.params=t,n=xe(this.config,n),"function"==typeof n.intercept){await new Promise(((e,t)=>{n.intercept(n,e)}))&&this.openPage(n)}else this.openPage(n)}openPage(e){const{url:t,type:n,delta:o,animationType:r,animationDuration:i,events:a}=e;"navigateTo"!=e.type&&"to"!=e.type||uni.navigateTo({url:t,animationType:r,animationDuration:i,events:a}),"redirectTo"!=e.type&&"redirect"!=e.type||uni.redirectTo({url:t}),"switchTab"!=e.type&&"tab"!=e.type||uni.switchTab({url:t}),"reLaunch"!=e.type&&"launch"!=e.type||uni.reLaunch({url:t}),"navigateBack"!=e.type&&"back"!=e.type||uni.navigateBack({delta:o})}}).route;let su,lu=null;const cu="1.1.14";let uu="none";uu="vue3",uu="plus";const du={route:au,config:{v:cu,version:cu,type:["primary","success","info","error","warning"],color:{"uv-primary":"#2979ff","uv-warning":"#ff9900","uv-success":"#19be6b","uv-error":"#fa3534","uv-info":"#909399","uv-main-color":"#303133","uv-content-color":"#606266","uv-tips-color":"#909399","uv-light-color":"#c0c4cc"},unit:"px"},test:he,date:Se,...De,colorGradient:so,hexToRgb:lo,rgbToHex:co,colorToRgba:function(e,t){e=co(e);let n=String(e).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let e="#";for(let t=1;t<4;t+=1)e+=n.slice(t,t+1).concat(n.slice(t,t+1));n=e}const e=[];for(let t=1;t<7;t+=2)e.push(parseInt(`0x${n.slice(t,t+2)}`));return`rgba(${e.join(",")},${t})`}return n},http:new class{constructor(e={}){var t;t=e,"[object Object]"!==Object.prototype.toString.call(t)&&(e={},Q("warn","at uni_modules/uv-ui-tools/libs/luch-request/core/Request.js:39","设置全局参数必须接收一个Object")),this.config=iu({...ru,...e}),this.interceptors={request:new nu,response:new nu}}setConfig(e){this.config=e(this.config)}middleware(e){e=((e,t={})=>{const n=t.method||e.method||"GET";let o={baseURL:e.baseURL||"",method:n,url:t.url||"",params:t.params||{},custom:{...e.custom||{},...t.custom||{}},header:Jc(e.header||{},t.header||{})};if(o={...o,...ou(["getTask","validateStatus"],e,t)},"DOWNLOAD"===n)Zc(t.timeout)?Zc(e.timeout)||(o.timeout=e.timeout):o.timeout=t.timeout;else if("UPLOAD"===n)delete o.header["content-type"],delete o.header["Content-Type"],["files","filePath","name","timeout","formData"].forEach((e=>{Zc(t[e])||(o[e]=t[e])})),Zc(o.timeout)&&!Zc(e.timeout)&&(o.timeout=e.timeout);else{const n=["data","timeout","dataType","responseType","sslVerify","firstIpv4"];o={...o,...ou(n,e,t)}}return o})(this.config,e);const t=[tu,void 0];let n=Promise.resolve(e);for(this.interceptors.request.forEach((e=>{t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((e=>{t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n}request(e={}){return this.middleware(e)}get(e,t={}){return this.middleware({url:e,method:"GET",...t})}post(e,t,n={}){return this.middleware({url:e,data:t,method:"POST",...n})}put(e,t,n={}){return this.middleware({url:e,data:t,method:"PUT",...n})}delete(e,t,n={}){return this.middleware({url:e,data:t,method:"DELETE",...n})}options(e,t,n={}){return this.middleware({url:e,data:t,method:"OPTIONS",...n})}upload(e,t={}){return t.url=e,t.method="UPLOAD",this.middleware(t)}download(e,t={}){return t.url=e,t.method="DOWNLOAD",this.middleware(t)}},debounce:function(e,t=500,n=!1){if(null!==lu&&clearTimeout(lu),n){const n=!lu;lu=setTimeout((()=>{lu=null}),t),n&&"function"==typeof e&&e()}else lu=setTimeout((()=>{"function"==typeof e&&e()}),t)},throttle:function(e,t=500,n=!0){n?su||(su=!0,"function"==typeof e&&e(),setTimeout((()=>{su=!1}),t)):su||(su=!0,setTimeout((()=>{su=!1,"function"==typeof e&&e()}),t))},platform:"plus",mixin:Ee,mpMixin:X};uni.$uv=du;const hu={install:(e,t={})=>{var n,o;const r=ke(Ee);null==(n=null==r?void 0:r.props)||delete n.customClass,null==(o=null==r?void 0:r.props)||delete o.customStyle,e.mixin(r),e.config.globalProperties.$uv=du}};var pu={cookies:{path:"/"},treeOptions:{parentKey:"parentId",key:"id",children:"children"},parseDateFormat:"yyyy-MM-dd HH:mm:ss",firstDayOfWeek:1};function fu(e,t,n){if(e)if(e.forEach)e.forEach(t,n);else for(var o=0,r=e.length;o<r;o++)t.call(n,e[o],o,e)}var mu=Object.prototype.toString;function gu(e){return function(t){return"[object "+e+"]"===mu.call(t)}}var yu=Array.isArray||gu("Array");function vu(e,t){return!(!e||!e.hasOwnProperty)&&e.hasOwnProperty(t)}function bu(e,t,n){if(e)for(var o in e)vu(e,o)&&t.call(n,e[o],o,e)}function wu(e,t,n){return e?(yu(e)?fu:bu)(e,t,n):e}function ku(e){return function(t){return typeof t===e}}var xu=ku("function");function Su(e,t){var n=Object[e];return function(e){var o=[];if(e){if(n)return n(e);wu(e,t>1?function(t){o.push([""+t,e[t]])}:function(){o.push(arguments[t])})}return o}}var Cu=Su("keys",1);function _u(e,t){var n=e.__proto__.constructor;return t?new n(t):new n}function Nu(e,t){return t?Du(e,t):e}function Du(e,t){if(e)switch(mu.call(e)){case"[object Object]":var n=Object.create(e.__proto__);return bu(e,(function(e,o){n[o]=Nu(e,t)})),n;case"[object Date]":case"[object RegExp]":return _u(e,e.valueOf());case"[object Array]":case"[object Arguments]":var o=[];return fu(e,(function(e){o.push(Nu(e,t))})),o;case"[object Set]":var r=_u(e);return r.forEach((function(e){r.add(Nu(e,t))})),r;case"[object Map]":var i=_u(e);return i.forEach((function(e,n){i.set(n,Nu(e,t))})),i}return e}function Eu(e,t){return e?Du(e,t):e}var Tu=Object.assign;function Bu(e,t,n){for(var o,r=t.length,i=1;i<r;i++)o=t[i],fu(Cu(t[i]),n?function(t){e[t]=Eu(o[t],n)}:function(t){e[t]=o[t]});return e}var Vu=function(e){if(e){var t=arguments;if(!0!==e)return Tu?Tu.apply(Object,t):Bu(e,t);if(t.length>1)return Bu(e=yu(e[1])?[]:{},t,!0)}return e},Iu=function(){};function Au(e,t,n){for(var o=e.length-1;o>=0;o--)t.call(n,e[o],o,e)}function Pu(e,t,n){Au(Cu(e),(function(o){t.call(n,e[o],o,e)}))}function $u(e){return null===e}function Ou(e,t){return function(n){return $u(n)?t:n[e]}}function Fu(e){return!!e&&e.constructor===Object}function Mu(e,t){return Fu(e)&&Fu(t)||yu(e)&&yu(t)?(wu(t,(function(t,n){e[n]=Mu(e[n],t)})),e):t}Iu.VERSION="3.5.12",Iu.mixin=function(){fu(arguments,(function(e){wu(e,(function(e,t){Iu[t]=xu(e)?function(){var t=e.apply(Iu.$context,arguments);return Iu.$context=null,t}:e}))}))},Iu.setup=function(e){return Vu(pu,e)};function Lu(e,t,n){var o=[];if(e&&arguments.length>1){if(e.map)return e.map(t,n);wu(e,(function(){o.push(t.apply(n,arguments))}))}return o}function zu(e,t,n,o,r){return function(i,a,s){if(i&&a){if(e&&i[e])return i[e](a,s);if(t&&yu(i)){for(var l=0,c=i.length;l<c;l++)if(!!a.call(s,i[l],l,i)===o)return[!0,!1,l,i[l]][n]}else for(var u in i)if(vu(i,u)&&!!a.call(s,i[u],u,i)===o)return[!0,!1,u,i[u]][n]}return r}}var Ru=zu("some",1,0,!0,!1),Uu=zu("every",1,1,!1,!0);function qu(e,t){if(e){if(e.includes)return e.includes(t);for(var n in e)if(vu(e,n)&&t===e[n])return!0}return!1}function ju(e,t){var n,o=0;if(yu(e)&&yu(t)){for(n=t.length;o<n;o++)if(!qu(e,t[o]))return!1;return!0}return qu(e,t)}function Hu(e,t,n){var o=[];if(t){xu(t)||(t=Ou(t));var r,i={};wu(e,(function(a,s){r=t.call(n,a,s,e),i[r]||(i[r]=1,o.push(a))}))}else wu(e,(function(e){qu(o,e)||o.push(e)}));return o}function Wu(e){return Lu(e,(function(e){return e}))}var Ku="undefined",Yu=ku(Ku);function Gu(e){return $u(e)||Yu(e)}var Ju=/(.+)?\[(\d+)\]$/;function Zu(e){return e?e.splice&&e.join?e:(""+e).replace(/(\[\d+\])\.?/g,"$1.").replace(/\.$/,"").split("."):[]}function Xu(e,t,n){if(Gu(e))return n;var o=function(e,t){if(e){var n,o,r,i=0;if(e[t]||vu(e,t))return e[t];if(r=(o=Zu(t)).length)for(n=e;i<r;i++)if(Gu(n=Qu(n,o[i])))return i===r-1?n:void 0;return n}}(e,t);return Yu(o)?n:o}function Qu(e,t){var n=t?t.match(Ju):"";return n?n[1]?e[n[1]]?e[n[1]][n[2]]:void 0:e[n[2]]:e[t]}function ed(e,t){return Yu(e)?1:$u(e)?Yu(t)?-1:1:e&&e.localeCompare?e.localeCompare(t):e>t?1:-1}function td(e,t,n){return function(o,r){var i=o[e],a=r[e];return i===a?n?n(o,r):0:"desc"===t.order?ed(a,i):ed(i,a)}}function nd(e,t,n){if(e){if(Gu(t))return Wu(e).sort(ed);for(var o,r=Lu(e,(function(e){return{data:e}})),i=function(e,t,n,o){var r=[];return fu(n=yu(n)?n:[n],(function(n,i){if(n){var a,s=n;yu(n)?(s=n[0],a=n[1]):Fu(n)&&(s=n.field,a=n.order),r.push({field:s,order:a||"asc"}),fu(t,xu(s)?function(t,n){t[i]=s.call(o,t.data,n,e)}:function(e){e[i]=s?Xu(e.data,s):e.data})}})),r}(e,r,t,n),a=i.length-1;a>=0;)o=td(a,i[a],o),a--;return o&&(r=r.sort(o)),Lu(r,Ou("data"))}return[]}var od=nd;function rd(e,t){return e>=t?e:(e>>=0)+Math.round(Math.random()*((t||9)-e))}var id=Su("values",0);function ad(e){for(var t,n=[],o=id(e),r=o.length-1;r>=0;r--)t=r>0?rd(0,r):0,n.push(o[t]),o.splice(t,1);return n}function sd(e){return function(t){if(t){var n=e(t);if(!isNaN(n))return n}return 0}}var ld=sd(parseFloat);function cd(e,t,n){var o=[],r=arguments.length;if(e){if(t=r>=2?ld(t):0,n=r>=3?ld(n):e.length,e.slice)return e.slice(t,n);for(;t<n;t++)o.push(e[t])}return o}var ud=zu("",0,2,!0),dd=zu("find",1,3,!0);function hd(e,t){return Lu(e,Ou(t))}function pd(e){return function(t,n){var o,r;return t&&t.length?(fu(t,(function(i,a){n&&(i=xu(n)?n(i,a,t):Xu(i,n)),Gu(i)||!Gu(o)&&!e(o,i)||(r=a,o=i)})),t[r]):o}}var fd=pd((function(e,t){return e<t}));function md(e){var t,n,o,r=[];if(e&&e.length)for(t=0,o=(n=fd(e,(function(e){return e?e.length:0})))?n.length:0;t<o;t++)r.push(hd(e,t));return r}function gd(e,t){var n=[];return fu(e,(function(e){n=n.concat(yu(e)?t?gd(e,t):e:[e])})),n}function yd(e,t){for(var n=0,o=t.length;e&&n<o;)e=e[t[n++]];return o&&e?e:0}function vd(e,t){try{delete e[t]}catch(Er){e[t]=void 0}}function bd(e,t,n){return e?(yu(e)?Au:Pu)(e,t,n):e}var wd=ku("object");function kd(e,t,n){if(e){var o,r=arguments.length>1&&($u(t)||!wd(t)),i=r?n:t;if(Fu(e))bu(e,r?function(n,o){e[o]=t}:function(t,n){vd(e,n)}),i&&Vu(e,i);else if(yu(e)){if(r)for(o=e.length;o>0;)o--,e[o]=t;else e.length=0;i&&e.push.apply(e,i)}}return e}function xd(e,t,n){if(e){if(!Gu(t)){var o=[],r=[];return xu(t)||(i=t,t=function(e,t){return t===i}),wu(e,(function(e,r,i){t.call(n,e,r,i)&&o.push(r)})),yu(e)?bd(o,(function(t,n){r.push(e[t]),e.splice(t,1)})):(r={},fu(o,(function(t){r[t]=e[t],vd(e,t)}))),r}return kd(e)}var i;return e}function Sd(e,t,n){var o=n.children,r=n.data,i=n.clear;return wu(t,(function(t){var a=t[o];r&&(t=t[r]),e.push(t),a&&a.length&&Sd(e,a,n),i&&delete t[o]})),e}function Cd(e){return function(t,n,o,r){var i=o||{},a=i.children||"children";return e(null,t,n,r,[],[],a,i)}}var _d=Cd((function e(t,n,o,r,i,a,s,l){var c,u,d,h,p,f;if(n)for(u=0,d=n.length;u<d;u++){if(c=n[u],h=i.concat([""+u]),p=a.concat([c]),o.call(r,c,u,n,h,t,p))return{index:u,item:c,path:h,items:n,parent:t,nodes:p};if(s&&c&&(f=e(c,c[s],o,r,h.concat([s]),p,s)))return f}}));var Nd=Cd((function e(t,n,o,r,i,a,s,l){var c,u;wu(n,(function(l,d){c=i.concat([""+d]),u=a.concat([l]),o.call(r,l,d,n,c,t,u),l&&s&&(c.push(s),e(l,l[s],o,r,c,u,s))}))}));var Dd=Cd((function e(t,n,o,r,i,a,s,l){var c,u,d,h=l.mapChildren||s;return Lu(n,(function(p,f){return c=i.concat([""+f]),u=a.concat([p]),(d=o.call(r,p,f,n,c,t,u))&&p&&s&&p[s]&&(d[h]=e(p,p[s],o,r,c,u,s,l)),d}))}));function Ed(e,t,n,o,r,i,a,s,l){var c,u,d,h,p,f=[],m=l.original,g=l.data,y=l.mapChildren||s;return fu(n,(function(v,b){c=i.concat([""+b]),u=a.concat([v]),h=e||o.call(r,v,b,n,c,t,u),p=s&&v[s],h||p?(m?d=v:(d=Vu({},v),g&&(d[g]=v)),d[y]=Ed(h,v,v[s],o,r,c,u,s,l),(h||d[y].length)&&f.push(d)):h&&f.push(d)})),f}var Td=Cd((function(e,t,n,o,r,i,a,s){return Ed(0,e,t,n,o,r,i,a,s)}));function Bd(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,o=e.length;n<o;n++)if(t===e[n])return n}function Vd(e,t){if(e.lastIndexOf)return e.lastIndexOf(t);for(var n=e.length-1;n>=0;n--)if(t===e[n])return n;return-1}var Id=ku("number");var Ad=ku("string"),Pd=gu("Date"),$d=parseInt;function Od(e){return Date.UTC(e.y,e.M||0,e.d||1,e.H||0,e.m||0,e.s||0,e.S||0)}function Fd(e){return e.getTime()}function Md(e){return"(\\d{"+e+"})"}function Ld(e){return isNaN(e)?e:$d(e)}for(var zd=Md(2),Rd=Md("1,2"),Ud=Md("1,7"),qd=Md("3,4"),jd=".{1}",Hd=jd+Rd,Wd="(([zZ])|([-+]\\d{2}:?\\d{2}))",Kd=[qd,Hd,Hd,Hd,Hd,Hd,jd+Ud,Wd],Yd=[],Gd=Kd.length-1;Gd>=0;Gd--){for(var Jd="",Zd=0;Zd<Gd+1;Zd++)Jd+=Kd[Zd];Yd.push(new RegExp("^"+Jd+"$"))}var Xd=[["yyyy",qd],["yy",zd],["MM",zd],["M",Rd],["dd",zd],["d",Rd],["HH",zd],["H",Rd],["mm",zd],["m",Rd],["ss",zd],["s",Rd],["SSS",Md(3)],["S",Ud],["Z",Wd]],Qd={},eh=["\\[([^\\]]+)\\]"];for(Zd=0;Zd<Xd.length;Zd++){var th=Xd[Zd];Qd[th[0]]=th[1]+"?",eh.push(th[0])}var nh=new RegExp(eh.join("|"),"g"),oh={};function rh(e,t){if(e){var n=Pd(e);if(n||!t&&/^[0-9]{11,15}$/.test(e))return new Date(n?Fd(e):$d(e));if(Ad(e)){var o=t?function(e,t){var n=oh[t];if(!n){var o=[],r=t.replace(/([$(){}*+.?\\^|])/g,"\\$1").replace(nh,(function(e,t){var n=e.charAt(0);return"["===n?t:(o.push(n),Qd[e])}));n=oh[t]={_i:o,_r:new RegExp(r)}}var i={},a=e.match(n._r);if(a){for(var s=n._i,l=1,c=a.length;l<c;l++)i[s[l-1]]=a[l];return i}return i}(e,t):function(e){for(var t,n={},o=0,r=Yd.length;o<r;o++)if(t=e.match(Yd[o])){n.y=t[1],n.M=t[2],n.d=t[3],n.H=t[4],n.m=t[5],n.s=t[6],n.S=t[7],n.Z=t[8];break}return n}(e);if(o.y)return o.M&&(o.M=Ld(o.M)-1),o.S&&(o.S=(r=Ld(o.S.substring(0,3)))<10?100*r:r<100?10*r:r),o.Z?function(e){if(/^[zZ]/.test(e.Z))return new Date(Od(e));var t=e.Z.match(/([-+])(\d{2}):?(\d{2})/);return t?new Date(Od(e)-("-"===t[1]?-1:1)*$d(t[2])*36e5+6e4*$d(t[3])):new Date("")}(o):new Date(o.y,o.M||0,o.d||1,o.H||0,o.m||0,o.s||0,o.S||0)}}var r;return new Date("")}function ih(){return new Date}function ah(e){var t,n=e?rh(e):ih();return!!Pd(n)&&((t=n.getFullYear())%4==0&&(t%100!=0||t%400==0))}function sh(e,t){return function(n,o){if(n){if(n[e])return n[e](o);if(Ad(n)||yu(n))return t(n,o);for(var r in n)if(vu(n,r)&&o===n[r])return r}return-1}}var lh=sh("indexOf",Bd),ch=sh("lastIndexOf",Vd);function uh(e){var t=0;return Ad(e)||yu(e)?e.length:(wu(e,(function(){t++})),t)}var dh=function(e){return!$u(e)&&!isNaN(e)&&!yu(e)&&e%1==0};var hh=ku("boolean"),ph=gu("RegExp"),fh=gu("Error");function mh(e){for(var t in e)return!1;return!0}var gh=typeof Symbol!==Ku;function yh(e){return gh&&Symbol.isSymbol?Symbol.isSymbol(e):"symbol"==typeof e}var vh=gu("Arguments");var bh=typeof document===Ku?0:document;var wh=typeof window===Ku?0:window;var kh=typeof FormData!==Ku;var xh=typeof Map!==Ku;var Sh=typeof WeakMap!==Ku;var Ch=typeof Set!==Ku;var _h=typeof WeakSet!==Ku;function Nh(e){return function(t,n,o){if(t&&xu(n)){if(yu(t)||Ad(t))return e(t,n,o);for(var r in t)if(vu(t,r)&&n.call(o,t[r],r,t))return r}return-1}}var Dh=Nh((function(e,t,n){for(var o=0,r=e.length;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1}));function Eh(e,t,n,o,r,i,a){if(e===t)return!0;if(e&&t&&!Id(e)&&!Id(t)&&!Ad(e)&&!Ad(t)){if(ph(e))return n(""+e,""+t,r,i,a);if(Pd(e)||hh(e))return n(+e,+t,r,i,a);var s,l,c,u=yu(e),d=yu(t);if(u||d?u&&d:e.constructor===t.constructor)return l=Cu(e),c=Cu(t),o&&(s=o(e,t,r)),l.length===c.length&&(Yu(s)?Uu(l,(function(r,i){return r===c[i]&&Eh(e[r],t[c[i]],n,o,u||d?i:r,e,t)})):!!s)}return n(e,t,r,i,a)}function Th(e,t){return e===t}function Bh(e,t){return Eh(e,t,Th)}var Vh=0;var Ih=Nh((function(e,t,n){for(var o=e.length-1;o>=0;o--)if(t.call(n,e[o],o,e))return o;return-1}));var Ah=Su("entries",2);function Ph(e,t){return function(n,o){var r,i,a={},s=[],l=this,c=arguments,u=c.length;if(!xu(o)){for(i=1;i<u;i++)r=c[i],s.push.apply(s,yu(r)?r:[r]);o=0}return wu(n,(function(r,i){((o?o.call(l,r,i,n):Dh(s,(function(e){return e===i}))>-1)?e:t)&&(a[i]=r)})),a}}var $h=Ph(1,0),Oh=Ph(0,1);var Fh=/(.+)?\[(\d+)\]$/;function Mh(e,t,n,o,r){if(!e[t]){var i,a,s=t?t.match(Fh):null;if(n)a=r;else{var l=o?o.match(Fh):null;a=l&&!l[1]?new Array($d(l[2])+1):{}}return s?s[1]?(i=$d(s[2]),e[s[1]]?n?e[s[1]][i]=a:e[s[1]][i]?a=e[s[1]][i]:e[s[1]][i]=a:(e[s[1]]=new Array(i+1),e[s[1]][i]=a)):e[s[2]]=a:e[t]=a,a}return n&&(e[t]=r),e[t]}function Lh(e){return"__proto__"===e||"constructor"===e||"prototype"===e}function zh(e,t,n){var o,r={};return e&&(t&&wd(t)?t=function(e){return function(){return mh(e)}}(t):xu(t)||(t=Ou(t)),wu(e,(function(i,a){o=t?t.call(n,i,a,e):i,r[o]?r[o].push(i):r[o]=[i]}))),r}var Rh=pd((function(e,t){return e>t}));function Uh(e){return(e.split(".")[1]||"").length}function qh(e,t){if(e.repeat)return e.repeat(t);var n=isNaN(t)?[]:new Array($d(t));return n.join(e)+(n.length>0?e:"")}function jh(e,t){return e.substring(0,t)+"."+e.substring(t,e.length)}function Hh(e){var t=""+e,n=t.match(/^([-+]?)((\d+)|((\d+)?[.](\d+)?))e([-+]{1})([0-9]+)$/);if(n){var o=e<0?"-":"",r=n[3]||"",i=n[5]||"",a=n[6]||"",s=n[7],l=n[8],c=l-a.length,u=l-r.length,d=l-i.length;return"+"===s?r?o+r+qh("0",l):c>0?o+i+a+qh("0",c):o+i+jh(a,l):r?u>0?o+"0."+qh("0",Math.abs(u))+r:o+jh(r,u):d>0?o+"0."+qh("0",Math.abs(d))+i+a:o+jh(i,d)+a}return t}function Wh(e,t){var n=Hh(e),o=Hh(t);return parseInt(n.replace(".",""))*parseInt(o.replace(".",""))/Math.pow(10,Uh(n)+Uh(o))}function Kh(e){return function(t,n){var o=ld(t),r=o;if(o){n>>=0;var i=Hh(o).split("."),a=i[0],s=i[1]||"",l=s.substring(0,n+1),c=a+(l?"."+l:"");if(n>=s.length)return ld(c);if(c=o,n>0){var u=Math.pow(10,n);r=Math[e](Wh(c,u))/u}else r=Math[e](c)}return r}}var Yh=Kh("round"),Gh=Kh("ceil"),Jh=Kh("floor");function Zh(e){return Id(e)?Hh(e):""+(Gu(e)?"":e)}function Xh(e,t){var n=Zh(Yh(e,t>>=0)).split("."),o=n[0],r=n[1]||"",i=t-r.length;return t?i>0?o+"."+r+qh("0",i):o+jh(r,Math.abs(i)):o}var Qh=sd($d);function ep(e,t){return Wh(ld(e),ld(t))}function tp(e,t){var n=Hh(e),o=Hh(t),r=Math.pow(10,Math.max(Uh(n),Uh(o)));return(ep(e,r)+ep(t,r))/r}function np(e,t){var n=Hh(e),o=Hh(t),r=Uh(n),i=Uh(o)-r,a=i<0,s=Math.pow(10,a?Math.abs(i):i);return ep(n.replace(".","")/o.replace(".",""),a?1/s:s)}function op(e,t,n){var o=0;return wu(e,t?xu(t)?function(){o=tp(o,t.apply(n,arguments))}:function(e){o=tp(o,Xu(e,t))}:function(e){o=tp(o,e)}),o}var rp="first",ip="last";function ap(e){return e.getFullYear()}var sp=864e5;function lp(e){return e.getMonth()}function cp(e){return Pd(e)&&!isNaN(Fd(e))}function up(e,t,n){var o=t&&!isNaN(t)?t:0;if(cp(e=rh(e))){if(n===rp)return new Date(ap(e),lp(e)+o,1);if(n===ip)return new Date(Fd(up(e,o+1,rp))-1);if(Id(n)&&e.setDate(n),o){var r=e.getDate();if(e.setMonth(lp(e)+o),r!==e.getDate())return e.setDate(1),new Date(Fd(e)-sp)}}return e}function dp(e,t,n){var o;if(cp(e=rh(e))&&(t&&(o=t&&!isNaN(t)?t:0,e.setFullYear(ap(e)+o)),n||!isNaN(n))){if(n===rp)return new Date(ap(e),0,1);if(n===ip)return e.setMonth(11),up(e,0,ip);e.setMonth(n)}return e}function hp(e,t,n){if(cp(e=rh(e))&&!isNaN(t)){if(e.setDate(e.getDate()+$d(t)),n===rp)return new Date(ap(e),lp(e),e.getDate());if(n===ip)return new Date(Fd(hp(e,1,rp))-1)}return e}function pp(e){return e.toUpperCase()}var fp=6048e5;function mp(e,t,n,o){if(cp(e=rh(e))){var r=Id(n),i=Id(o),a=Fd(e);if(r||i){var s=i?o:pu.firstDayOfWeek,l=e.getDay(),c=r?n:l;if(l!==c){var u=0;s>l?u=-(7-s+l):s<l&&(u=s-l),a+=c>s?((0===c?7:c)-s+u)*sp:c<s?(7-s+c+u)*sp:u*sp}}return t&&!isNaN(t)&&(a+=t*fp),new Date(a)}return e}function gp(e){return function(t,n){var o=Id(n)?n:pu.firstDayOfWeek,r=mp(t,0,o,o);if(cp(r)){var i=new Date(r.getFullYear(),r.getMonth(),r.getDate()),a=e(r),s=a.getDay();return s>o&&a.setDate(7-s+o+1),s<o&&a.setDate(o-s+1),Math.floor((Fd(i)-Fd(a))/fp+1)}return NaN}}var yp=gp((function(e){return new Date(e.getFullYear(),0,1)}));function vp(e){return Fd(function(e){return new Date(ap(e),lp(e),e.getDate())}(e))}function bp(e){return cp(e=rh(e))?Math.floor((vp(e)-vp(dp(e,0,rp)))/sp)+1:NaN}function wp(e,t,n){var o=Zh(e);return t>>=0,n=Yu(n)?" ":""+n,o.padStart?o.padStart(t,n):t>o.length?((t-=o.length)>n.length&&(n+=qh(n,t/n.length)),n.slice(0,t)+o):o}function kp(e,t,n,o){var r=t[n];return r?xu(r)?r(o,n,e):r[o]:o}var xp=/\[([^\]]+)]|y{2,4}|M{1,2}|d{1,2}|H{1,2}|h{1,2}|m{1,2}|s{1,2}|S{1,3}|Z{1,2}|W{1,2}|D{1,3}|[aAeEq]/g;function Sp(e,t,n){if(e){if(cp(e=rh(e))){var o=t||pu.parseDateFormat||pu.formatString,r=e.getHours(),i=r<12?"am":"pm",a=Vu({},pu.parseDateRules||pu.formatStringMatchs,n?n.formats:null),s=function(t,n){return(""+ap(e)).substr(4-n)},l=function(t,n){return wp(lp(e)+1,n,"0")},c=function(t,n){return wp(e.getDate(),n,"0")},u=function(e,t){return wp(r,t,"0")},d=function(e,t){return wp(r<=12?r:r-12,t,"0")},h=function(t,n){return wp(e.getMinutes(),n,"0")},p=function(t,n){return wp(e.getSeconds(),n,"0")},f=function(t,n){return wp(e.getMilliseconds(),n,"0")},m=function(t,n){var o=e.getTimezoneOffset()/60*-1;return kp(e,a,t,(o>=0?"+":"-")+wp(o,2,"0")+(1===n?":":"")+"00")},g=function(t,o){return wp(kp(e,a,t,yp(e,(n?n.firstDay:null)||pu.firstDayOfWeek)),o,"0")},y=function(t,n){return wp(kp(e,a,t,bp(e)),n,"0")},v={yyyy:s,yy:s,MM:l,M:l,dd:c,d:c,HH:u,H:u,hh:d,h:d,mm:h,m:h,ss:p,s:p,SSS:f,S:f,ZZ:m,Z:m,WW:g,W:g,DDD:y,D:y,a:function(t){return kp(e,a,t,i)},A:function(t){return kp(e,a,t,pp(i))},e:function(t){return kp(e,a,t,e.getDay())},E:function(t){return kp(e,a,t,e.getDay())},q:function(t){return kp(e,a,t,Math.floor((lp(e)+3)/3))}};return o.replace(xp,(function(e,t){return t||(v[e]?v[e](e,e.length):e)}))}return"Invalid Date"}return""}var Cp=Date.now||function(){return Fd(ih())};var _p=gp((function(e){return new Date(e.getFullYear(),e.getMonth(),1)}));var Np=[["yyyy",31536e6],["MM",2592e6],["dd",864e5],["HH",36e5],["mm",6e4],["ss",1e3],["S",0]];function Dp(e){return e&&e.trimRight?e.trimRight():Zh(e).replace(/[\s\uFEFF\xA0]+$/g,"")}function Ep(e){return e&&e.trimLeft?e.trimLeft():Zh(e).replace(/^[\s\uFEFF\xA0]+/g,"")}function Tp(e){return e&&e.trim?e.trim():Dp(Ep(e))}var Bp={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};function Vp(e){var t=new RegExp("(?:"+Cu(e).join("|")+")","g");return function(n){return Zh(n).replace(t,(function(t){return e[t]}))}}var Ip=Vp(Bp),Ap={};wu(Bp,(function(e,t){Ap[Bp[t]]=t}));var Pp=Vp(Ap);function $p(e,t,n){return e.substring(t,n)}function Op(e){return e.toLowerCase()}var Fp={};var Mp={};function Lp(e,t,n){return Zh(e).replace((n||pu).tmplRE||/\{{2}([.\w[\]\s]+)\}{2}/g,(function(e,n){return Xu(t,Tp(n))}))}var zp=decodeURIComponent;function Rp(e){var t,n={};return e&&Ad(e)&&fu(e.split("&"),(function(e){t=e.split("="),n[zp(t[0])]=zp(t[1]||"")})),n}var Up=encodeURIComponent;function qp(e,t,n){var o,r=[];return wu(e,(function(e,i){o=yu(e),Fu(e)||o?r=r.concat(qp(e,t+"["+i+"]",o)):r.push(Up(t+"["+(n?"":i)+"]")+"="+Up($u(e)?"":e))})),r}var jp=typeof location===Ku?0:location;function Hp(){return jp?jp.origin||jp.protocol+"//"+jp.host:""}function Wp(e){return Rp(e.split("?")[1]||"")}function Kp(e){var t,n,o,r,i=""+e;return 0===i.indexOf("//")?i=(jp?jp.protocol:"")+i:0===i.indexOf("/")&&(i=Hp()+i),o=i.replace(/#.*/,"").match(/(\?.*)/),(r={href:i,hash:"",host:"",hostname:"",protocol:"",port:"",search:o&&o[1]&&o[1].length>1?o[1]:""}).path=i.replace(/^([a-z0-9.+-]*:)\/\//,(function(e,t){return r.protocol=t,""})).replace(/^([a-z0-9.+-]*)(:\d+)?\/?/,(function(e,t,o){return n=o||"",r.port=n.replace(":",""),r.hostname=t,r.host=t+n,"/"})).replace(/(#.*)/,(function(e,t){return r.hash=t.length>1?t:"",""})),t=r.hash.match(/#((.*)\?|(.*))/),r.pathname=r.path.replace(/(\?|#.*).*/,""),r.origin=r.protocol+"//"+r.host,r.hashKey=t&&(t[2]||t[1])||"",r.hashQuery=Wp(r.hash),r.searchQuery=Wp(r.search),r}function Yp(e,t){var n=parseFloat(t),o=ih(),r=Fd(o);switch(e){case"y":return Fd(dp(o,n));case"M":return Fd(up(o,n));case"d":return Fd(hp(o,n));case"h":case"H":return r+60*n*60*1e3;case"m":return r+60*n*1e3;case"s":return r+1e3*n}return r}function Gp(e){return(Pd(e)?e:new Date(e)).toUTCString()}function Jp(e,t,n){if(bh){var o,r,i,a,s,l,c=[],u=arguments;return yu(e)?c=e:u.length>1?c=[Vu({name:e,value:t},n)]:wd(e)&&(c=[e]),c.length>0?(fu(c,(function(e){o=Vu({},pu.cookies,e),i=[],o.name&&(r=o.expires,i.push(Up(o.name)+"="+Up(wd(o.value)?JSON.stringify(o.value):o.value)),r&&(r=isNaN(r)?r.replace(/^([0-9]+)(y|M|d|H|h|m|s)$/,(function(e,t,n){return Gp(Yp(n,t))})):/^[0-9]{11,13}$/.test(r)||Pd(r)?Gp(r):Gp(Yp("d",r)),o.expires=r),fu(["expires","path","domain","secure"],(function(e){Yu(o[e])||i.push(o[e]&&"secure"===e?e:e+"="+o[e])}))),bh.cookie=i.join("; ")})),!0):(a={},(s=bh.cookie)&&fu(s.split("; "),(function(e){l=e.indexOf("="),a[zp(e.substring(0,l))]=zp(e.substring(l+1)||"")})),1===u.length?a[e]:a)}return!1}function Zp(e){return Jp(e)}function Xp(e,t,n){return Jp(e,t,n),Jp}function Qp(e,t){Jp(e,"",Vu({expires:-1},pu.cookies,t))}function ef(){return Cu(Jp())}function tf(e){try{var t="__xe_t";return e.setItem(t,1),e.removeItem(t),!0}catch(Er){return!1}}function nf(e){return navigator.userAgent.indexOf(e)>-1}Vu(Jp,{has:function(e){return qu(ef(),e)},set:Xp,setItem:Xp,get:Zp,getItem:Zp,remove:Qp,removeItem:Qp,keys:ef,getJSON:function(){return Jp()}}),Vu(Iu,{assign:Vu,objectEach:bu,lastObjectEach:Pu,objectMap:function(e,t,n){var o={};if(e){if(!t)return e;xu(t)||(t=Ou(t)),wu(e,(function(r,i){o[i]=t.call(n,r,i,e)}))}return o},merge:function(e){e||(e={});for(var t,n=arguments,o=n.length,r=1;r<o;r++)(t=n[r])&&Mu(e,t);return e},uniq:Hu,union:function(){for(var e=arguments,t=[],n=0,o=e.length;n<o;n++)t=t.concat(Wu(e[n]));return Hu(t)},sortBy:od,orderBy:nd,shuffle:ad,sample:function(e,t){var n=ad(e);return arguments.length<=1?n[0]:(t<n.length&&(n.length=t||0),n)},some:Ru,every:Uu,slice:cd,filter:function(e,t,n){var o=[];if(e&&t){if(e.filter)return e.filter(t,n);wu(e,(function(r,i){t.call(n,r,i,e)&&o.push(r)}))}return o},find:dd,findLast:function(e,t,n){if(e){yu(e)||(e=id(e));for(var o=e.length-1;o>=0;o--)if(t.call(n,e[o],o,e))return e[o]}},findKey:ud,includes:qu,arrayIndexOf:Bd,arrayLastIndexOf:Vd,map:Lu,reduce:function(e,t,n){if(e){var o,r,i=0,a=null,s=n,l=arguments.length>2,c=Cu(e);if(e.length&&e.reduce)return r=function(){return t.apply(a,arguments)},l?e.reduce(r,s):e.reduce(r);for(l&&(i=1,s=e[c[0]]),o=c.length;i<o;i++)s=t.call(a,s,e[c[i]],i,e);return s}},copyWithin:function(e,t,n,o){if(yu(e)&&e.copyWithin)return e.copyWithin(t,n,o);var r,i,a=t>>0,s=n>>0,l=e.length,c=arguments.length>3?o>>0:l;if(a<l&&(a=a>=0?a:l+a)>=0&&(s=s>=0?s:l+s)<(c=c>=0?c:l+c))for(r=0,i=e.slice(s,c);a<l&&!(i.length<=r);a++)e[a]=i[r++];return e},chunk:function(e,t){var n,o=[],r=t>>0||1;if(yu(e))if(r>=0&&e.length>r)for(n=0;n<e.length;)o.push(e.slice(n,n+r)),n+=r;else o=e.length?[e]:e;return o},zip:function(){return md(arguments)},unzip:md,zipObject:function(e,t){var n={};return t=t||[],wu(id(e),(function(e,o){n[e]=t[o]})),n},flatten:function(e,t){return yu(e)?gd(e,t):[]},toArray:Wu,includeArrays:ju,pluck:hd,invoke:function(e,t){for(var n,o=arguments,r=[],i=[],a=2,s=o.length;a<s;a++)r.push(o[a]);if(yu(t)){for(s=t.length-1,a=0;a<s;a++)i.push(t[a]);t=t[s]}return Lu(e,(function(e){if(i.length&&(e=yd(e,i)),(n=e[t]||t)&&n.apply)return n.apply(e,r)}))},arrayEach:fu,lastArrayEach:Au,toArrayTree:function(e,t){var n,o,r,i=Vu({},pu.treeOptions,t),a=i.strict,s=i.key,l=i.parentKey,c=i.children,u=i.mapChildren,d=i.sortKey,h=i.reverse,p=i.data,f=[],m={},g={};return d&&(e=nd(Eu(e),d),h&&(e=e.reverse())),wu(e,(function(e){n=e[s],g[n]=!0})),wu(e,(function(e){n=e[s],p?(o={})[p]=e:o=e,r=e[l],m[n]=m[n]||[],m[r]=m[r]||[],m[r].push(o),o[s]=n,o[l]=r,o[c]=m[n],u&&(o[u]=m[n]),(!a||a&&Gu(r))&&(g[r]||f.push(o))})),a&&function(e,t){wu(e,(function(e){e[t]&&!e[t].length&&xd(e,t)}))}(e,c),f},toTreeArray:function(e,t){return Sd([],e,Vu({},pu.treeOptions,t))},findTree:_d,eachTree:Nd,mapTree:Dd,filterTree:function(e,t,n,o){var r=[];return e&&t&&Nd(e,(function(e,n,i,a,s,l){t.call(o,e,n,i,a,s,l)&&r.push(e)}),n),r},searchTree:Td,hasOwnProp:vu,eqNull:Gu,isNaN:function(e){return Id(e)&&isNaN(e)},isFinite:function(e){return Id(e)&&isFinite(e)},isUndefined:Yu,isArray:yu,isFloat:function(e){return!($u(e)||isNaN(e)||yu(e)||dh(e))},isInteger:dh,isFunction:xu,isBoolean:hh,isString:Ad,isNumber:Id,isRegExp:ph,isObject:wd,isPlainObject:Fu,isDate:Pd,isError:fh,isTypeError:function(e){return!!e&&e.constructor===TypeError},isEmpty:mh,isNull:$u,isSymbol:yh,isArguments:vh,isElement:function(e){return!!(e&&Ad(e.nodeName)&&Id(e.nodeType))},isDocument:function(e){return!(!e||!bh||9!==e.nodeType)},isWindow:function(e){return wh&&!(!e||e!==e.window)},isFormData:function(e){return kh&&e instanceof FormData},isMap:function(e){return xh&&e instanceof Map},isWeakMap:function(e){return Sh&&e instanceof WeakMap},isSet:function(e){return Ch&&e instanceof Set},isWeakSet:function(e){return _h&&e instanceof WeakSet},isLeapYear:ah,isMatch:function(e,t){var n=Cu(e),o=Cu(t);return!o.length||(ju(n,o)?Ru(o,(function(o){return Dh(n,(function(n){return n===o&&Bh(e[n],t[o])}))>-1})):Bh(e,t))},isEqual:Bh,isEqualWith:function(e,t,n){return xu(n)?Eh(e,t,(function(e,t,o,r,i){var a=n(e,t,o,r,i);return Yu(a)?Th(e,t):!!a}),n):Eh(e,t,Th)},getType:function(e){return $u(e)?"null":yh(e)?"symbol":Pd(e)?"date":yu(e)?"array":ph(e)?"regexp":fh(e)?"error":typeof e},uniqueId:function(e){return[e,++Vh].join("")},getSize:uh,indexOf:lh,lastIndexOf:ch,findIndexOf:Dh,findLastIndexOf:Ih,toStringJSON:function(e){if(Fu(e))return e;if(Ad(e))try{return JSON.parse(e)}catch(Er){}return{}},toJSONString:function(e){return Gu(e)?"":JSON.stringify(e)},keys:Cu,values:id,entries:Ah,pick:$h,omit:Oh,first:function(e){return id(e)[0]},last:function(e){var t=id(e);return t[t.length-1]},each:wu,forOf:function(e,t,n){if(e)if(yu(e))for(var o=0,r=e.length;o<r&&!1!==t.call(n,e[o],o,e);o++);else for(var i in e)if(vu(e,i)&&!1===t.call(n,e[i],i,e))break},lastForOf:function(e,t,n){var o,r;if(e)if(yu(e))for(o=e.length-1;o>=0&&!1!==t.call(n,e[o],o,e);o--);else for(o=(r=vu(e)).length-1;o>=0&&!1!==t.call(n,e[r[o]],r[o],e);o--);},lastEach:bd,has:function(e,t){if(e){if(vu(e,t))return!0;var n,o,r,i,a,s,l=Zu(t),c=0,u=l.length;for(a=e;c<u&&(s=!1,(i=(n=l[c])?n.match(Ju):"")?(o=i[1],r=i[2],o?a[o]&&vu(a[o],r)&&(s=!0,a=a[o][r]):vu(a,r)&&(s=!0,a=a[r])):vu(a,n)&&(s=!0,a=a[n]),s);c++)if(c===u-1)return!0}return!1},get:Xu,set:function(e,t,n){if(e)if(!e[t]&&!vu(e,t)||Lh(t)){for(var o=e,r=Zu(t),i=r.length,a=0;a<i;a++)if(!Lh(r[a])){var s=a===i-1;o=Mh(o,r[a],s,s?null:r[a+1],n)}}else e[t]=n;return e},groupBy:zh,countBy:function(e,t,n){var o=zh(e,t,n||this);return bu(o,(function(e,t){o[t]=e.length})),o},clone:Eu,clear:kd,remove:xd,range:function(e,t,n){var o,r,i=[],a=arguments;if(a.length<2&&(t=a[0],e=0),r=t>>0,(o=e>>0)<t)for(n=n>>0||1;o<r;o+=n)i.push(o);return i},destructuring:function(e,t){if(e&&t){var n=Vu.apply(this,[{}].concat(cd(arguments,1))),o=Cu(n);fu(Cu(e),(function(t){qu(o,t)&&(e[t]=n[t])}))}return e},random:rd,min:Rh,max:fd,commafy:function(e,t){var n,o,r,i,a,s=Vu({},pu.commafyOptions,t),l=s.digits;return Id(e)?(n=(s.ceil?Gh:s.floor?Jh:Yh)(e,l),i=(o=Hh(l?Xh(n,l):n).split("."))[0],a=o[1],(r=i&&n<0)&&(i=i.substring(1,i.length))):i=(o=(n=Zh(e).replace(/,/g,""))?[n]:[])[0],o.length?(r?"-":"")+i.replace(new RegExp("(?=(?!(\\b))(.{"+(s.spaceNumber||3)+"})+$)","g"),s.separator||",")+(a?"."+a:""):n},round:Yh,ceil:Gh,floor:Jh,toFixed:Xh,toNumber:ld,toNumberString:Hh,toInteger:Qh,add:function(e,t){return tp(ld(e),ld(t))},subtract:function(e,t){var n=ld(e),o=ld(t),r=Hh(n),i=Hh(o),a=Uh(r),s=Uh(i),l=Math.pow(10,Math.max(a,s));return parseFloat(Xh((n*l-o*l)/l,a>=s?a:s))},multiply:ep,divide:function(e,t){return np(ld(e),ld(t))},sum:op,mean:function(e,t,n){return np(op(e,t,n),uh(e))},now:Cp,timestamp:function(e,t){if(e){var n=rh(e,t);return Pd(n)?Fd(n):n}return Cp()},isValidDate:cp,isDateSame:function(e,t,n){return!(!e||!t)&&("Invalid Date"!==(e=Sp(e,n))&&e===Sp(t,n))},toStringDate:rh,toDateString:Sp,getWhatYear:dp,getWhatQuarter:function(e,t,n){var o,r=t&&!isNaN(t)?3*t:0;return cp(e=rh(e))?(o=3*(function(e){var t=e.getMonth();return t<3?1:t<6?2:t<9?3:4}(e)-1),e.setMonth(o),up(e,r,n)):e},getWhatMonth:up,getWhatWeek:mp,getWhatDay:hp,getYearDay:bp,getYearWeek:yp,getMonthWeek:_p,getDayOfYear:function(e,t){return cp(e=rh(e))?ah(dp(e,t))?366:365:NaN},getDayOfMonth:function(e,t){return cp(e=rh(e))?Math.floor((Fd(up(e,t,ip))-Fd(up(e,t,rp)))/sp)+1:NaN},getDateDiff:function(e,t){var n,o,r,i,a,s,l={done:!1,time:0};if(e=rh(e),t=t?rh(t):ih(),cp(e)&&cp(t)&&(n=Fd(e))<(o=Fd(t)))for(i=l.time=o-n,l.done=!0,s=0,a=Np.length;s<a;s++)i>=(r=Np[s])[1]?s===a-1?l[r[0]]=i||0:(l[r[0]]=Math.floor(i/r[1]),i-=l[r[0]]*r[1]):l[r[0]]=0;return l},trim:Tp,trimLeft:Ep,trimRight:Dp,escape:Ip,unescape:Pp,camelCase:function(e){if(e=Zh(e),Fp[e])return Fp[e];var t=e.length,n=e.replace(/([-]+)/g,(function(e,n,o){return o&&o+n.length<t?"-":""}));return t=n.length,n=n.replace(/([A-Z]+)/g,(function(e,n,o){var r=n.length;return n=Op(n),o?r>2&&o+r<t?pp($p(n,0,1))+$p(n,1,r-1)+pp($p(n,r-1,r)):pp($p(n,0,1))+$p(n,1,r):r>1&&o+r<t?$p(n,0,r-1)+pp($p(n,r-1,r)):n})).replace(/(-[a-zA-Z])/g,(function(e,t){return pp($p(t,1,t.length))})),Fp[e]=n,n},kebabCase:function(e){if(e=Zh(e),Mp[e])return Mp[e];if(/^[A-Z]+$/.test(e))return Op(e);var t=e.replace(/^([a-z])([A-Z]+)([a-z]+)$/,(function(e,t,n,o){var r=n.length;return r>1?t+"-"+Op($p(n,0,r-1))+"-"+Op($p(n,r-1,r))+o:Op(t+"-"+n+o)})).replace(/^([A-Z]+)([a-z]+)?$/,(function(e,t,n){var o=t.length;return Op($p(t,0,o-1)+"-"+$p(t,o-1,o)+(n||""))})).replace(/([a-z]?)([A-Z]+)([a-z]?)/g,(function(e,t,n,o,r){var i=n.length;return i>1&&(t&&(t+="-"),o)?(t||"")+Op($p(n,0,i-1))+"-"+Op($p(n,i-1,i))+o:(t||"")+(r?"-":"")+Op(n)+(o||"")}));return t=t.replace(/([-]+)/g,(function(e,n,o){return o&&o+n.length<t.length?"-":""})),Mp[e]=t,t},repeat:function(e,t){return qh(Zh(e),t)},padStart:wp,padEnd:function(e,t,n){var o=Zh(e);return t>>=0,n=Yu(n)?" ":""+n,o.padEnd?o.padEnd(t,n):t>o.length?((t-=o.length)>n.length&&(n+=qh(n,t/n.length)),o+n.slice(0,t)):o},startsWith:function(e,t,n){var o=Zh(e);return 0===(1===arguments.length?o:o.substring(n)).indexOf(t)},endsWith:function(e,t,n){var o=Zh(e),r=arguments.length;return r>1&&(r>2?o.substring(0,n).indexOf(t)===n-1:o.indexOf(t)===o.length-1)},template:Lp,toFormatString:function(e,t){return Lp(e,t,{tmplRE:/\{([.\w[\]\s]+)\}/g})},toString:Zh,toValueString:Zh,noop:function(){},property:Ou,bind:function(e,t){var n=cd(arguments,2);return function(){return e.apply(t,cd(arguments).concat(n))}},once:function(e,t){var n=!1,o=null,r=cd(arguments,2);return function(){return n||(o=e.apply(t,cd(arguments).concat(r)),n=!0),o}},after:function(e,t,n){var o=0,r=[];return function(){var i=arguments;++o<=e&&r.push(i[0]),o>=e&&t.apply(n,[r].concat(cd(i)))}},before:function(e,t,n){var o=0,r=[];return n=n||this,function(){var i=arguments;++o<e&&(r.push(i[0]),t.apply(n,[r].concat(cd(i))))}},throttle:function(e,t,n){var o,r,i=n||{},a=!1,s=0,l=!("leading"in i)||i.leading,c="trailing"in i&&i.trailing,u=function(){a=!0,e.apply(r,o),s=setTimeout(d,t)},d=function(){s=0,a||!0!==c||u()},h=function(){o=arguments,r=this,a=!1,0===s&&(!0===l?u():!0===c&&(s=setTimeout(d,t)))};return h.cancel=function(){var e=0!==s;return clearTimeout(s),o=null,r=null,a=!1,s=0,e},h},debounce:function(e,t,n){var o,r,i=n||{},a=!1,s=0,l="boolean"==typeof n,c="leading"in i?i.leading:l,u="trailing"in i?i.trailing:!l,d=function(){a=!0,s=0,e.apply(r,o)},h=function(){!0===c&&(s=0),a||!0!==u||d()},p=function(){a=!1,o=arguments,r=this,0===s?!0===c&&d():clearTimeout(s),s=setTimeout(h,t)};return p.cancel=function(){var e=0!==s;return clearTimeout(s),o=null,r=null,s=0,e},p},delay:function(e,t){var n=cd(arguments,2),o=this;return setTimeout((function(){e.apply(o,n)}),t)},unserialize:Rp,serialize:function(e){var t,n=[];return wu(e,(function(e,o){Yu(e)||(t=yu(e),Fu(e)||t?n=n.concat(qp(e,o,t)):n.push(Up(o)+"="+Up($u(e)?"":e)))})),n.join("&").replace(/%20/g,"+")},parseUrl:Kp,getBaseURL:function(){if(jp){var e=jp.pathname,t=ch(e,"/")+1;return Hp()+(t===e.length?e:e.substring(0,t))}return""},locat:function(){return jp?Kp(jp.href):{}},browse:function(){var e,t,n,o=!1,r={isNode:!1,isMobile:o,isPC:!1,isDoc:!!bh};return wh||typeof process===Ku?(n=nf("Edge"),t=nf("Chrome"),o=/(Android|webOS|iPhone|iPad|iPod|SymbianOS|BlackBerry|Windows Phone)/.test(navigator.userAgent),r.isDoc&&(e=bh.body||bh.documentElement,fu(["webkit","khtml","moz","ms","o"],(function(t){r["-"+t]=!!e[t+"MatchesSelector"]}))),Vu(r,{edge:n,firefox:nf("Firefox"),msie:!n&&r["-ms"],safari:!t&&!n&&nf("Safari"),isMobile:o,isPC:!o,isLocalStorage:tf(wh.localStorage),isSessionStorage:tf(wh.sessionStorage)})):r.isNode=!0,r},cookie:Jp});const of={reLaunch:e=>uni.reLaunch({url:e}),switchTab:e=>uni.switchTab({url:e}),redirectTo:e=>uni.redirectTo({url:e}),navigateTo:e=>uni.navigateTo({url:e}),navigateBack:()=>uni.navigateBack()},rf={msg(e){uni.showToast({title:e,icon:"none"})},msgError(e){uni.showToast({title:e,icon:"error"})},msgSuccess(e){uni.showToast({title:e,icon:"success"})},hideMsg(e){uni.hideToast()},alert(e,t){uni.showModal({title:t||"警告提示",content:e,showCancel:!1})},confirm:e=>new Promise(((t,n)=>{uni.showModal({title:"系统提示",content:e,cancelText:"取消",confirmText:"确定",success:function(e){e.confirm&&t(e.confirm)}})})),showToast(e){"object"==typeof e?uni.showToast(e):uni.showToast({title:e,icon:"none",duration:2500})},loading(e){uni.showLoading({title:e,icon:"none"})},closeLoading(){uni.hideLoading()}},af={};af.dictTypeData=(e,t)=>{const n=Zn.getters.dictTypeTreeData;if(!n)return"需重新登录";const o=n.find((t=>t.dictValue===e));if(!o)return"无此字典";const r=o.children.find((e=>e.dictValue===t));return(null==r?void 0:r.name)||"无此字典"},af.dictList=e=>{const t=Zn.getters.dictTypeTreeData;if(!t)return[];const n=t.find((t=>t.dictValue===e));return n?n.children.map((e=>({value:e.dictValue,text:e.name}))):[]};const sf={tab:of,modal:rf,tool:af,hasPerm:function(e,t="or"){if(!e)return!1;const n=Zn.getters.userInfo;if(!n)return!1;const{mobileButtonCodeList:o}=n;if(!o)return!1;if(Array.isArray(e)){return e["or"===t?"some":"every"]((e=>o.includes(e)))}return o.includes(e)}};const{app:lf,Vuex:cf,Pinia:uf}=function(){const t=e.createVueApp(Wc);return t.use(Zn),t.use(hu),t.config.globalProperties.$snowy=sf,t.config.globalProperties.$xeu=Iu,uni.$snowy=sf,uni.$xeu=Iu,{app:t}}();uni.Vuex=cf,uni.Pinia=uf,lf.provide("__globalStyles",__uniConfig.styles),lf._component.mpType="app",lf._component.render=()=>{},lf.mount("#app")}(Vue,uni.VueShared);
