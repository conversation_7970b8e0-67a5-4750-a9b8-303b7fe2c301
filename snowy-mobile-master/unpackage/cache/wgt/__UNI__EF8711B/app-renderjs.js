var __renderjsModules={};
__renderjsModules.f9cb76fc=(()=>{var De=Object.defineProperty;var na=Object.getOwnPropertyDescriptor;var ha=Object.getOwnPropertyNames;var da=Object.prototype.hasOwnProperty;var oa=(i,e)=>{for(var l in e)De(i,l,{get:e[l],enumerable:!0})},fa=(i,e,l,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let t of ha(e))!da.call(i,t)&&t!==l&&De(i,t,{get:()=>e[t],enumerable:!(a=na(e,t))||a.enumerable});return i};var ua=i=>fa(De({},"__esModule",{value:!0}),i);var Di={};oa(Di,{default:()=>pi});var Ae={version:"v2.5.0-20230101",yAxisWidth:15,xAxisHeight:22,padding:[10,10,10,10],rotate:!1,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15,radarLabelTextMargin:13},k=function(i,...e){if(i==null)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!e||e.length<=0)return i;function l(a,t){for(let r in t)a[r]=a[r]&&a[r].toString()==="[object Object]"?l(a[r],t[r]):a[r]=t[r];return a}return e.forEach(a=>{i=l(i,a)}),i},fe={toFixed:function(e,l){return l=l||2,this.isFloat(e)&&(e=e.toFixed(l)),e},isFloat:function(e){return e%1!==0},approximatelyEqual:function(e,l){return Math.abs(e-l)<1e-10},isSameSign:function(e,l){return Math.abs(e)===e&&Math.abs(l)===l||Math.abs(e)!==e&&Math.abs(l)!==l},isSameXCoordinateArea:function(e,l){return this.isSameSign(e.x,l.x)},isCollision:function(e,l){e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height,l.end={},l.end.x=l.start.x+l.width,l.end.y=l.start.y-l.height;var a=l.start.x>e.end.x||l.end.x<e.start.x||l.end.y>e.start.y||l.start.y<e.end.y;return!a}};function M(i,e){var l=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,a=i.replace(l,function(n,g,o,f){return g+g+o+o+f+f}),t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(a),r=parseInt(t[1],16),h=parseInt(t[2],16),d=parseInt(t[3],16);return"rgba("+r+","+h+","+d+","+e+")"}function Ne(i,e,l){if(isNaN(i))throw new Error("[uCharts] series\u6570\u636E\u9700\u4E3ANumber\u683C\u5F0F");l=l||10,e=e||"upper";for(var a=1;l<1;)l*=10,a*=10;for(e==="upper"?i=Math.ceil(i*a):i=Math.floor(i*a);i%l!==0;)if(e==="upper"){if(i==i+1)break;i++}else i--;return i/a}function sa(i,e,l,a){let t=[];for(let r=0;r<i.length;r++){let h={data:[],name:e[r],color:l[r]};for(let d=0,n=a.length;d<n;d++){if(d<i[r]){h.data.push(null);continue}let g=0;for(let o=0;o<i[r];o++)g+=a[d-o][1];h.data.push(+(g/i[r]).toFixed(3))}t.push(h)}return t}function Re(i,e,l,a,t){var r=t.width-t.area[1]-t.area[3],h=l.eachSpacing*(t.chartData.xAxisData.xAxisPoints.length-1);t.type=="mount"&&t.extra&&t.extra.mount&&t.extra.mount.widthRatio&&t.extra.mount.widthRatio>1&&(t.extra.mount.widthRatio>2&&(t.extra.mount.widthRatio=2),h+=(t.extra.mount.widthRatio-1)*l.eachSpacing);var d=e;return e>=0?(d=0,i.uevent.trigger("scrollLeft"),i.scrollOption.position="left",t.xAxis.scrollPosition="left"):Math.abs(e)>=h-r?(d=r-h,i.uevent.trigger("scrollRight"),i.scrollOption.position="right",t.xAxis.scrollPosition="right"):(i.scrollOption.position=e,t.xAxis.scrollPosition=e),d}function Ke(i,e,l){function a(t){for(;t<0;)t+=2*Math.PI;for(;t>2*Math.PI;)t-=2*Math.PI;return t}return i=a(i),e=a(e),l=a(l),e>l&&(l+=2*Math.PI,i<e&&(i+=2*Math.PI)),i>=e&&i<=l}function me(i,e){function l(f,u){return f[u-1]&&f[u+1]?f[u].y>=Math.max(f[u-1].y,f[u+1].y)||f[u].y<=Math.min(f[u-1].y,f[u+1].y):!1}function a(f,u){return f[u-1]&&f[u+1]?f[u].x>=Math.max(f[u-1].x,f[u+1].x)||f[u].x<=Math.min(f[u-1].x,f[u+1].x):!1}var t=.2,r=.2,h=null,d=null,n=null,g=null;if(e<1?(h=i[0].x+(i[1].x-i[0].x)*t,d=i[0].y+(i[1].y-i[0].y)*t):(h=i[e].x+(i[e+1].x-i[e-1].x)*t,d=i[e].y+(i[e+1].y-i[e-1].y)*t),e>i.length-3){var o=i.length-1;n=i[o].x-(i[o].x-i[o-1].x)*r,g=i[o].y-(i[o].y-i[o-1].y)*r}else n=i[e+1].x-(i[e+2].x-i[e].x)*r,g=i[e+1].y-(i[e+2].y-i[e].y)*r;return l(i,e+1)&&(g=i[e+1].y),l(i,e)&&(d=i[e].y),a(i,e+1)&&(n=i[e+1].x),a(i,e)&&(h=i[e].x),(d>=Math.max(i[e].y,i[e+1].y)||d<=Math.min(i[e].y,i[e+1].y))&&(d=i[e].y),(g>=Math.max(i[e].y,i[e+1].y)||g<=Math.min(i[e].y,i[e+1].y))&&(g=i[e+1].y),(h>=Math.max(i[e].x,i[e+1].x)||h<=Math.min(i[e].x,i[e+1].x))&&(h=i[e].x),(n>=Math.max(i[e].x,i[e+1].x)||n<=Math.min(i[e].x,i[e+1].x))&&(n=i[e+1].x),{ctrA:{x:h,y:d},ctrB:{x:n,y:g}}}function oe(i,e,l){return{x:l.x+i,y:l.y-e}}function ga(i,e){if(e)for(;fe.isCollision(i,e);)i.start.x>0?i.start.y--:i.start.x<0||i.start.y>0?i.start.y++:i.start.y--;return i}function ya(i,e,l){let a=[];if(i.length>0&&i[0].data.constructor.toString().indexOf("Array")>-1){e._pieSeries_=i;let r=i[0].data;for(var t=0;t<r.length;t++)r[t].formatter=i[0].formatter,r[t].data=r[t].value,a.push(r[t]);e.series=a}else a=i;return a}function Le(i,e,l){for(var a=0,t=0;t<i.length;t++){let r=i[t];if(r.color||(r.color=l.color[a],a=(a+1)%l.color.length),r.linearIndex||(r.linearIndex=t),r.index||(r.index=0),r.type||(r.type=e.type),typeof r.show=="undefined"&&(r.show=!0),r.type||(r.type=e.type),r.pointShape||(r.pointShape="circle"),!r.legendShape)switch(r.type){case"line":r.legendShape="line";break;case"column":case"bar":r.legendShape="rect";break;case"area":case"mount":r.legendShape="triangle";break;default:r.legendShape="circle"}}return i}function ue(i,e,l,a){var t=e||[];if(i=="custom"&&t.length==0&&(t=a.linearColor),i=="custom"&&t.length<l.length){let h=l.length-t.length;for(var r=0;r<h;r++)t.push(a.linearColor[(r+1)%a.linearColor.length])}return t}function ca(i,e){var l=0,a=e-i;return a>=1e4?l=1e3:a>=1e3?l=100:a>=100?l=10:a>=10?l=5:a>=1?l=1:a>=.1?l=.1:a>=.01?l=.01:a>=.001?l=.001:a>=1e-4?l=1e-4:a>=1e-5?l=1e-5:l=1e-6,{minRange:Ne(i,"lower",l),maxRange:Ne(e,"upper",l)}}function H(i,e,l){var a=0;if(i=String(i),l=!1,l!==!1&&l!==void 0&&l.setFontSize&&l.measureText)return l.setFontSize(e),l.measureText(i).width;var i=i.split("");for(let r=0;r<i.length;r++){let h=i[r];/[a-zA-Z]/.test(h)?a+=7:/[0-9]/.test(h)?a+=5.5:/\./.test(h)?a+=2.7:/-/.test(h)?a+=3.25:/:/.test(h)?a+=2.5:/[\u4e00-\u9fa5]/.test(h)?a+=10:/\(|\)/.test(h)?a+=3.73:/\s/.test(h)?a+=2.5:/%/.test(h)?a+=8:a+=10}return a*e/10}function Ce(i){return i.reduce(function(e,l){return(e.data?e.data:e).concat(l.data)},[])}function Qe(i,e){for(var l=new Array(e),a=0;a<l.length;a++)l[a]=0;for(var t=0;t<i.length;t++)for(var a=0;a<l.length;a++)l[a]+=i[t].data[a];return i.reduce(function(r,h){return(r.data?r.data:r).concat(h.data).concat(l)},[])}function ye(i,e,l){let a,t;return i.clientX?e.rotate?(t=e.height-i.clientX*e.pix,a=(i.pageY-l.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):(a=i.clientX*e.pix,t=(i.pageY-l.currentTarget.offsetTop-e.height/e.pix/2*(e.pix-1))*e.pix):e.rotate?(t=e.height-i.x*e.pix,a=i.y*e.pix):(a=i.x*e.pix,t=i.y*e.pix),{x:a,y:t}}function Te(i,e,l){var a=[],t=[],r=e.constructor.toString().indexOf("Array")>-1;if(r){let d=ea(i);for(var h=0;h<l.length;h++)t.push(d[l[h]])}else t=i;for(let d=0;d<t.length;d++){let n=t[d],g=-1;if(r?g=e[d]:g=e,n.data[g]!==null&&typeof n.data[g]!="undefined"&&n.show){let o={};o.color=n.color,o.type=n.type,o.style=n.style,o.pointShape=n.pointShape,o.disableLegend=n.disableLegend,o.legendShape=n.legendShape,o.name=n.name,o.show=n.show,o.data=n.formatter?n.formatter(n.data[g]):n.data[g],a.push(o)}}return a}function xa(i,e,l){var a=i.map(function(t){return H(t,e,l)});return Math.max.apply(null,a)}function va(i){for(var e=2*Math.PI/i,l=[],a=0;a<i;a++)l.push(e*a);return l.map(function(t){return-1*t+Math.PI/2})}function He(i,e,l,a,t){var r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},h=e.chartData.calPoints?e.chartData.calPoints:[];let d={};if(a.length>0){let o=[];for(let f=0;f<a.length;f++)o.push(h[a[f]]);d=o[0][l[0]]}else for(let o=0;o<h.length;o++)if(h[o][l]){d=h[o][l];break}var n=i.map(function(o){let f=null;return e.categories&&e.categories.length>0&&(f=t[l]),{text:r.formatter?r.formatter(o,f,l,e):o.name+": "+o.data,color:o.color,legendShape:e.extra.tooltip.legendShape=="auto"?o.legendShape:e.extra.tooltip.legendShape}}),g={x:Math.round(d.x),y:Math.round(d.y)};return{textList:n,offset:g}}function ma(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{},r=e.chartData.xAxisPoints[l]+e.chartData.eachSpacing/2,h=i.map(function(n){return{text:t.formatter?t.formatter(n,a[l],l,e):n.name+": "+n.data,color:n.color,disableLegend:!!n.disableLegend,legendShape:e.extra.tooltip.legendShape=="auto"?n.legendShape:e.extra.tooltip.legendShape}});h=h.filter(function(n){if(n.disableLegend!==!0)return n});var d={x:Math.round(r),y:0};return{textList:h,offset:d}}function ba(i,e,l,a,t,r){var h=arguments.length>6&&arguments[6]!==void 0?arguments[6]:{},d=l.chartData.calPoints;let n=r.color.upFill,g=r.color.downFill,o=[n,n,g,n];var f=[];e.map(function(y){a==0?y.data[1]-y.data[0]<0?o[1]=g:o[1]=n:(y.data[0]<i[a-1][1]&&(o[0]=g),y.data[1]<y.data[0]&&(o[1]=g),y.data[2]>i[a-1][1]&&(o[2]=n),y.data[3]<i[a-1][1]&&(o[3]=g));let c={text:"\u5F00\u76D8\uFF1A"+y.data[0],color:o[0],legendShape:l.extra.tooltip.legendShape=="auto"?y.legendShape:l.extra.tooltip.legendShape},x={text:"\u6536\u76D8\uFF1A"+y.data[1],color:o[1],legendShape:l.extra.tooltip.legendShape=="auto"?y.legendShape:l.extra.tooltip.legendShape},v={text:"\u6700\u4F4E\uFF1A"+y.data[2],color:o[2],legendShape:l.extra.tooltip.legendShape=="auto"?y.legendShape:l.extra.tooltip.legendShape},b={text:"\u6700\u9AD8\uFF1A"+y.data[3],color:o[3],legendShape:l.extra.tooltip.legendShape=="auto"?y.legendShape:l.extra.tooltip.legendShape};f.push(c,x,v,b)});var u=[],s={x:0,y:0};for(let y=0;y<d.length;y++){let c=d[y];typeof c[a]!="undefined"&&c[a]!==null&&u.push(c[a])}return s.x=Math.round(u[0][0].x),{textList:f,offset:s}}function ea(i){let e=[];for(let l=0;l<i.length;l++)i[l].show==!0&&e.push(i[l]);return e}function Aa(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0,r={index:-1,group:[]},h=l.chartData.eachSpacing/2;let d=[];if(e&&e.length>0){if(!l.categories)h=0;else{for(let n=1;n<l.chartData.xAxisPoints.length;n++)d.push(l.chartData.xAxisPoints[n]-h);(l.type=="line"||l.type=="area")&&l.xAxis.boundaryGap=="justify"&&(d=l.chartData.xAxisPoints)}if(aa(i,l,a))if(l.categories)d.forEach(function(n,g){i.x+t+h>n&&(r.index=g)});else{let n=Array(e.length);for(let u=0;u<e.length;u++){n[u]=Array(e[u].length);for(let s=0;s<e[u].length;s++)n[u][s]=Math.abs(e[u][s].x-i.x)}let g=Array(n.length),o=Array(n.length);for(let u=0;u<n.length;u++)g[u]=Math.min.apply(null,n[u]),o[u]=n[u].indexOf(g[u]);let f=Math.min.apply(null,g);r.index=[];for(let u=0;u<g.length;u++)g[u]==f&&(r.group.push(u),r.index.push(o[u]))}}return r}function Ta(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0,r={index:-1,group:[]},h=l.chartData.eachSpacing/2;let d=l.chartData.yAxisPoints;return e&&e.length>0&&aa(i,l,a)&&d.forEach(function(n,g){i.y+t+h>n&&(r.index=g)}),r}function Sa(i,e,l){let a=-1,t=0;if(wa(i,e.area)){let r=e.points,h=-1;for(let d=0,n=r.length;d<n;d++){let g=r[d];for(let o=0;o<g.length;o++){h+=1;let f=g[o].area;if(f&&i.x>f[0]-t&&i.x<f[2]+t&&i.y>f[1]-t&&i.y<f[3]+t){a=h;break}}}return a}return a}function wa(i,e){return i.x>e.start.x&&i.x<e.end.x&&i.y>e.start.y&&i.y<e.end.y}function aa(i,e,l){return i.x<=e.width-e.area[1]+10&&i.x>=e.area[3]-10&&i.y>=e.area[0]&&i.y<=e.height-e.area[2]}function Pa(i,e,l){var a=2*Math.PI/l,t=-1;if(Oe(i,e.center,e.radius)){var r=function(g){return g<0&&(g+=2*Math.PI),g>2*Math.PI&&(g-=2*Math.PI),g},h=Math.atan2(e.center.y-i.y,i.x-e.center.x);h=-1*h,h<0&&(h+=2*Math.PI);var d=e.angleList.map(function(n){return n=r(-1*n),n});d.forEach(function(n,g){var o=r(n-a/2),f=r(n+a/2);f<o&&(f+=2*Math.PI),(h>=o&&h<=f||h+2*Math.PI>=o&&h+2*Math.PI<=f)&&(t=g)})}return t}function Ca(i,e){for(var l=-1,a=0,t=e.series.length;a<t;a++){var r=e.series[a];if(i.x>r.funnelArea[0]&&i.x<r.funnelArea[2]&&i.y>r.funnelArea[1]&&i.y<r.funnelArea[3]){l=a;break}}return l}function Fa(i,e){for(var l=-1,a=0,t=e.length;a<t;a++){var r=e[a];if(i.x>r.area[0]&&i.x<r.area[2]&&i.y>r.area[1]&&i.y<r.area[3]){l=a;break}}return l}function Ma(i,e){for(var l=-1,a=e.chartData.mapData,t=e.series,r=ci(i.y,i.x,a.bounds,a.scale,a.xoffset,a.yoffset),h=[r.x,r.y],d=0,n=t.length;d<n;d++){var g=t[d].geometry.coordinates;if(vi(h,g,e.chartData.mapData.mercator)){l=d;break}}return l}function pa(i,e,l){var a=-1,t=ia(l._series_,l.extra.rose.type,e.radius,e.radius);if(e&&e.center&&Oe(i,e.center,e.radius)){var r=Math.atan2(e.center.y-i.y,i.x-e.center.x);r=-r,l.extra.rose&&l.extra.rose.offsetAngle&&(r=r-l.extra.rose.offsetAngle*Math.PI/180);for(var h=0,d=t.length;h<d;h++)if(Ke(r,t[h]._start_,t[h]._start_+t[h]._rose_proportion_*2*Math.PI)){a=h;break}}return a}function Da(i,e,l){var a=-1,t=We(e.series);if(e&&e.center&&Oe(i,e.center,e.radius)){var r=Math.atan2(e.center.y-i.y,i.x-e.center.x);r=-r,l.extra.pie&&l.extra.pie.offsetAngle&&(r=r-l.extra.pie.offsetAngle*Math.PI/180),l.extra.ring&&l.extra.ring.offsetAngle&&(r=r-l.extra.ring.offsetAngle*Math.PI/180);for(var h=0,d=t.length;h<d;h++)if(Ke(r,t[h]._start_,t[h]._start_+t[h]._proportion_*2*Math.PI)){a=h;break}}return a}function Oe(i,e,l){return Math.pow(i.x-e.x,2)+Math.pow(i.y-e.y,2)<=Math.pow(l,2)}function be(i,e){var l=[],a=[];return i.forEach(function(t,r){e.connectNulls?t!==null&&a.push(t):t!==null?a.push(t):(a.length&&l.push(a),a=[])}),a.length&&l.push(a),l}function La(i,e,l,a,t){let r={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(e.legend.show===!1)return a.legendData=r,r;let h=e.legend.padding*e.pix,d=e.legend.margin*e.pix,n=e.legend.fontSize?e.legend.fontSize*e.pix:l.fontSize,g=15*e.pix,o=5*e.pix,f=Math.max(e.legend.lineHeight*e.pix,n);if(e.legend.position=="top"||e.legend.position=="bottom"){let u=[],s=0,y=[],c=[];for(let x=0;x<i.length;x++){let v=i[x],b=v.legendText?v.legendText:v.name,m=g+o+H(b||"undefined",n,t)+e.legend.itemGap*e.pix;s+m>e.width-e.area[1]-e.area[3]?(u.push(c),y.push(s-e.legend.itemGap*e.pix),s=m,c=[v]):(s+=m,c.push(v))}if(c.length){u.push(c),y.push(s-e.legend.itemGap*e.pix),r.widthArr=y;let x=Math.max.apply(null,y);switch(e.legend.float){case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+x+2*h;break;case"right":r.area.start.x=e.width-e.area[1]-x-2*h,r.area.end.x=e.width-e.area[1];break;default:r.area.start.x=(e.width-x)/2-h,r.area.end.x=(e.width+x)/2+h}r.area.width=x+2*h,r.area.wholeWidth=x+2*h,r.area.height=u.length*f+2*h,r.area.wholeHeight=u.length*f+2*h+2*d,r.points=u}}else{let u=i.length,s=e.height-e.area[0]-e.area[2]-2*d-2*h,y=Math.min(Math.floor(s/f),u);switch(r.area.height=y*f+h*2,r.area.wholeHeight=y*f+h*2,e.legend.float){case"top":r.area.start.y=e.area[0]+d,r.area.end.y=e.area[0]+d+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-d-r.area.height,r.area.end.y=e.height-e.area[2]-d;break;default:r.area.start.y=(e.height-r.area.height)/2,r.area.end.y=(e.height+r.area.height)/2}let c=u%y===0?u/y:Math.floor(u/y+1),x=[];for(let v=0;v<c;v++){let b=i.slice(v*y,v*y+y);x.push(b)}if(r.points=x,x.length){for(let b=0;b<x.length;b++){let m=x[b],S=0;for(let w=0;w<m.length;w++){let C=g+o+H(m[w].name||"undefined",n,t)+e.legend.itemGap*e.pix;C>S&&(S=C)}r.widthArr.push(S),r.heightArr.push(m.length*f+h*2)}let v=0;for(let b=0;b<r.widthArr.length;b++)v+=r.widthArr[b];r.area.width=v-e.legend.itemGap*e.pix+2*h,r.area.wholeWidth=r.area.width+h}}switch(e.legend.position){case"top":r.area.start.y=e.area[0]+d,r.area.end.y=e.area[0]+d+r.area.height;break;case"bottom":r.area.start.y=e.height-e.area[2]-r.area.height-d,r.area.end.y=e.height-e.area[2]-d;break;case"left":r.area.start.x=e.area[3],r.area.end.x=e.area[3]+r.area.width;break;case"right":r.area.start.x=e.width-e.area[1]-r.area.width,r.area.end.x=e.width-e.area[1];break}return a.legendData=r,r}function Xe(i,e,l,a,t){var r={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix},h=e.xAxis.fontSize*e.pix,d=i.map(function(g,o){var f=e.xAxis.formatter?e.xAxis.formatter(g,o,e):g;return H(String(f),h,t)}),n=Math.max.apply(this,d);if(e.xAxis.rotateLabel==!0){r.angle=e.xAxis.rotateAngle*Math.PI/180;let g=e.xAxis.marginTop*e.pix*2+Math.abs(n*Math.sin(r.angle));g=g<h+e.xAxis.marginTop*e.pix*2?g+e.xAxis.marginTop*e.pix*2:g,r.xAxisHeight=g}return e.enableScroll&&e.xAxis.scrollShow&&(r.xAxisHeight+=6*e.pix),e.xAxis.disabled&&(r.xAxisHeight=0),r}function _a(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1,r;a=="stack"?r=Qe(i,e.categories.length):r=Ce(i);var h=[];r=r.filter(function(c){return typeof c=="object"&&c!==null?c.constructor.toString().indexOf("Array")>-1?c!==null:c.value!==null:c!==null}),r.map(function(c){typeof c=="object"?c.constructor.toString().indexOf("Array")>-1?e.type=="candle"?c.map(function(x){h.push(x)}):h.push(c[0]):h.push(c.value):h.push(c)});var d=0,n=0;if(h.length>0&&(d=Math.min.apply(this,h),n=Math.max.apply(this,h)),t>-1?(typeof e.xAxis.data[t].min=="number"&&(d=Math.min(e.xAxis.data[t].min,d)),typeof e.xAxis.data[t].max=="number"&&(n=Math.max(e.xAxis.data[t].max,n))):(typeof e.xAxis.min=="number"&&(d=Math.min(e.xAxis.min,d)),typeof e.xAxis.max=="number"&&(n=Math.max(e.xAxis.max,n))),d===n){var g=n||10;n+=g}for(var o=d,f=n,u=[],s=(f-o)/e.xAxis.splitNumber,y=0;y<=e.xAxis.splitNumber;y++)u.push(o+s*y);return u}function ka(i,e,l,a){var t=k({},{type:""},e.extra.bar),r={angle:0,xAxisHeight:e.xAxis.lineHeight*e.pix+e.xAxis.marginTop*e.pix};r.ranges=_a(i,e,l,t.type),r.rangesFormat=r.ranges.map(function(g){return g=fe.toFixed(g,2),g});var h=r.ranges.map(function(g){return g=fe.toFixed(g,2),g});r=Object.assign(r,we(h,e,l));var d=r.eachSpacing,n=h.map(function(g){return H(g,e.xAxis.fontSize*e.pix,a)});return e.xAxis.disabled===!0&&(r.xAxisHeight=0),r}function Ia(i,e,l,a,t){var r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:1,h=t.extra.radar||{};h.max=h.max||0;var d=Math.max(h.max,Math.max.apply(null,Ce(a))),n=[];for(let g=0;g<a.length;g++){let o=a[g],f={};f.color=o.color,f.legendShape=o.legendShape,f.pointShape=o.pointShape,f.data=[],o.data.forEach(function(u,s){let y={};y.angle=i[s],y.proportion=u/d,y.value=u,y.position=oe(l*y.proportion*r*Math.cos(y.angle),l*y.proportion*r*Math.sin(y.angle),e),f.data.push(y)}),n.push(f)}return n}function We(i,e){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,a=0,t=0;for(let r=0;r<i.length;r++){let h=i[r];h.data=h.data===null?0:h.data,a+=h.data}for(let r=0;r<i.length;r++){let h=i[r];h.data=h.data===null?0:h.data,a===0?h._proportion_=1/i.length*l:h._proportion_=h.data/a*l,h._radius_=e}for(let r=0;r<i.length;r++){let h=i[r];h._start_=t,t+=2*h._proportion_*Math.PI}return i}function za(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1;for(let r=0;r<i.length;r++)l.type=="funnel"?i[r].radius=i[r].data/i[0].data*e*t:i[r].radius=a*(i.length-r)/(a*i.length)*e*t,i[r]._proportion_=i[r].data/i[0].data;return i}function ia(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=0,h=0,d=[];for(let f=0;f<i.length;f++){let u=i[f];u.data=u.data===null?0:u.data,r+=u.data,d.push(u.data)}var n=Math.min.apply(null,d),g=Math.max.apply(null,d),o=a-l;for(let f=0;f<i.length;f++){let u=i[f];u.data=u.data===null?0:u.data,r===0?(u._proportion_=1/i.length*t,u._rose_proportion_=1/i.length*t):(u._proportion_=u.data/r*t,e=="area"?u._rose_proportion_=1/i.length*t:u._rose_proportion_=u.data/r*t),u._radius_=l+o*((u.data-n)/(g-n))||a}for(let f=0;f<i.length;f++){let u=i[f];u._start_=h,h+=2*u._rose_proportion_*Math.PI}return i}function Ra(i,e){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;l==1&&(l=.999999);for(let a=0;a<i.length;a++){let t=i[a];t.data=t.data===null?0:t.data;let r;e.type=="circle"?r=2:e.direction=="ccw"?e.startAngle<e.endAngle?r=2+e.startAngle-e.endAngle:r=e.startAngle-e.endAngle:e.endAngle<e.startAngle?r=2+e.endAngle-e.startAngle:r=e.startAngle-e.endAngle,t._proportion_=r*t.data*l+e.startAngle,e.direction=="ccw"&&(t._proportion_=e.startAngle-r*t.data*l),t._proportion_>=2&&(t._proportion_=t._proportion_%2)}return i}function Oa(i,e){var l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;l==1&&(l=.999999);for(let a=0;a<i.length;a++){let t=i[a];t.data=t.data===null?0:t.data;let r;e.type=="circle"?r=2:e.endAngle<e.startAngle?r=2+e.endAngle-e.startAngle:r=e.startAngle-e.endAngle,t._proportion_=r*t.data*l+e.startAngle,t._proportion_>=2&&(t._proportion_=t._proportion_%2)}return i}function Wa(i,e,l){let a;l<e?a=2+l-e:a=e-l;let t=e;for(let r=0;r<i.length;r++)i[r].value=i[r].value===null?0:i[r].value,i[r]._startAngle_=t,i[r]._endAngle_=a*i[r].value+e,i[r]._endAngle_>=2&&(i[r]._endAngle_=i[r]._endAngle_%2),t=i[r]._endAngle_;return i}function Ea(i,e,l){let a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;for(let t=0;t<i.length;t++){let r=i[t];if(r.data=r.data===null?0:r.data,l.pointer.color=="auto"){for(let d=0;d<e.length;d++)if(r.data<=e[d].value){r.color=e[d].color;break}}else r.color=l.pointer.color;let h;l.endAngle<l.startAngle?h=2+l.endAngle-l.startAngle:h=l.startAngle-l.endAngle,r._endAngle_=h*r.data+l.startAngle,r._oldAngle_=l.oldAngle,l.oldAngle<l.endAngle&&(r._oldAngle_+=2),r.data>=l.oldData?r._proportion_=(r._endAngle_-r._oldAngle_)*a+l.oldAngle:r._proportion_=r._oldAngle_-(r._oldAngle_-r._endAngle_)*a,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return i}function Ba(i,e,l,a){i=We(i);let t=0;for(let r=0;r<i.length;r++){let h=i[r],d=h.formatter?h.formatter(+h._proportion_.toFixed(2)):fe.toFixed(h._proportion_*100)+"%";t=Math.max(t,H(d,h.textSize*a.pix||e.fontSize,l))}return t}function Pe(i,e,l,a,t,r){return i.map(function(h){if(h===null)return null;var d=0,n=0;return r.type=="mix"?(d=r.extra.mix.column.seriesGap*r.pix||0,n=r.extra.mix.column.categoryGap*r.pix||0):(d=r.extra.column.seriesGap*r.pix||0,n=r.extra.column.categoryGap*r.pix||0),d=Math.min(d,e/l),n=Math.min(n,e/l),h.width=Math.ceil((e-2*n-d*(l-1))/l),r.extra.mix&&r.extra.mix.column.width&&+r.extra.mix.column.width>0&&(h.width=Math.min(h.width,+r.extra.mix.column.width*r.pix)),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(h.width=Math.min(h.width,+r.extra.column.width*r.pix)),h.width<=0&&(h.width=1),h.x+=(a+.5-l/2)*(h.width+d),h})}function Ye(i,e,l,a,t,r){return i.map(function(h){if(h===null)return null;var d=0,n=0;return d=r.extra.bar.seriesGap*r.pix||0,n=r.extra.bar.categoryGap*r.pix||0,d=Math.min(d,e/l),n=Math.min(n,e/l),h.width=Math.ceil((e-2*n-d*(l-1))/l),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(h.width=Math.min(h.width,+r.extra.bar.width*r.pix)),h.width<=0&&(h.width=1),h.y+=(a+.5-l/2)*(h.width+d),h})}function Ga(i,e,l,a,t,r,h){var d=r.extra.column.categoryGap*r.pix||0;return i.map(function(n){return n===null?null:(n.width=e-2*d,r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(n.width=Math.min(n.width,+r.extra.column.width*r.pix)),a>0&&(n.width-=h),n)})}function Na(i,e,l,a,t,r,h){var d=r.extra.column.categoryGap*r.pix||0;return i.map(function(n,g){return n===null?null:(n.width=Math.ceil(e-2*d),r.extra.column&&r.extra.column.width&&+r.extra.column.width>0&&(n.width=Math.min(n.width,+r.extra.column.width*r.pix)),n.width<=0&&(n.width=1),n)})}function Ha(i,e,l,a,t,r,h){var d=r.extra.bar.categoryGap*r.pix||0;return i.map(function(n,g){return n===null?null:(n.width=Math.ceil(e-2*d),r.extra.bar&&r.extra.bar.width&&+r.extra.bar.width>0&&(n.width=Math.min(n.width,+r.extra.bar.width*r.pix)),n.width<=0&&(n.width=1),n)})}function we(i,e,l){var a=e.width-e.area[1]-e.area[3],t=e.enableScroll?Math.min(e.xAxis.itemCount,i.length):i.length;(e.type=="line"||e.type=="area"||e.type=="scatter"||e.type=="bubble"||e.type=="bar")&&t>1&&e.xAxis.boundaryGap=="justify"&&(t-=1);var r=0;e.type=="mount"&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),r=e.extra.mount.widthRatio-1,t+=r);var h=a/t,d=[],n=e.area[3],g=e.width-e.area[1];return i.forEach(function(o,f){d.push(n+r/2*h+f*h)}),e.xAxis.boundaryGap!=="justify"&&(e.enableScroll===!0?d.push(n+r*h+i.length*h):d.push(g)),{xAxisPoints:d,startX:n,endX:g,eachSpacing:h}}function Xa(i,e,l,a,t,r,h){var d=arguments.length>7&&arguments[7]!==void 0?arguments[7]:1,n=[],g=r.height-r.area[0]-r.area[2];return i.forEach(function(o,f){if(o===null)n.push(null);else{var u=[];o.forEach(function(s,y){var c={};c.x=a[f]+Math.round(t/2);var x=s.value||s,v=g*(x-e)/(l-e);v*=d,c.y=r.height-Math.round(v)-r.area[2],u.push(c)}),n.push(u)}}),n}function ie(i,e,l,a,t,r,h){var d=arguments.length>7&&arguments[7]!==void 0?arguments[7]:1,n="center";(r.type=="line"||r.type=="area"||r.type=="scatter"||r.type=="bubble")&&(n=r.xAxis.boundaryGap);var g=[],o=r.height-r.area[0]-r.area[2],f=r.width-r.area[1]-r.area[3];return i.forEach(function(u,s){if(u===null)g.push(null);else{var y={};y.color=u.color,y.x=a[s];var c=u;if(typeof u=="object"&&u!==null)if(u.constructor.toString().indexOf("Array")>-1){let v,b,m;v=[].concat(r.chartData.xAxisData.ranges),b=v.shift(),m=v.pop(),c=u[1],y.x=r.area[3]+f*(u[0]-b)/(m-b),r.type=="bubble"&&(y.r=u[2],y.t=u[3])}else c=u.value;n=="center"&&(y.x+=t/2);var x=o*(c-e)/(l-e);x*=d,y.y=r.height-x-r.area[2],g.push(y)}}),g}function Ya(i,e,l,a,t,r,h,d,g){var g=arguments.length>8&&arguments[8]!==void 0?arguments[8]:1,o=r.xAxis.boundaryGap,f=[],u=r.height-r.area[0]-r.area[2],s=r.width-r.area[1]-r.area[3];return i.forEach(function(y,c){if(y===null)f.push(null);else{var x={};if(x.color=y.color,d.animation=="vertical"){x.x=a[c];var v=y;if(typeof y=="object"&&y!==null)if(y.constructor.toString().indexOf("Array")>-1){let m,S,w;m=[].concat(r.chartData.xAxisData.ranges),S=m.shift(),w=m.pop(),v=y[1],x.x=r.area[3]+s*(y[0]-S)/(w-S)}else v=y.value;o=="center"&&(x.x+=t/2);var b=u*(v-e)/(l-e);b*=g,x.y=r.height-b-r.area[2],f.push(x)}else{x.x=a[0]+t*c*g;var v=y;o=="center"&&(x.x+=t/2);var b=u*(v-e)/(l-e);x.y=r.height-b-r.area[2],f.push(x)}}}),f}function $e(i,e,l,a,t,r,h,d,g){var g=arguments.length>8&&arguments[8]!==void 0?arguments[8]:1,o=[],f=r.height-r.area[0]-r.area[2],u=r.width-r.area[1]-r.area[3];return i.forEach(function(s,y){if(s===null)o.push(null);else{var c={};c.color=s.color,c.x=a[y];var x=s;if(typeof s=="object"&&s!==null)if(s.constructor.toString().indexOf("Array")>-1){let b,m,S;b=[].concat(r.chartData.xAxisData.ranges),m=b.shift(),S=b.pop(),x=s[1],c.x=r.area[3]+u*(s[0]-m)/(S-m)}else x=s.value;c.x+=t/2;var v=f*(x*g-e)/(l-e);c.y=r.height-v-r.area[2],o.push(c)}}),o}function Ve(i,e,l,a,t,r,h,d){var n=arguments.length>8&&arguments[8]!==void 0?arguments[8]:1,g=[],o=r.height-r.area[0]-r.area[2],f=r.width-r.area[1]-r.area[3],u=t*h.widthRatio;return i.forEach(function(s,y){if(s===null)g.push(null);else{var c={};c.color=s.color,c.x=a[y],c.x+=t/2;var x=s.data,v=o*(x*n-e)/(l-e);c.y=r.height-v-r.area[2],c.value=x,c.width=u,g.push(c)}}),g}function qe(i,e,l,a,t,r,h){var d=arguments.length>7&&arguments[7]!==void 0?arguments[7]:1,n=[],g=r.height-r.area[0]-r.area[2],o=r.width-r.area[1]-r.area[3];return i.forEach(function(f,u){if(f===null)n.push(null);else{var s={};s.color=f.color,s.y=a[u];var y=f;typeof f=="object"&&f!==null&&(y=f.value);var c=o*(y-e)/(l-e);c*=d,s.height=c,s.value=y,s.x=c+r.area[3],n.push(s)}}),n}function _e(i,e,l,a,t,r,h,d,n){var g=arguments.length>9&&arguments[9]!==void 0?arguments[9]:1,o=[],f=r.height-r.area[0]-r.area[2];return i.forEach(function(u,s){if(u===null)o.push(null);else{var y={};if(y.color=u.color,y.x=a[s]+Math.round(t/2),d>0){var c=0;for(let S=0;S<=d;S++)c+=n[S].data[s];var x=c-u,v=f*(c-e)/(l-e),b=f*(x-e)/(l-e)}else{var c=u;typeof u=="object"&&u!==null&&(c=u.value);var v=f*(c-e)/(l-e),b=0}var m=b;v*=g,m*=g,y.y=r.height-Math.round(v)-r.area[2],y.y0=r.height-Math.round(m)-r.area[2],o.push(y)}}),o}function ke(i,e,l,a,t,r,h,d,n){var g=arguments.length>9&&arguments[9]!==void 0?arguments[9]:1,o=[],f=r.width-r.area[1]-r.area[3];return i.forEach(function(u,s){if(u===null)o.push(null);else{var y={};if(y.color=u.color,y.y=a[s],d>0){var c=0;for(let S=0;S<=d;S++)c+=n[S].data[s];var x=c-u,v=f*(c-e)/(l-e),b=f*(x-e)/(l-e)}else{var c=u;typeof u=="object"&&u!==null&&(c=u.value);var v=f*(c-e)/(l-e),b=0}var m=b;v*=g,m*=g,y.height=v-m,y.x=r.area[3]+v,y.x0=r.area[3]+m,o.push(y)}}),o}function Je(i,e,l,a,t){var r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:-1,h;a=="stack"?h=Qe(i,e.categories.length):h=Ce(i);var d=[];h=h.filter(function(x){return typeof x=="object"&&x!==null?x.constructor.toString().indexOf("Array")>-1?x!==null:x.value!==null:x!==null}),h.map(function(x){typeof x=="object"?x.constructor.toString().indexOf("Array")>-1?e.type=="candle"?x.map(function(v){d.push(v)}):d.push(x[1]):d.push(x.value):d.push(x)});var n=t.min||0,g=t.max||0;d.length>0&&(n=Math.min.apply(this,d),g=Math.max.apply(this,d)),n===g&&(g==0?g=10:n=0);for(var o=ca(n,g),f=t.min===void 0||t.min===null?o.minRange:t.min,u=t.max===void 0||t.max===null?o.maxRange:t.max,s=(u-f)/e.yAxis.splitNumber,y=[],c=0;c<=e.yAxis.splitNumber;c++)y.push(f+s*c);return y.reverse()}function Ee(i,e,l,a){var t=k({},{type:""},e.extra.column),r=e.yAxis.data.length,h=new Array(r);if(r>0){for(let f=0;f<r;f++){h[f]=[];for(let u=0;u<i.length;u++)i[u].index==f&&h[f].push(i[u])}var d=new Array(r),n=new Array(r),g=new Array(r);for(let f=0;f<r;f++){let u=e.yAxis.data[f];e.yAxis.disabled==!0&&(u.disabled=!0),u.type==="categories"?(u.formatter||(u.formatter=(c,x,v)=>c+(u.unit||"")),u.categories=u.categories||e.categories,d[f]=u.categories):(u.formatter||(u.formatter=(c,x,v)=>fe.toFixed(c,u.tofix||0)+(u.unit||"")),d[f]=Je(h[f],e,l,t.type,u,f));let s=u.fontSize*e.pix||l.fontSize;g[f]={position:u.position?u.position:"left",width:0},n[f]=d[f].map(function(c,x){return c=u.formatter(c,x,e),g[f].width=Math.max(g[f].width,H(c,s,a)+5),c});let y=u.calibration?4*e.pix:0;g[f].width+=y+3*e.pix,u.disabled===!0&&(g[f].width=0)}}else{var d=new Array(1),n=new Array(1),g=new Array(1);e.type==="bar"?(d[0]=e.categories,e.yAxis.formatter||(e.yAxis.formatter=(y,c,x)=>y+(x.yAxis.unit||""))):(e.yAxis.formatter||(e.yAxis.formatter=(y,c,x)=>y.toFixed(x.yAxis.tofix)+(x.yAxis.unit||"")),d[0]=Je(i,e,l,t.type,{})),g[0]={position:"left",width:0};var o=e.yAxis.fontSize*e.pix||l.fontSize;n[0]=d[0].map(function(y,c){return y=e.yAxis.formatter(y,c,e),g[0].width=Math.max(g[0].width,H(y,o,a)+5),y}),g[0].width+=3*e.pix,e.yAxis.disabled===!0?(g[0]={position:"left",width:0},e.yAxis.data[0]={disabled:!0}):(e.yAxis.data[0]={disabled:!1,position:"left",max:e.yAxis.max,min:e.yAxis.min,formatter:e.yAxis.formatter},e.type==="bar"&&(e.yAxis.data[0].categories=e.categories,e.yAxis.data[0].type="categories"))}return{rangesFormat:n,ranges:d,yAxisWidth:g}}function $a(i,e,l,a,t){let r=[].concat(l.chartData.yAxisData.ranges),h=l.height-l.area[0]-l.area[2],d=l.area[0],n=[];for(let g=0;g<r.length;g++){let o=Math.max.apply(this,r[g]),f=Math.min.apply(this,r[g]),u=o-(o-f)*(i-d)/h;u=l.yAxis.data&&l.yAxis.data[g].formatter?l.yAxis.data[g].formatter(u,g,l):u.toFixed(0),n.push(String(u))}return n}function Va(i,e){let l,a,t=e.height-e.area[0]-e.area[2];for(let r=0;r<i.length;r++){i[r].yAxisIndex=i[r].yAxisIndex?i[r].yAxisIndex:0;let h=[].concat(e.chartData.yAxisData.ranges[i[r].yAxisIndex]);l=h.pop(),a=h.shift();let d=t*(i[r].value-l)/(a-l);i[r].y=e.height-Math.round(d)-e.area[2]}return i}function U(i,e){e.rotateLock!==!0?(i.translate(e.height,0),i.rotate(90*Math.PI/180)):e._rotate_!==!0&&(i.translate(e.height,0),i.rotate(90*Math.PI/180),e._rotate_=!0)}function Fe(i,e,l,a,t){if(a.beginPath(),t.dataPointShapeType=="hollow"?(a.setStrokeStyle(e),a.setFillStyle(t.background),a.setLineWidth(2*t.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(e),a.setLineWidth(1*t.pix)),l==="diamond")i.forEach(function(r,h){r!==null&&(a.moveTo(r.x,r.y-4.5),a.lineTo(r.x-4.5,r.y),a.lineTo(r.x,r.y****),a.lineTo(r.x****,r.y),a.lineTo(r.x,r.y-4.5))});else if(l==="circle")i.forEach(function(r,h){r!==null&&(a.moveTo(r.x*****t.pix,r.y),a.arc(r.x,r.y,3*t.pix,0,2*Math.PI,!1))});else if(l==="square")i.forEach(function(r,h){r!==null&&(a.moveTo(r.x-3.5,r.y-3.5),a.rect(r.x-3.5,r.y-3.5,7,7))});else if(l==="triangle")i.forEach(function(r,h){r!==null&&(a.moveTo(r.x,r.y-4.5),a.lineTo(r.x-4.5,r.y****),a.lineTo(r.x****,r.y****),a.lineTo(r.x,r.y-4.5))});else if(l==="none")return;a.closePath(),a.fill(),a.stroke()}function ra(i,e,l,a,t,r,h){if(t.tooltip&&!(t.tooltip.group.length>0&&t.tooltip.group.includes(h)==!1)){var d=typeof t.tooltip.index=="number"?t.tooltip.index:t.tooltip.index[t.tooltip.group.indexOf(h)];if(a.beginPath(),r.activeType=="hollow"?(a.setStrokeStyle(e),a.setFillStyle(t.background),a.setLineWidth(2*t.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(e),a.setLineWidth(1*t.pix)),l==="diamond")i.forEach(function(n,g){n!==null&&d==g&&(a.moveTo(n.x,n.y-4.5),a.lineTo(n.x-4.5,n.y),a.lineTo(n.x,n.y****),a.lineTo(n.x****,n.y),a.lineTo(n.x,n.y-4.5))});else if(l==="circle")i.forEach(function(n,g){n!==null&&d==g&&(a.moveTo(n.x*****t.pix,n.y),a.arc(n.x,n.y,3*t.pix,0,2*Math.PI,!1))});else if(l==="square")i.forEach(function(n,g){n!==null&&d==g&&(a.moveTo(n.x-3.5,n.y-3.5),a.rect(n.x-3.5,n.y-3.5,7,7))});else if(l==="triangle")i.forEach(function(n,g){n!==null&&d==g&&(a.moveTo(n.x,n.y-4.5),a.lineTo(n.x-4.5,n.y****),a.lineTo(n.x****,n.y****),a.lineTo(n.x,n.y-4.5))});else if(l==="none")return;a.closePath(),a.fill(),a.stroke()}}function Be(i,e,l,a){var t=i.title.fontSize||e.titleFontSize,r=i.subtitle.fontSize||e.subtitleFontSize,h=i.title.name||"",d=i.subtitle.name||"",n=i.title.color||i.fontColor,g=i.subtitle.color||i.fontColor,o=h?t:0,f=d?r:0,u=5;if(d){var s=H(d,r*i.pix,l),y=a.x-s/2+(i.subtitle.offsetX||0)*i.pix,c=a.y+r*i.pix/2+(i.subtitle.offsetY||0)*i.pix;h&&(c+=(o*i.pix+u)/2),l.beginPath(),l.setFontSize(r*i.pix),l.setFillStyle(g),l.fillText(d,y,c),l.closePath(),l.stroke()}if(h){var x=H(h,t*i.pix,l),v=a.x-x/2+(i.title.offsetX||0),b=a.y+t*i.pix/2+(i.title.offsetY||0)*i.pix;d&&(b-=(f*i.pix+u)/2),l.beginPath(),l.setFontSize(t*i.pix),l.setFillStyle(n),l.fillText(h,v,b),l.closePath(),l.stroke()}}function Se(i,e,l,a,t){var r=e.data,h=e.textOffset?e.textOffset:0;i.forEach(function(d,n){if(d!==null){a.beginPath();var g=e.textSize?e.textSize*t.pix:l.fontSize;a.setFontSize(g),a.setFillStyle(e.textColor||t.fontColor);var o=r[n];typeof r[n]=="object"&&r[n]!==null&&(r[n].constructor.toString().indexOf("Array")>-1?o=r[n][1]:o=r[n].value);var f=e.formatter?e.formatter(o,n,e,t):o;a.setTextAlign("center"),a.fillText(String(f),d.x,d.y-4+h*t.pix),a.closePath(),a.stroke(),a.setTextAlign("left")}})}function Ie(i,e,l,a,t){var r=e.data,h=e.textOffset?e.textOffset:0,d=t.extra.column.labelPosition;i.forEach(function(n,g){if(n!==null){a.beginPath();var o=e.textSize?e.textSize*t.pix:l.fontSize;a.setFontSize(o),a.setFillStyle(e.textColor||t.fontColor);var f=r[g];typeof r[g]=="object"&&r[g]!==null&&(r[g].constructor.toString().indexOf("Array")>-1?f=r[g][1]:f=r[g].value);var u=e.formatter?e.formatter(f,g,e,t):f;a.setTextAlign("center");var s=n.y-4*t.pix+h*t.pix;n.y>e.zeroPoints&&(s=n.y+h*t.pix+o),d=="insideTop"&&(s=n.y+o+h*t.pix,n.y>e.zeroPoints&&(s=n.y-h*t.pix-4*t.pix)),d=="center"&&(s=n.y+h*t.pix+(t.height-t.area[2]-n.y+o)/2,e.zeroPoints<t.height-t.area[2]&&(s=n.y+h*t.pix+(e.zeroPoints-n.y+o)/2),n.y>e.zeroPoints&&(s=n.y-h*t.pix-(n.y-e.zeroPoints-o)/2),t.extra.column.type=="stack"&&(s=n.y+h*t.pix+(n.y0-n.y+o)/2)),d=="bottom"&&(s=t.height-t.area[2]+h*t.pix-4*t.pix,e.zeroPoints<t.height-t.area[2]&&(s=e.zeroPoints+h*t.pix-4*t.pix),n.y>e.zeroPoints&&(s=e.zeroPoints-h*t.pix+o+2*t.pix),t.extra.column.type=="stack"&&(s=n.y0+h*t.pix-4*t.pix)),a.fillText(String(u),n.x,s),a.closePath(),a.stroke(),a.setTextAlign("left")}})}function qa(i,e,l,a,t,r){var h=e.data,d=e.textOffset?e.textOffset:0,n=t.extra.mount.labelPosition;i.forEach(function(g,o){if(g!==null){a.beginPath();var f=e[o].textSize?e[o].textSize*t.pix:l.fontSize;a.setFontSize(f),a.setFillStyle(e[o].textColor||t.fontColor);var u=g.value,s=e[o].formatter?e[o].formatter(u,o,e,t):u;a.setTextAlign("center");var y=g.y-4*t.pix+d*t.pix;g.y>r&&(y=g.y+d*t.pix+f),a.fillText(String(s),g.x,y),a.closePath(),a.stroke(),a.setTextAlign("left")}})}function je(i,e,l,a,t){var r=e.data,h=e.textOffset?e.textOffset:0;i.forEach(function(d,n){if(d!==null){a.beginPath();var g=e.textSize?e.textSize*t.pix:l.fontSize;a.setFontSize(g),a.setFillStyle(e.textColor||t.fontColor);var o=r[n];typeof r[n]=="object"&&r[n]!==null&&(o=r[n].value);var f=e.formatter?e.formatter(o,n,e,t):o;a.setTextAlign("left"),a.fillText(String(f),d.x+4*t.pix,d.y+g/2-3),a.closePath(),a.stroke()}})}function Ja(i,e,l,a,t,r){e-=i.width/2+i.labelOffset*a.pix,e=e<10?10:e;let h;i.endAngle<i.startAngle?h=2+i.endAngle-i.startAngle:h=i.startAngle-i.endAngle;let d=h/i.splitLine.splitNumber,g=(i.endNumber-i.startNumber)/i.splitLine.splitNumber,o=i.startAngle,f=i.startNumber;for(let x=0;x<i.splitLine.splitNumber+1;x++){var u={x:e*Math.cos(o*Math.PI),y:e*Math.sin(o*Math.PI)},s=i.formatter?i.formatter(f,x,a):f;u.x+=l.x-H(s,t.fontSize,r)/2,u.y+=l.y;var y=u.x,c=u.y;r.beginPath(),r.setFontSize(t.fontSize),r.setFillStyle(i.labelColor||a.fontColor),r.fillText(s,y,c+t.fontSize/2),r.closePath(),r.stroke(),o+=d,o>=2&&(o=o%2),f+=g}}function ja(i,e,l,a,t,r){var h=a.extra.radar||{};i.forEach(function(d,n){if(h.labelPointShow===!0&&a.categories[n]!==""){var g={x:e*Math.cos(d),y:e*Math.sin(d)},o=oe(g.x,g.y,l);r.setFillStyle(h.labelPointColor),r.beginPath(),r.arc(o.x,o.y,h.labelPointRadius*a.pix,0,2*Math.PI,!1),r.closePath(),r.fill()}if(h.labelShow===!0){var f={x:(e+t.radarLabelTextMargin*a.pix)*Math.cos(d),y:(e+t.radarLabelTextMargin*a.pix)*Math.sin(d)},u=oe(f.x,f.y,l),s=u.x,y=u.y;fe.approximatelyEqual(f.x,0)?s-=H(a.categories[n]||"",t.fontSize,r)/2:f.x<0&&(s-=H(a.categories[n]||"",t.fontSize,r)),r.beginPath(),r.setFontSize(t.fontSize),r.setFillStyle(h.labelColor||a.fontColor),r.fillText(a.categories[n]||"",s,y+t.fontSize/2),r.closePath(),r.stroke()}})}function la(i,e,l,a,t,r){var h=l.pieChartLinePadding,d=[],n=null,g=i.map(function(o,f){var u=o.formatter?o.formatter(o,f,i,e):fe.toFixed(o._proportion_.toFixed(4)*100)+"%";u=o.labelText?o.labelText:u;var s=2*Math.PI-(o._start_+2*Math.PI*o._proportion_/2);o._rose_proportion_&&(s=2*Math.PI-(o._start_+2*Math.PI*o._rose_proportion_/2));var y=o.color,c=o._radius_;return{arc:s,text:u,color:y,radius:c,textColor:o.textColor,textSize:o.textSize,labelShow:o.labelShow}});for(let o=0;o<g.length;o++){let f=g[o],u=Math.cos(f.arc)*(f.radius+h),s=Math.sin(f.arc)*(f.radius+h),y=Math.cos(f.arc)*f.radius,c=Math.sin(f.arc)*f.radius,x=u>=0?u+l.pieChartTextPadding:u-l.pieChartTextPadding,v=s,b=H(f.text,f.textSize*e.pix||l.fontSize,a),m=v;n&&fe.isSameXCoordinateArea(n.start,{x})&&(x>0?m=Math.min(v,n.start.y):u<0||v>0?m=Math.max(v,n.start.y):m=Math.min(v,n.start.y)),x<0&&(x-=b);let S={lineStart:{x:y,y:c},lineEnd:{x:u,y:s},start:{x,y:m},width:b,height:l.fontSize,text:f.text,color:f.color,textColor:f.textColor,textSize:f.textSize};n=ga(S,n),d.push(n)}for(let o=0;o<d.length;o++){if(g[o].labelShow===!1)continue;let f=d[o],u=oe(f.lineStart.x,f.lineStart.y,r),s=oe(f.lineEnd.x,f.lineEnd.y,r),y=oe(f.start.x,f.start.y,r);a.setLineWidth(1*e.pix),a.setFontSize(f.textSize*e.pix||l.fontSize),a.beginPath(),a.setStrokeStyle(f.color),a.setFillStyle(f.color),a.moveTo(u.x,u.y);let c=f.start.x<0?y.x+f.width:y.x,x=f.start.x<0?y.x-5:y.x+5;a.quadraticCurveTo(s.x,s.y,c,y.y),a.moveTo(u.x,u.y),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(y.x+f.width,y.y),a.arc(c,y.y,2*e.pix,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(f.textSize*e.pix||l.fontSize),a.setFillStyle(f.textColor||e.fontColor),a.fillText(f.text,x,y.y+3),a.closePath(),a.stroke(),a.closePath()}}function Ua(i,e,l,a){var t=e.extra.tooltip||{};t.gridType=t.gridType==null?"solid":t.gridType,t.dashLength=t.dashLength==null?4:t.dashLength;var r=e.area[0],h=e.height-e.area[2];if(t.gridType=="dash"&&a.setLineDash([t.dashLength,t.dashLength]),a.setStrokeStyle(t.gridColor||"#cccccc"),a.setLineWidth(1*e.pix),a.beginPath(),a.moveTo(i,r),a.lineTo(i,h),a.stroke(),a.setLineDash([]),t.xAxisLabel){let d=e.categories[e.tooltip.index];a.setFontSize(l.fontSize);let n=H(d,l.fontSize,a),g=i-.5*n,o=h+2*e.pix;a.beginPath(),a.setFillStyle(M(t.labelBgColor||l.toolTipBackground,t.labelBgOpacity||l.toolTipOpacity)),a.setStrokeStyle(t.labelBgColor||l.toolTipBackground),a.setLineWidth(1*e.pix),a.rect(g-t.boxPadding*e.pix,o,n+2*t.boxPadding*e.pix,l.fontSize+2*t.boxPadding*e.pix),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(l.fontSize),a.setFillStyle(t.labelFontColor||e.fontColor),a.fillText(String(d),g,o+t.boxPadding*e.pix+l.fontSize),a.closePath(),a.stroke()}}function ne(i,e,l){let a=k({},{type:"solid",dashLength:4,data:[]},i.extra.markLine),t=i.area[3],r=i.width-i.area[1],h=Va(a.data,i);for(let d=0;d<h.length;d++){let n=k({},{lineColor:"#DE4A42",showLabel:!1,labelFontSize:13,labelPadding:6,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,labelAlign:"left",labelOffsetX:0,labelOffsetY:0},h[d]);if(a.type=="dash"&&l.setLineDash([a.dashLength,a.dashLength]),l.setStrokeStyle(n.lineColor),l.setLineWidth(1*i.pix),l.beginPath(),l.moveTo(t,n.y),l.lineTo(r,n.y),l.stroke(),l.setLineDash([]),n.showLabel){let g=n.labelFontSize*i.pix,o=n.labelText?n.labelText:n.value;l.setFontSize(g);let u=H(o,g,l)+n.labelPadding*i.pix*2,s=n.labelAlign=="left"?i.area[3]-u:i.width-i.area[1];s+=n.labelOffsetX;let y=n.y-.5*g-n.labelPadding*i.pix;y+=n.labelOffsetY;let c=s+n.labelPadding*i.pix,x=n.y;l.setFillStyle(M(n.labelBgColor,n.labelBgOpacity)),l.setStrokeStyle(n.labelBgColor),l.setLineWidth(1*i.pix),l.beginPath(),l.rect(s,y,u,g+2*n.labelPadding*i.pix),l.closePath(),l.stroke(),l.fill(),l.setFontSize(g),l.setTextAlign("left"),l.setFillStyle(n.labelFontColor),l.fillText(String(o),c,y+g+n.labelPadding*i.pix/2),l.stroke(),l.setTextAlign("left")}}}function Za(i,e,l,a,t){var r=k({},{gridType:"solid",dashLength:4},i.extra.tooltip),h=i.area[3],d=i.width-i.area[1];if(r.gridType=="dash"&&l.setLineDash([r.dashLength,r.dashLength]),l.setStrokeStyle(r.gridColor||"#cccccc"),l.setLineWidth(1*i.pix),l.beginPath(),l.moveTo(h,i.tooltip.offset.y),l.lineTo(d,i.tooltip.offset.y),l.stroke(),l.setLineDash([]),r.yAxisLabel){let n=r.boxPadding*i.pix,g=$a(i.tooltip.offset.y,i.series,i,e,a),o=i.chartData.yAxisData.yAxisWidth,f=i.area[3],u=i.width-i.area[1];for(let s=0;s<g.length;s++){l.setFontSize(r.fontSize*i.pix);let y=H(g[s],r.fontSize*i.pix,l),c,x,v;o[s].position=="left"?(c=f-(y+n*2)-2*i.pix,x=Math.max(c,c+y+n*2)):(c=u+2*i.pix,x=Math.max(c+o[s].width,c+y+n*2)),v=x-c;let b=c+(v-y)/2,m=i.tooltip.offset.y;l.beginPath(),l.setFillStyle(M(r.labelBgColor||e.toolTipBackground,r.labelBgOpacity||e.toolTipOpacity)),l.setStrokeStyle(r.labelBgColor||e.toolTipBackground),l.setLineWidth(1*i.pix),l.rect(c,m-.5*e.fontSize-n,v,e.fontSize+2*n),l.closePath(),l.stroke(),l.fill(),l.beginPath(),l.setFontSize(e.fontSize),l.setFillStyle(r.labelFontColor||i.fontColor),l.fillText(g[s],b,m+.5*e.fontSize),l.closePath(),l.stroke(),o[s].position=="left"?f-=o[s].width+i.yAxis.padding*i.pix:u+=o[s].width+i.yAxis.padding*i.pix}}}function Ka(i,e,l,a,t){var r=k({},{activeBgColor:"#000000",activeBgOpacity:.08,activeWidth:t},e.extra.column);r.activeWidth=r.activeWidth>t?t:r.activeWidth;var h=e.area[0],d=e.height-e.area[2];a.beginPath(),a.setFillStyle(M(r.activeBgColor,r.activeBgOpacity)),a.rect(i-r.activeWidth/2,h,r.activeWidth,d-h),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function Qa(i,e,l,a,t){var r=k({},{activeBgColor:"#000000",activeBgOpacity:.08},e.extra.bar),h=e.area[3],d=e.width-e.area[1];a.beginPath(),a.setFillStyle(M(r.activeBgColor,r.activeBgOpacity)),a.rect(h,i-t/2,d-h,t),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function ei(i,e,l,a,t,r,h){var d=k({},{showBox:!0,showArrow:!0,showCategory:!1,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:!0,legendShape:"auto",splitLine:!0},l.extra.tooltip);d.showCategory==!0&&l.categories&&i.unshift({text:l.categories[l.tooltip.index],color:null});var n=d.fontSize*l.pix,g=d.lineHeight*l.pix,o=d.boxPadding*l.pix,f=n,u=5*l.pix;d.legendShow==!1&&(f=0,u=0);var s=d.showArrow?8*l.pix:0,y=!1;(l.type=="line"||l.type=="mount"||l.type=="area"||l.type=="candle"||l.type=="mix")&&d.splitLine==!0&&Ua(l.tooltip.offset.x,l,a,t),e=k({x:0,y:0},e),e.y-=8*l.pix;var c=i.map(function(m){return H(m.text,n,t)}),x=f+u+4*o+Math.max.apply(null,c),v=2*o+i.length*g;if(d.showBox!=!1){e.x-Math.abs(l._scrollDistance_||0)+s+x>l.width&&(y=!0),v+e.y>l.height&&(e.y=l.height-v),t.beginPath(),t.setFillStyle(M(d.bgColor,d.bgOpacity)),t.setLineWidth(d.borderWidth*l.pix),t.setStrokeStyle(M(d.borderColor,d.borderOpacity));var b=d.borderRadius;y?(x+s>l.width&&(e.x=l.width+Math.abs(l._scrollDistance_||0)+s+(x-l.width)),x>e.x&&(e.x=l.width+Math.abs(l._scrollDistance_||0)+s+(x-l.width)),d.showArrow&&(t.moveTo(e.x,e.y+10*l.pix),t.lineTo(e.x-s,e.y+10*l.pix+5*l.pix)),t.arc(e.x-s-b,e.y+v-b,b,0,Math.PI/2,!1),t.arc(e.x-s-Math.round(x)+b,e.y+v-b,b,Math.PI/2,Math.PI,!1),t.arc(e.x-s-Math.round(x)+b,e.y+b,b,-Math.PI,-Math.PI/2,!1),t.arc(e.x-s-b,e.y+b,b,-Math.PI/2,0,!1),d.showArrow&&(t.lineTo(e.x-s,e.y+10*l.pix-5*l.pix),t.lineTo(e.x,e.y+10*l.pix))):(d.showArrow&&(t.moveTo(e.x,e.y+10*l.pix),t.lineTo(e.x+s,e.y+10*l.pix-5*l.pix)),t.arc(e.x+s+b,e.y+b,b,-Math.PI,-Math.PI/2,!1),t.arc(e.x+s+Math.round(x)-b,e.y+b,b,-Math.PI/2,0,!1),t.arc(e.x+s+Math.round(x)-b,e.y+v-b,b,0,Math.PI/2,!1),t.arc(e.x+s+b,e.y+v-b,b,Math.PI/2,Math.PI,!1),d.showArrow&&(t.lineTo(e.x+s,e.y+10*l.pix+5*l.pix),t.lineTo(e.x,e.y+10*l.pix))),t.closePath(),t.fill(),d.borderWidth>0&&t.stroke(),d.legendShow&&i.forEach(function(m,S){if(m.color!==null){t.beginPath(),t.setFillStyle(m.color);var w=e.x+s+2*o,C=e.y+(g-n)/2+g*S+o+1;switch(y&&(w=e.x-x-s+2*o),m.legendShape){case"line":t.moveTo(w,C+.5*f-2*l.pix),t.fillRect(w,C+.5*f-2*l.pix,f,4*l.pix);break;case"triangle":t.moveTo(w+7.5*l.pix,C+.5*f-5*l.pix),t.lineTo(w*****l.pix,C+.5*f+5*l.pix),t.lineTo(w+12.5*l.pix,C+.5*f+5*l.pix),t.lineTo(w+7.5*l.pix,C+.5*f-5*l.pix);break;case"diamond":t.moveTo(w+7.5*l.pix,C+.5*f-5*l.pix),t.lineTo(w*****l.pix,C+.5*f),t.lineTo(w+7.5*l.pix,C+.5*f+5*l.pix),t.lineTo(w+12.5*l.pix,C+.5*f),t.lineTo(w+7.5*l.pix,C+.5*f-5*l.pix);break;case"circle":t.moveTo(w+7.5*l.pix,C+.5*f),t.arc(w+7.5*l.pix,C+.5*f,5*l.pix,0,2*Math.PI);break;case"rect":t.moveTo(w,C+.5*f-5*l.pix),t.fillRect(w,C+.5*f-5*l.pix,15*l.pix,10*l.pix);break;case"square":t.moveTo(w+2*l.pix,C+.5*f-5*l.pix),t.fillRect(w+2*l.pix,C+.5*f-5*l.pix,10*l.pix,10*l.pix);break;default:t.moveTo(w,C+.5*f-5*l.pix),t.fillRect(w,C+.5*f-5*l.pix,15*l.pix,10*l.pix)}t.closePath(),t.fill()}}),i.forEach(function(m,S){var w=e.x+s+2*o+f+u;y&&(w=e.x-x-s+2*o+f+u);var C=e.y+g*S+(g-n)/2-1+o+n;t.beginPath(),t.setFontSize(n),t.setTextBaseline("normal"),t.setFillStyle(d.fontColor),t.fillText(m.text,w,C),t.closePath(),t.stroke()})}}function ai(i,e,l,a){let t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=e.chartData.xAxisData,h=r.xAxisPoints,d=r.eachSpacing,n=k({},{type:"group",width:d/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0,labelPosition:"outside"},e.extra.column),g=[];a.save();let o=-2,f=h.length+2;return e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===!0&&(a.translate(e._scrollDistance_,0),o=Math.floor(-e._scrollDistance_/d)-2,f=o+e.xAxis.itemCount+4),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&t===1&&Ka(e.tooltip.offset.x,e,l,a,d),n.customColor=ue(n.linearType,n.customColor,i,l),i.forEach(function(u,s){let y,c,x;y=[].concat(e.chartData.yAxisData.ranges[u.index]),c=y.pop(),x=y.shift();let b=(e.height-e.area[0]-e.area[2])*(0-c)/(x-c),m=e.height-Math.round(b)-e.area[2];u.zeroPoints=m;var S=u.data;switch(n.type){case"group":var p=$e(S,c,x,h,d,e,l,m,t),w=_e(S,c,x,h,d,e,l,s,i,t);g.push(w),p=Pe(p,d,i.length,s,l,e);for(let X=0;X<p.length;X++){let _=p[X];if(_!==null&&X>o&&X<f){var C=_.x-_.width/2,P=e.height-_.y-e.area[2];a.beginPath();var A=_.color||u.color,T=_.color||u.color;if(n.linearType!=="none"){var F=a.createLinearGradient(C,_.y,C,m);n.linearType=="opacity"?(F.addColorStop(0,M(A,n.linearOpacity)),F.addColorStop(1,M(A,1))):(F.addColorStop(0,M(n.customColor[u.linearIndex],n.linearOpacity)),F.addColorStop(n.colorStop,M(n.customColor[u.linearIndex],n.linearOpacity)),F.addColorStop(1,M(A,1))),A=F}if(n.barBorderRadius&&n.barBorderRadius.length===4||n.barBorderCircle===!0){let Y=C,$=_.y>m?m:_.y,G=_.width,I=Math.abs(m-_.y);n.barBorderCircle&&(n.barBorderRadius=[G/2,G/2,0,0]),_.y>m&&(n.barBorderRadius=[0,0,G/2,G/2]);let[R,N,O,W]=n.barBorderRadius,z=Math.min(G/2,I/2);R=R>z?z:R,N=N>z?z:N,O=O>z?z:O,W=W>z?z:W,R=R<0?0:R,N=N<0?0:N,O=O<0?0:O,W=W<0?0:W,a.arc(Y+R,$+R,R,-Math.PI,-Math.PI/2),a.arc(Y+G-N,$+N,N,-Math.PI/2,0),a.arc(Y+G-O,$+I-O,O,0,Math.PI/2),a.arc(Y+W,$+I-W,W,Math.PI/2,Math.PI)}else a.moveTo(C,_.y),a.lineTo(C+_.width,_.y),a.lineTo(C+_.width,m),a.lineTo(C,m),a.lineTo(C,_.y),a.setLineWidth(1),a.setStrokeStyle(T);a.setFillStyle(A),a.closePath(),a.fill()}}break;case"stack":var p=_e(S,c,x,h,d,e,l,s,i,t);g.push(p),p=Na(p,d,i.length,s,l,e,i);for(let X=0;X<p.length;X++){let _=p[X];if(_!==null&&X>o&&X<f){a.beginPath();var A=_.color||u.color,C=_.x-_.width/2+1,P=e.height-_.y-e.area[2],L=e.height-_.y0-e.area[2];s>0&&(P-=L),a.setFillStyle(A),a.moveTo(C,_.y),a.fillRect(C,_.y,_.width,P),a.closePath(),a.fill()}}break;case"meter":var p=ie(S,c,x,h,d,e,l,t);g.push(p),p=Ga(p,d,i.length,s,l,e,n.meterBorder);for(let X=0;X<p.length;X++){let _=p[X];if(_!==null&&X>o&&X<f){a.beginPath(),s==0&&n.meterBorder>0&&(a.setStrokeStyle(u.color),a.setLineWidth(n.meterBorder*e.pix)),s==0?a.setFillStyle(n.meterFillColor):a.setFillStyle(_.color||u.color);var C=_.x-_.width/2,P=e.height-_.y-e.area[2];if(n.barBorderRadius&&n.barBorderRadius.length===4||n.barBorderCircle===!0){let G=C,I=_.y,R=_.width,N=m-_.y;n.barBorderCircle&&(n.barBorderRadius=[R/2,R/2,0,0]);let[O,W,z,B]=n.barBorderRadius,J=Math.min(R/2,N/2);O=O>J?J:O,W=W>J?J:W,z=z>J?J:z,B=B>J?J:B,O=O<0?0:O,W=W<0?0:W,z=z<0?0:z,B=B<0?0:B,a.arc(G+O,I+O,O,-Math.PI,-Math.PI/2),a.arc(G+R-W,I+W,W,-Math.PI/2,0),a.arc(G+R-z,I+N-z,z,0,Math.PI/2),a.arc(G+B,I+N-B,B,Math.PI/2,Math.PI),a.fill()}else a.moveTo(C,_.y),a.lineTo(C+_.width,_.y),a.lineTo(C+_.width,m),a.lineTo(C,m),a.lineTo(C,_.y),a.fill();s==0&&n.meterBorder>0&&(a.closePath(),a.stroke())}}break}}),e.dataLabel!==!1&&t===1&&i.forEach(function(u,s){let y,c,x;y=[].concat(e.chartData.yAxisData.ranges[u.index]),c=y.pop(),x=y.shift();var v=u.data;switch(n.type){case"group":var b=$e(v,c,x,h,d,e,l,t);b=Pe(b,d,i.length,s,l,e),Ie(b,u,l,a,e);break;case"stack":var b=_e(v,c,x,h,d,e,l,s,i,t);Ie(b,u,l,a,e);break;case"meter":var b=ie(v,c,x,h,d,e,l,t);Ie(b,u,l,a,e);break}}),a.restore(),{xAxisPoints:h,calPoints:g,eachSpacing:d}}function ii(i,e,l,a){let t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=e.chartData.xAxisData,h=r.xAxisPoints,d=r.eachSpacing,n=k({},{type:"mount",widthRatio:1,borderWidth:1,barBorderCircle:!1,barBorderRadius:[],linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mount);n.widthRatio=n.widthRatio<=0?0:n.widthRatio,n.widthRatio=n.widthRatio>=2?2:n.widthRatio;let g=[];a.save();let o=-2,f=h.length+2;e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===!0&&(a.translate(e._scrollDistance_,0),o=Math.floor(-e._scrollDistance_/d)-2,f=o+e.xAxis.itemCount+4),n.customColor=ue(n.linearType,n.customColor,i,l);let u,s,y;u=[].concat(e.chartData.yAxisData.ranges[0]),s=u.pop(),y=u.shift();let x=(e.height-e.area[0]-e.area[2])*(0-s)/(y-s),v=e.height-Math.round(x)-e.area[2];var b=Ve(i,s,y,h,d,e,n,v,t);switch(n.type){case"bar":for(let A=0;A<b.length;A++){let T=b[A];if(T!==null&&A>o&&A<f){var m=T.x-d*n.widthRatio/2,S=e.height-T.y-e.area[2];a.beginPath();var w=T.color||i[A].color,C=T.color||i[A].color;if(n.linearType!=="none"){var P=a.createLinearGradient(m,T.y,m,v);n.linearType=="opacity"?(P.addColorStop(0,M(w,n.linearOpacity)),P.addColorStop(1,M(w,1))):(P.addColorStop(0,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(n.colorStop,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(1,M(w,1))),w=P}if(n.barBorderRadius&&n.barBorderRadius.length===4||n.barBorderCircle===!0){let F=m,L=T.y>v?v:T.y,p=T.width,X=Math.abs(v-T.y);n.barBorderCircle&&(n.barBorderRadius=[p/2,p/2,0,0]),T.y>v&&(n.barBorderRadius=[0,0,p/2,p/2]);let[_,Y,$,G]=n.barBorderRadius,I=Math.min(p/2,X/2);_=_>I?I:_,Y=Y>I?I:Y,$=$>I?I:$,G=G>I?I:G,_=_<0?0:_,Y=Y<0?0:Y,$=$<0?0:$,G=G<0?0:G,a.arc(F+_,L+_,_,-Math.PI,-Math.PI/2),a.arc(F+p-Y,L+Y,Y,-Math.PI/2,0),a.arc(F+p-$,L+X-$,$,0,Math.PI/2),a.arc(F+G,L+X-G,G,Math.PI/2,Math.PI)}else a.moveTo(m,T.y),a.lineTo(m+T.width,T.y),a.lineTo(m+T.width,v),a.lineTo(m,v),a.lineTo(m,T.y);a.setStrokeStyle(C),a.setFillStyle(w),n.borderWidth>0&&(a.setLineWidth(n.borderWidth*e.pix),a.closePath(),a.stroke()),a.fill()}}break;case"triangle":for(let A=0;A<b.length;A++){let T=b[A];if(T!==null&&A>o&&A<f){var m=T.x-d*n.widthRatio/2,S=e.height-T.y-e.area[2];a.beginPath();var w=T.color||i[A].color,C=T.color||i[A].color;if(n.linearType!=="none"){var P=a.createLinearGradient(m,T.y,m,v);n.linearType=="opacity"?(P.addColorStop(0,M(w,n.linearOpacity)),P.addColorStop(1,M(w,1))):(P.addColorStop(0,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(n.colorStop,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(1,M(w,1))),w=P}a.moveTo(m,v),a.lineTo(T.x,T.y),a.lineTo(m+T.width,v),a.setStrokeStyle(C),a.setFillStyle(w),n.borderWidth>0&&(a.setLineWidth(n.borderWidth*e.pix),a.stroke()),a.fill()}}break;case"mount":for(let A=0;A<b.length;A++){let T=b[A];if(T!==null&&A>o&&A<f){var m=T.x-d*n.widthRatio/2,S=e.height-T.y-e.area[2];a.beginPath();var w=T.color||i[A].color,C=T.color||i[A].color;if(n.linearType!=="none"){var P=a.createLinearGradient(m,T.y,m,v);n.linearType=="opacity"?(P.addColorStop(0,M(w,n.linearOpacity)),P.addColorStop(1,M(w,1))):(P.addColorStop(0,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(n.colorStop,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(1,M(w,1))),w=P}a.moveTo(m,v),a.bezierCurveTo(T.x-T.width/4,v,T.x-T.width/4,T.y,T.x,T.y),a.bezierCurveTo(T.x+T.width/4,T.y,T.x+T.width/4,v,m+T.width,v),a.setStrokeStyle(C),a.setFillStyle(w),n.borderWidth>0&&(a.setLineWidth(n.borderWidth*e.pix),a.stroke()),a.fill()}}break;case"sharp":for(let A=0;A<b.length;A++){let T=b[A];if(T!==null&&A>o&&A<f){var m=T.x-d*n.widthRatio/2,S=e.height-T.y-e.area[2];a.beginPath();var w=T.color||i[A].color,C=T.color||i[A].color;if(n.linearType!=="none"){var P=a.createLinearGradient(m,T.y,m,v);n.linearType=="opacity"?(P.addColorStop(0,M(w,n.linearOpacity)),P.addColorStop(1,M(w,1))):(P.addColorStop(0,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(n.colorStop,M(n.customColor[i[A].linearIndex],n.linearOpacity)),P.addColorStop(1,M(w,1))),w=P}a.moveTo(m,v),a.quadraticCurveTo(T.x-0,v-S/4,T.x,T.y),a.quadraticCurveTo(T.x+0,v-S/4,m+T.width,v),a.setStrokeStyle(C),a.setFillStyle(w),n.borderWidth>0&&(a.setLineWidth(n.borderWidth*e.pix),a.stroke()),a.fill()}}break}if(e.dataLabel!==!1&&t===1){let A,T,F;A=[].concat(e.chartData.yAxisData.ranges[0]),T=A.pop(),F=A.shift();var b=Ve(i,T,F,h,d,e,n,v,t);qa(b,i,l,a,e,v)}return a.restore(),{xAxisPoints:h,calPoints:b,eachSpacing:d}}function ri(i,e,l,a){let t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=[],h=(e.height-e.area[0]-e.area[2])/e.categories.length;for(let f=0;f<e.categories.length;f++)r.push(e.area[0]+h/2+h*f);let d=k({},{type:"group",width:h/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.bar),n=[];a.save();let g=-2,o=r.length+2;return e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&t===1&&Qa(e.tooltip.offset.y,e,l,a,h),d.customColor=ue(d.linearType,d.customColor,i,l),i.forEach(function(f,u){let s,y,c;s=[].concat(e.chartData.xAxisData.ranges),c=s.pop(),y=s.shift();var x=f.data;switch(d.type){case"group":var A=qe(x,y,c,r,h,e,l,t),v=ke(x,y,c,r,h,e,l,u,i,t);n.push(v),A=Ye(A,h,i.length,u,l,e);for(let T=0;T<A.length;T++){let F=A[T];if(F!==null&&T>g&&T<o){var b=e.area[3],m=F.y-F.width/2,S=F.height;a.beginPath();var w=F.color||f.color,C=F.color||f.color;if(d.linearType!=="none"){var P=a.createLinearGradient(b,F.y,F.x,F.y);d.linearType=="opacity"?(P.addColorStop(0,M(w,d.linearOpacity)),P.addColorStop(1,M(w,1))):(P.addColorStop(0,M(d.customColor[f.linearIndex],d.linearOpacity)),P.addColorStop(d.colorStop,M(d.customColor[f.linearIndex],d.linearOpacity)),P.addColorStop(1,M(w,1))),w=P}if(d.barBorderRadius&&d.barBorderRadius.length===4||d.barBorderCircle===!0){let L=b,p=F.width,X=F.y-F.width/2,_=F.height;d.barBorderCircle&&(d.barBorderRadius=[p/2,p/2,0,0]);let[Y,$,G,I]=d.barBorderRadius,R=Math.min(p/2,_/2);Y=Y>R?R:Y,$=$>R?R:$,G=G>R?R:G,I=I>R?R:I,Y=Y<0?0:Y,$=$<0?0:$,G=G<0?0:G,I=I<0?0:I,a.arc(L+I,X+I,I,-Math.PI,-Math.PI/2),a.arc(F.x-Y,X+Y,Y,-Math.PI/2,0),a.arc(F.x-$,X+p-$,$,0,Math.PI/2),a.arc(L+G,X+p-G,G,Math.PI/2,Math.PI)}else a.moveTo(b,m),a.lineTo(F.x,m),a.lineTo(F.x,m+F.width),a.lineTo(b,m+F.width),a.lineTo(b,m),a.setLineWidth(1),a.setStrokeStyle(C);a.setFillStyle(w),a.closePath(),a.fill()}}break;case"stack":var A=ke(x,y,c,r,h,e,l,u,i,t);n.push(A),A=Ha(A,h,i.length,u,l,e,i);for(let T=0;T<A.length;T++){let F=A[T];if(F!==null&&T>g&&T<o){a.beginPath();var w=F.color||f.color,b=F.x0;a.setFillStyle(w),a.moveTo(b,F.y-F.width/2),a.fillRect(b,F.y-F.width/2,F.height,F.width),a.closePath(),a.fill()}}break}}),e.dataLabel!==!1&&t===1&&i.forEach(function(f,u){let s,y,c;s=[].concat(e.chartData.xAxisData.ranges),c=s.pop(),y=s.shift();var x=f.data;switch(d.type){case"group":var v=qe(x,y,c,r,h,e,l,t);v=Ye(v,h,i.length,u,l,e),je(v,f,l,a,e);break;case"stack":var v=ke(x,y,c,r,h,e,l,u,i,t);je(v,f,l,a,e);break}}),{yAxisPoints:r,calPoints:n,eachSpacing:h}}function li(i,e,l,a,t){var r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:1,h=k({},{color:{},average:{}},l.extra.candle);h.color=k({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},h.color),h.average=k({},{show:!1,name:[],day:[],color:a.color},h.average),l.extra.candle=h;let d=l.chartData.xAxisData,n=d.xAxisPoints,g=d.eachSpacing,o=[];t.save();let f=-2,u=n.length+2,s=0,y=l.width+g;return l._scrollDistance_&&l._scrollDistance_!==0&&l.enableScroll===!0&&(t.translate(l._scrollDistance_,0),f=Math.floor(-l._scrollDistance_/g)-2,u=f+l.xAxis.itemCount+4,s=-l._scrollDistance_-g*2+l.area[3],y=s+(l.xAxis.itemCount+4)*g),(h.average.show||e)&&e.forEach(function(c,x){let v,b,m;v=[].concat(l.chartData.yAxisData.ranges[c.index]),b=v.pop(),m=v.shift();var S=c.data,w=ie(S,b,m,n,g,l,a,r),C=be(w,c);for(let A=0;A<C.length;A++){let T=C[A];if(t.beginPath(),t.setStrokeStyle(c.color),t.setLineWidth(1),T.length===1)t.moveTo(T[0].x,T[0].y),t.arc(T[0].x,T[0].y,1,0,2*Math.PI);else{t.moveTo(T[0].x,T[0].y);let F=0;for(let L=0;L<T.length;L++){let p=T[L];if(F==0&&p.x>s&&(t.moveTo(p.x,p.y),F=1),L>0&&p.x>s&&p.x<y){var P=me(T,L-1);t.bezierCurveTo(P.ctrA.x,P.ctrA.y,P.ctrB.x,P.ctrB.y,p.x,p.y)}}t.moveTo(T[0].x,T[0].y)}t.closePath(),t.stroke()}}),i.forEach(function(c,x){let v,b,m;v=[].concat(l.chartData.yAxisData.ranges[c.index]),b=v.pop(),m=v.shift();var S=c.data,w=Xa(S,b,m,n,g,l,a,r);o.push(w);var C=be(w,c);for(let P=0;P<C[0].length;P++)if(P>f&&P<u){let A=C[0][P];t.beginPath(),S[P][1]-S[P][0]>0?(t.setStrokeStyle(h.color.upLine),t.setFillStyle(h.color.upFill),t.setLineWidth(1*l.pix),t.moveTo(A[3].x,A[3].y),t.lineTo(A[1].x,A[1].y),t.lineTo(A[1].x-g/4,A[1].y),t.lineTo(A[0].x-g/4,A[0].y),t.lineTo(A[0].x,A[0].y),t.lineTo(A[2].x,A[2].y),t.lineTo(A[0].x,A[0].y),t.lineTo(A[0].x+g/4,A[0].y),t.lineTo(A[1].x+g/4,A[1].y),t.lineTo(A[1].x,A[1].y),t.moveTo(A[3].x,A[3].y)):(t.setStrokeStyle(h.color.downLine),t.setFillStyle(h.color.downFill),t.setLineWidth(1*l.pix),t.moveTo(A[3].x,A[3].y),t.lineTo(A[0].x,A[0].y),t.lineTo(A[0].x-g/4,A[0].y),t.lineTo(A[1].x-g/4,A[1].y),t.lineTo(A[1].x,A[1].y),t.lineTo(A[2].x,A[2].y),t.lineTo(A[1].x,A[1].y),t.lineTo(A[1].x+g/4,A[1].y),t.lineTo(A[0].x+g/4,A[0].y),t.lineTo(A[0].x,A[0].y),t.moveTo(A[3].x,A[3].y)),t.closePath(),t.fill(),t.stroke()}}),t.restore(),{xAxisPoints:n,calPoints:o,eachSpacing:g}}function ti(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1,activeType:"none"},e.extra.area);let h=e.chartData.xAxisData,d=h.xAxisPoints,n=h.eachSpacing,g=e.height-e.area[2],o=[];a.save();let f=0,u=e.width+n;return e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===!0&&(a.translate(e._scrollDistance_,0),f=-e._scrollDistance_-n*2+e.area[3],u=f+(e.xAxis.itemCount+4)*n),i.forEach(function(s,y){let c,x,v;c=[].concat(e.chartData.yAxisData.ranges[s.index]),x=c.pop(),v=c.shift();let b=s.data,m=ie(b,x,v,d,n,e,l,t);o.push(m);let S=be(m,s);for(let w=0;w<S.length;w++){let C=S[w];if(a.beginPath(),a.setStrokeStyle(M(s.color,r.opacity)),r.gradient){let P=a.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);P.addColorStop("0",M(s.color,r.opacity)),P.addColorStop("1.0",M("#FFFFFF",.1)),a.setFillStyle(P)}else a.setFillStyle(M(s.color,r.opacity));if(a.setLineWidth(r.width*e.pix),C.length>1){let P=C[0],A=C[C.length-1];a.moveTo(P.x,P.y);let T=0;if(r.type==="curve")for(let F=0;F<C.length;F++){let L=C[F];if(T==0&&L.x>f&&(a.moveTo(L.x,L.y),T=1),F>0&&L.x>f&&L.x<u){let p=me(C,F-1);a.bezierCurveTo(p.ctrA.x,p.ctrA.y,p.ctrB.x,p.ctrB.y,L.x,L.y)}}if(r.type==="straight")for(let F=0;F<C.length;F++){let L=C[F];T==0&&L.x>f&&(a.moveTo(L.x,L.y),T=1),F>0&&L.x>f&&L.x<u&&a.lineTo(L.x,L.y)}if(r.type==="step")for(let F=0;F<C.length;F++){let L=C[F];T==0&&L.x>f&&(a.moveTo(L.x,L.y),T=1),F>0&&L.x>f&&L.x<u&&(a.lineTo(L.x,C[F-1].y),a.lineTo(L.x,L.y))}a.lineTo(A.x,g),a.lineTo(P.x,g),a.lineTo(P.x,P.y)}else{let P=C[0];a.moveTo(P.x-n/2,P.y)}if(a.closePath(),a.fill(),r.addLine){if(s.lineType=="dash"){let P=s.dashLength?s.dashLength:8;P*=e.pix,a.setLineDash([P,P])}if(a.beginPath(),a.setStrokeStyle(s.color),a.setLineWidth(r.width*e.pix),C.length===1)a.moveTo(C[0].x,C[0].y);else{a.moveTo(C[0].x,C[0].y);let P=0;if(r.type==="curve")for(let A=0;A<C.length;A++){let T=C[A];if(P==0&&T.x>f&&(a.moveTo(T.x,T.y),P=1),A>0&&T.x>f&&T.x<u){let F=me(C,A-1);a.bezierCurveTo(F.ctrA.x,F.ctrA.y,F.ctrB.x,F.ctrB.y,T.x,T.y)}}if(r.type==="straight")for(let A=0;A<C.length;A++){let T=C[A];P==0&&T.x>f&&(a.moveTo(T.x,T.y),P=1),A>0&&T.x>f&&T.x<u&&a.lineTo(T.x,T.y)}if(r.type==="step")for(let A=0;A<C.length;A++){let T=C[A];P==0&&T.x>f&&(a.moveTo(T.x,T.y),P=1),A>0&&T.x>f&&T.x<u&&(a.lineTo(T.x,C[A-1].y),a.lineTo(T.x,T.y))}a.moveTo(C[0].x,C[0].y)}a.stroke(),a.setLineDash([])}}e.dataPointShape!==!1&&Fe(m,s.color,s.pointShape,a,e),ra(m,s.color,s.pointShape,a,e,r,y)}),e.dataLabel!==!1&&t===1&&i.forEach(function(s,y){let c,x,v;c=[].concat(e.chartData.yAxisData.ranges[s.index]),x=c.pop(),v=c.shift();var b=s.data,m=ie(b,x,v,d,n,e,l,t);Se(m,s,l,a,e)}),a.restore(),{xAxisPoints:d,calPoints:o,eachSpacing:n}}function ni(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{type:"circle"},e.extra.scatter);let h=e.chartData.xAxisData,d=h.xAxisPoints,n=h.eachSpacing;var g=[];a.save();let o=0,f=e.width+n;return e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===!0&&(a.translate(e._scrollDistance_,0),o=-e._scrollDistance_-n*2+e.area[3],f=o+(e.xAxis.itemCount+4)*n),i.forEach(function(u,s){let y,c,x;y=[].concat(e.chartData.yAxisData.ranges[u.index]),c=y.pop(),x=y.shift();var v=u.data,b=ie(v,c,x,d,n,e,l,t);a.beginPath(),a.setStrokeStyle(u.color),a.setFillStyle(u.color),a.setLineWidth(1*e.pix);var m=u.pointShape;if(m==="diamond")b.forEach(function(S,w){S!==null&&(a.moveTo(S.x,S.y-4.5),a.lineTo(S.x-4.5,S.y),a.lineTo(S.x,S.y****),a.lineTo(S.x****,S.y),a.lineTo(S.x,S.y-4.5))});else if(m==="circle")b.forEach(function(S,w){S!==null&&(a.moveTo(S.x*****e.pix,S.y),a.arc(S.x,S.y,3*e.pix,0,2*Math.PI,!1))});else if(m==="square")b.forEach(function(S,w){S!==null&&(a.moveTo(S.x-3.5,S.y-3.5),a.rect(S.x-3.5,S.y-3.5,7,7))});else if(m==="triangle")b.forEach(function(S,w){S!==null&&(a.moveTo(S.x,S.y-4.5),a.lineTo(S.x-4.5,S.y****),a.lineTo(S.x****,S.y****),a.lineTo(S.x,S.y-4.5))});else if(m==="triangle")return;a.closePath(),a.fill(),a.stroke()}),e.dataLabel!==!1&&t===1&&i.forEach(function(u,s){let y,c,x;y=[].concat(e.chartData.yAxisData.ranges[u.index]),c=y.pop(),x=y.shift();var v=u.data,b=ie(v,c,x,d,n,e,l,t);Se(b,u,l,a,e)}),a.restore(),{xAxisPoints:d,calPoints:g,eachSpacing:n}}function hi(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{opacity:1,border:2},e.extra.bubble);let h=e.chartData.xAxisData,d=h.xAxisPoints,n=h.eachSpacing;var g=[];a.save();let o=0,f=e.width+n;return e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===!0&&(a.translate(e._scrollDistance_,0),o=-e._scrollDistance_-n*2+e.area[3],f=o+(e.xAxis.itemCount+4)*n),i.forEach(function(u,s){let y,c,x;y=[].concat(e.chartData.yAxisData.ranges[u.index]),c=y.pop(),x=y.shift();var v=u.data,b=ie(v,c,x,d,n,e,l,t);a.beginPath(),a.setStrokeStyle(u.color),a.setLineWidth(r.border*e.pix),a.setFillStyle(M(u.color,r.opacity)),b.forEach(function(m,S){a.moveTo(m.x+m.r,m.y),a.arc(m.x,m.y,m.r*e.pix,0,2*Math.PI,!1)}),a.closePath(),a.fill(),a.stroke(),e.dataLabel!==!1&&t===1&&b.forEach(function(m,S){a.beginPath();var w=u.textSize*e.pix||l.fontSize;a.setFontSize(w),a.setFillStyle(u.textColor||"#FFFFFF"),a.setTextAlign("center"),a.fillText(String(m.t),m.x,m.y+w/2),a.closePath(),a.stroke(),a.setTextAlign("left")})}),a.restore(),{xAxisPoints:d,calPoints:g,eachSpacing:n}}function di(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{type:"straight",width:2,activeType:"none",linearType:"none",onShadow:!1,animation:"vertical"},e.extra.line);r.width*=e.pix;let h=e.chartData.xAxisData,d=h.xAxisPoints,n=h.eachSpacing;var g=[];a.save();let o=0,f=e.width+n;return e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===!0&&(a.translate(e._scrollDistance_,0),o=-e._scrollDistance_-n*2+e.area[3],f=o+(e.xAxis.itemCount+4)*n),i.forEach(function(u,s){a.beginPath(),a.setStrokeStyle(u.color),a.moveTo(-1e4,-1e4),a.lineTo(-10001,-10001),a.stroke();let y,c,x;y=[].concat(e.chartData.yAxisData.ranges[u.index]),c=y.pop(),x=y.shift();var v=u.data,b=Ya(v,c,x,d,n,e,l,r,t);g.push(b);var m=be(b,u);if(u.lineType=="dash"){let P=u.dashLength?u.dashLength:8;P*=e.pix,a.setLineDash([P,P])}a.beginPath();var S=u.color;if(r.linearType!=="none"&&u.linearColor&&u.linearColor.length>0){for(var w=a.createLinearGradient(e.chartData.xAxisData.startX,e.height/2,e.chartData.xAxisData.endX,e.height/2),C=0;C<u.linearColor.length;C++)w.addColorStop(u.linearColor[C][0],M(u.linearColor[C][1],1));S=w}a.setStrokeStyle(S),r.onShadow==!0&&u.setShadow&&u.setShadow.length>0?a.setShadow(u.setShadow[0],u.setShadow[1],u.setShadow[2],u.setShadow[3]):a.setShadow(0,0,0,"rgba(0,0,0,0)"),a.setLineWidth(r.width),m.forEach(function(P,A){if(P.length===1)a.moveTo(P[0].x,P[0].y);else{a.moveTo(P[0].x,P[0].y);let F=0;if(r.type==="curve")for(let L=0;L<P.length;L++){let p=P[L];if(F==0&&p.x>o&&(a.moveTo(p.x,p.y),F=1),L>0&&p.x>o&&p.x<f){var T=me(P,L-1);a.bezierCurveTo(T.ctrA.x,T.ctrA.y,T.ctrB.x,T.ctrB.y,p.x,p.y)}}if(r.type==="straight")for(let L=0;L<P.length;L++){let p=P[L];F==0&&p.x>o&&(a.moveTo(p.x,p.y),F=1),L>0&&p.x>o&&p.x<f&&a.lineTo(p.x,p.y)}if(r.type==="step")for(let L=0;L<P.length;L++){let p=P[L];F==0&&p.x>o&&(a.moveTo(p.x,p.y),F=1),L>0&&p.x>o&&p.x<f&&(a.lineTo(p.x,P[L-1].y),a.lineTo(p.x,p.y))}a.moveTo(P[0].x,P[0].y)}}),a.stroke(),a.setLineDash([]),e.dataPointShape!==!1&&Fe(b,u.color,u.pointShape,a,e),ra(b,u.color,u.pointShape,a,e,r)}),e.dataLabel!==!1&&t===1&&i.forEach(function(u,s){let y,c,x;y=[].concat(e.chartData.yAxisData.ranges[u.index]),c=y.pop(),x=y.shift();var v=u.data,b=ie(v,c,x,d,n,e,l,t);Se(b,u,l,a,e)}),a.restore(),{xAxisPoints:d,calPoints:g,eachSpacing:n}}function oi(i,e,l,a){let t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=e.chartData.xAxisData,h=r.xAxisPoints,d=r.eachSpacing,n=k({},{width:d/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},e.extra.mix.column),g=k({},{opacity:.2,gradient:!1},e.extra.mix.area),o=k({},{width:2},e.extra.mix.line),f=e.height-e.area[2],u=[];var s=0,y=0;i.forEach(function(m,S){m.type=="column"&&(y+=1)}),a.save();let c=-2,x=h.length+2,v=0,b=e.width+d;if(e._scrollDistance_&&e._scrollDistance_!==0&&e.enableScroll===!0&&(a.translate(e._scrollDistance_,0),c=Math.floor(-e._scrollDistance_/d)-2,x=c+e.xAxis.itemCount+4,v=-e._scrollDistance_-d*2+e.area[3],b=v+(e.xAxis.itemCount+4)*d),n.customColor=ue(n.linearType,n.customColor,i,l),i.forEach(function(m,S){let w,C,P;w=[].concat(e.chartData.yAxisData.ranges[m.index]),C=w.pop(),P=w.shift();var A=m.data,T=ie(A,C,P,h,d,e,l,t);if(u.push(T),m.type=="column"){T=Pe(T,d,y,s,l,e);for(let I=0;I<T.length;I++){let R=T[I];if(R!==null&&I>c&&I<x){var F=R.x-R.width/2,L=e.height-R.y-e.area[2];a.beginPath();var p=R.color||m.color,X=R.color||m.color;if(n.linearType!=="none"){var _=a.createLinearGradient(F,R.y,F,e.height-e.area[2]);n.linearType=="opacity"?(_.addColorStop(0,M(p,n.linearOpacity)),_.addColorStop(1,M(p,1))):(_.addColorStop(0,M(n.customColor[m.linearIndex],n.linearOpacity)),_.addColorStop(n.colorStop,M(n.customColor[m.linearIndex],n.linearOpacity)),_.addColorStop(1,M(p,1))),p=_}if(n.barBorderRadius&&n.barBorderRadius.length===4||n.barBorderCircle){let N=F,O=R.y,W=R.width,z=e.height-e.area[2]-R.y;n.barBorderCircle&&(n.barBorderRadius=[W/2,W/2,0,0]);let[B,J,le,te]=n.barBorderRadius,se=Math.min(W/2,z/2);B=B>se?se:B,J=J>se?se:J,le=le>se?se:le,te=te>se?se:te,B=B<0?0:B,J=J<0?0:J,le=le<0?0:le,te=te<0?0:te,a.arc(N+B,O+B,B,-Math.PI,-Math.PI/2),a.arc(N+W-J,O+J,J,-Math.PI/2,0),a.arc(N+W-le,O+z-le,le,0,Math.PI/2),a.arc(N+te,O+z-te,te,Math.PI/2,Math.PI)}else a.moveTo(F,R.y),a.lineTo(F+R.width,R.y),a.lineTo(F+R.width,e.height-e.area[2]),a.lineTo(F,e.height-e.area[2]),a.lineTo(F,R.y),a.setLineWidth(1),a.setStrokeStyle(X);a.setFillStyle(p),a.closePath(),a.fill()}}s+=1}if(m.type=="area"){let I=be(T,m);for(let R=0;R<I.length;R++){let N=I[R];if(a.beginPath(),a.setStrokeStyle(m.color),a.setStrokeStyle(M(m.color,g.opacity)),g.gradient){let O=a.createLinearGradient(0,e.area[0],0,e.height-e.area[2]);O.addColorStop("0",M(m.color,g.opacity)),O.addColorStop("1.0",M("#FFFFFF",.1)),a.setFillStyle(O)}else a.setFillStyle(M(m.color,g.opacity));if(a.setLineWidth(2*e.pix),N.length>1){var Y=N[0];let O=N[N.length-1];a.moveTo(Y.x,Y.y);let W=0;if(m.style==="curve")for(let z=0;z<N.length;z++){let B=N[z];if(W==0&&B.x>v&&(a.moveTo(B.x,B.y),W=1),z>0&&B.x>v&&B.x<b){var $=me(N,z-1);a.bezierCurveTo($.ctrA.x,$.ctrA.y,$.ctrB.x,$.ctrB.y,B.x,B.y)}}else for(let z=0;z<N.length;z++){let B=N[z];W==0&&B.x>v&&(a.moveTo(B.x,B.y),W=1),z>0&&B.x>v&&B.x<b&&a.lineTo(B.x,B.y)}a.lineTo(O.x,f),a.lineTo(Y.x,f),a.lineTo(Y.x,Y.y)}else{let O=N[0];a.moveTo(O.x-d/2,O.y)}a.closePath(),a.fill()}}if(m.type=="line"){var G=be(T,m);G.forEach(function(I,R){if(m.lineType=="dash"){let O=m.dashLength?m.dashLength:8;O*=e.pix,a.setLineDash([O,O])}if(a.beginPath(),a.setStrokeStyle(m.color),a.setLineWidth(o.width*e.pix),I.length===1)a.moveTo(I[0].x,I[0].y);else{a.moveTo(I[0].x,I[0].y);let O=0;if(m.style=="curve")for(let W=0;W<I.length;W++){let z=I[W];if(O==0&&z.x>v&&(a.moveTo(z.x,z.y),O=1),W>0&&z.x>v&&z.x<b){var N=me(I,W-1);a.bezierCurveTo(N.ctrA.x,N.ctrA.y,N.ctrB.x,N.ctrB.y,z.x,z.y)}}else for(let W=0;W<I.length;W++){let z=I[W];O==0&&z.x>v&&(a.moveTo(z.x,z.y),O=1),W>0&&z.x>v&&z.x<b&&a.lineTo(z.x,z.y)}a.moveTo(I[0].x,I[0].y)}a.stroke(),a.setLineDash([])})}m.type=="point"&&(m.addPoint=!0),m.addPoint==!0&&m.type!=="column"&&Fe(T,m.color,m.pointShape,a,e)}),e.dataLabel!==!1&&t===1){var s=0;i.forEach(function(S,w){let C,P,A;C=[].concat(e.chartData.yAxisData.ranges[S.index]),P=C.pop(),A=C.shift();var T=S.data,F=ie(T,P,A,h,d,e,l,t);S.type!=="column"?Se(F,S,l,a,e):(F=Pe(F,d,y,s,l,e),Se(F,S,l,a,e),s+=1)})}return a.restore(),{xAxisPoints:h,calPoints:u,eachSpacing:d}}function ee(i,e,l,a,t,r){var h=i.extra.tooltip||{};h.horizentalLine&&i.tooltip&&a===1&&(i.type=="line"||i.type=="area"||i.type=="column"||i.type=="mount"||i.type=="candle"||i.type=="mix")&&Za(i,e,l,t,r),l.save(),i._scrollDistance_&&i._scrollDistance_!==0&&i.enableScroll===!0&&l.translate(i._scrollDistance_,0),i.tooltip&&i.tooltip.textList&&i.tooltip.textList.length&&a===1&&ei(i.tooltip.textList,i.tooltip.offset,i,e,l,t,r),l.restore()}function he(i,e,l,a){let t=e.chartData.xAxisData,r=t.xAxisPoints,h=t.startX,d=t.endX,n=t.eachSpacing;var g="center";(e.type=="bar"||e.type=="line"||e.type=="area"||e.type=="scatter"||e.type=="bubble")&&(g=e.xAxis.boundaryGap);var o=e.height-e.area[2],f=e.area[0];if(e.enableScroll&&e.xAxis.scrollShow){var u=e.height-e.area[2]+l.xAxisHeight,s=d-h,y=n*(r.length-1);e.type=="mount"&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),y+=(e.extra.mount.widthRatio-1)*n);var c=s*s/y,x=0;e._scrollDistance_&&(x=-e._scrollDistance_*s/y),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pix),a.setStrokeStyle(e.xAxis.scrollBackgroundColor||"#EFEBEF"),a.moveTo(h,u),a.lineTo(d,u),a.stroke(),a.closePath(),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*e.pix),a.setStrokeStyle(e.xAxis.scrollColor||"#A6A6A6"),a.moveTo(h+x,u),a.lineTo(h+x+c,u),a.stroke(),a.closePath(),a.setLineCap("butt")}if(a.save(),e._scrollDistance_&&e._scrollDistance_!==0&&a.translate(e._scrollDistance_,0),e.xAxis.calibration===!0&&(a.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*e.pix),r.forEach(function(b,m){m>0&&(a.beginPath(),a.moveTo(b-n/2,o),a.lineTo(b-n/2,o+3*e.pix),a.closePath(),a.stroke())})),e.xAxis.disableGrid!==!0&&(a.setStrokeStyle(e.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*e.pix),e.xAxis.gridType=="dash"&&a.setLineDash([e.xAxis.dashLength*e.pix,e.xAxis.dashLength*e.pix]),e.xAxis.gridEval=e.xAxis.gridEval||1,r.forEach(function(b,m){m%e.xAxis.gridEval==0&&(a.beginPath(),a.moveTo(b,o),a.lineTo(b,f),a.stroke())}),a.setLineDash([])),e.xAxis.disabled!==!0){let b=i.length;e.xAxis.labelCount&&(e.xAxis.itemCount?b=Math.ceil(i.length/e.xAxis.itemCount*e.xAxis.labelCount):b=e.xAxis.labelCount,b-=1);let m=Math.ceil(i.length/b),S=[],w=i.length;for(let C=0;C<w;C++)C%m!==0?S.push(""):S.push(i[C]);S[w-1]=i[w-1];var v=e.xAxis.fontSize*e.pix||l.fontSize;l._xAxisTextAngle_===0?S.forEach(function(C,P){var A=e.xAxis.formatter?e.xAxis.formatter(C,P,e):C,T=-H(String(A),v,a)/2;g=="center"&&(T+=n/2);var F=0;e.xAxis.scrollShow&&(F=6*e.pix);var L=e._scrollDistance_||0,p=g=="center"?r[P]+n/2:r[P];p-Math.abs(L)>=e.area[3]-1&&p-Math.abs(L)<=e.width-e.area[1]+1&&(a.beginPath(),a.setFontSize(v),a.setFillStyle(e.xAxis.fontColor||e.fontColor),a.fillText(String(A),r[P]+T,o+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.fontSize)*e.pix/2+e.xAxis.fontSize*e.pix),a.closePath(),a.stroke())}):S.forEach(function(C,P){var A=e.xAxis.formatter?e.xAxis.formatter(C):C,T=e._scrollDistance_||0,F=g=="center"?r[P]+n/2:r[P];if(F-Math.abs(T)>=e.area[3]-1&&F-Math.abs(T)<=e.width-e.area[1]+1){a.save(),a.beginPath(),a.setFontSize(v),a.setFillStyle(e.xAxis.fontColor||e.fontColor);var L=H(String(A),v,a),p=r[P];g=="center"&&(p=r[P]+n/2);var X=0;e.xAxis.scrollShow&&(X=6*e.pix);var _=o+e.xAxis.marginTop*e.pix+v-v*Math.abs(Math.sin(l._xAxisTextAngle_));e.xAxis.rotateAngle<0?(p-=v/2,L=0):(p+=v/2,L=-L),a.translate(p,_),a.rotate(-1*l._xAxisTextAngle_),a.fillText(String(A),L,0),a.closePath(),a.stroke(),a.restore()}})}a.restore(),e.xAxis.title&&(a.beginPath(),a.setFontSize(e.xAxis.titleFontSize*e.pix),a.setFillStyle(e.xAxis.titleFontColor),a.fillText(String(e.xAxis.title),e.width-e.area[1]+e.xAxis.titleOffsetX*e.pix,e.height-e.area[2]+e.xAxis.marginTop*e.pix+(e.xAxis.lineHeight-e.xAxis.titleFontSize)*e.pix/2+(e.xAxis.titleFontSize+e.xAxis.titleOffsetY)*e.pix),a.closePath(),a.stroke()),e.xAxis.axisLine&&(a.beginPath(),a.setStrokeStyle(e.xAxis.axisLineColor),a.setLineWidth(1*e.pix),a.moveTo(h,e.height-e.area[2]),a.lineTo(d,e.height-e.area[2]),a.stroke())}function ge(i,e,l,a){if(e.yAxis.disableGrid===!0)return;let r=(e.height-e.area[0]-e.area[2])/e.yAxis.splitNumber,h=e.area[3],d=e.chartData.xAxisData.xAxisPoints,n=e.chartData.xAxisData.eachSpacing,g=n*(d.length-1);e.type=="mount"&&e.extra&&e.extra.mount&&e.extra.mount.widthRatio&&e.extra.mount.widthRatio>1&&(e.extra.mount.widthRatio>2&&(e.extra.mount.widthRatio=2),g+=(e.extra.mount.widthRatio-1)*n);let o=h+g,f=[],u=1;e.xAxis.axisLine===!1&&(u=0);for(let s=u;s<e.yAxis.splitNumber+1;s++)f.push(e.height-e.area[2]-r*s);a.save(),e._scrollDistance_&&e._scrollDistance_!==0&&a.translate(e._scrollDistance_,0),e.yAxis.gridType=="dash"&&a.setLineDash([e.yAxis.dashLength*e.pix,e.yAxis.dashLength*e.pix]),a.setStrokeStyle(e.yAxis.gridColor),a.setLineWidth(1*e.pix),f.forEach(function(s,y){a.beginPath(),a.moveTo(h,s),a.lineTo(o,s),a.stroke()}),a.setLineDash([]),a.restore()}function de(i,e,l,a){if(e.yAxis.disabled===!0)return;var t=e.height-e.area[0]-e.area[2],r=t/e.yAxis.splitNumber,h=e.area[3],d=e.width-e.area[1],n=e.height-e.area[2];a.beginPath(),a.setFillStyle(e.background),e.enableScroll==!0&&e.xAxis.scrollPosition&&e.xAxis.scrollPosition!=="left"&&a.fillRect(0,0,h,n+2*e.pix),e.enableScroll==!0&&e.xAxis.scrollPosition&&e.xAxis.scrollPosition!=="right"&&a.fillRect(d,0,e.width,n+2*e.pix),a.closePath(),a.stroke();let g=e.area[3],o=e.width-e.area[1],f=e.area[3]+(e.width-e.area[1]-e.area[3])/2;if(e.yAxis.data)for(let s=0;s<e.yAxis.data.length;s++){let y=e.yAxis.data[s];var u=[];if(y.type==="categories")for(let c=0;c<=y.categories.length;c++)u.push(e.area[0]+t/y.categories.length/2+t/y.categories.length*c);else for(let c=0;c<=e.yAxis.splitNumber;c++)u.push(e.area[0]+r*c);if(y.disabled!==!0){let c=e.chartData.yAxisData.rangesFormat[s],x=y.fontSize?y.fontSize*e.pix:l.fontSize,v=e.chartData.yAxisData.yAxisWidth[s],b=y.textAlign||"right";if(c.forEach(function(m,S){var w=u[S];a.beginPath(),a.setFontSize(x),a.setLineWidth(1*e.pix),a.setStrokeStyle(y.axisLineColor||"#cccccc"),a.setFillStyle(y.fontColor||e.fontColor);let C=0,P=4*e.pix;if(v.position=="left"){switch(y.calibration==!0&&(a.moveTo(g,w),a.lineTo(g-3*e.pix,w),P+=3*e.pix),b){case"left":a.setTextAlign("left"),C=g-v.width;break;case"right":a.setTextAlign("right"),C=g-P;break;default:a.setTextAlign("center"),C=g-v.width/2}a.fillText(String(m),C,w+x/2-3*e.pix)}else if(v.position=="right"){switch(y.calibration==!0&&(a.moveTo(o,w),a.lineTo(o+3*e.pix,w),P+=3*e.pix),b){case"left":a.setTextAlign("left"),C=o+P;break;case"right":a.setTextAlign("right"),C=o+v.width;break;default:a.setTextAlign("center"),C=o+v.width/2}a.fillText(String(m),C,w+x/2-3*e.pix)}else if(v.position=="center"){switch(y.calibration==!0&&(a.moveTo(f,w),a.lineTo(f-3*e.pix,w),P+=3*e.pix),b){case"left":a.setTextAlign("left"),C=f-v.width;break;case"right":a.setTextAlign("right"),C=f-P;break;default:a.setTextAlign("center"),C=f-v.width/2}a.fillText(String(m),C,w+x/2-3*e.pix)}a.closePath(),a.stroke(),a.setTextAlign("left")}),y.axisLine!==!1&&(a.beginPath(),a.setStrokeStyle(y.axisLineColor||"#cccccc"),a.setLineWidth(1*e.pix),v.position=="left"?(a.moveTo(g,e.height-e.area[2]),a.lineTo(g,e.area[0])):v.position=="right"?(a.moveTo(o,e.height-e.area[2]),a.lineTo(o,e.area[0])):v.position=="center"&&(a.moveTo(f,e.height-e.area[2]),a.lineTo(f,e.area[0])),a.stroke()),e.yAxis.showTitle){let m=y.titleFontSize*e.pix||l.fontSize,S=y.title;a.beginPath(),a.setFontSize(m),a.setFillStyle(y.titleFontColor||e.fontColor),v.position=="left"?a.fillText(S,g-H(S,m,a)/2+(y.titleOffsetX||0),e.area[0]-(10-(y.titleOffsetY||0))*e.pix):v.position=="right"?a.fillText(S,o-H(S,m,a)/2+(y.titleOffsetX||0),e.area[0]-(10-(y.titleOffsetY||0))*e.pix):v.position=="center"&&a.fillText(S,f-H(S,m,a)/2+(y.titleOffsetX||0),e.area[0]-(10-(y.titleOffsetY||0))*e.pix),a.closePath(),a.stroke()}v.position=="left"?g-=v.width+e.yAxis.padding*e.pix:o+=v.width+e.yAxis.padding*e.pix}}}function Q(i,e,l,a,t){if(e.legend.show===!1)return;let r=t.legendData,h=r.points,d=r.area,n=e.legend.padding*e.pix,g=e.legend.fontSize*e.pix,o=15*e.pix,f=5*e.pix,u=e.legend.itemGap*e.pix,s=Math.max(e.legend.lineHeight*e.pix,g);a.beginPath(),a.setLineWidth(e.legend.borderWidth*e.pix),a.setStrokeStyle(e.legend.borderColor),a.setFillStyle(e.legend.backgroundColor),a.moveTo(d.start.x,d.start.y),a.rect(d.start.x,d.start.y,d.width,d.height),a.closePath(),a.fill(),a.stroke(),h.forEach(function(y,c){let x=0,v=0;x=r.widthArr[c],v=r.heightArr[c];let b=0,m=0;if(e.legend.position=="top"||e.legend.position=="bottom"){switch(e.legend.float){case"left":b=d.start.x+n;break;case"right":b=d.start.x+d.width-x;break;default:b=d.start.x+(d.width-x)/2}m=d.start.y+n+c*s}else c==0?x=0:x=r.widthArr[c-1],b=d.start.x+n+x,m=d.start.y+n+(d.height-v)/2;a.setFontSize(l.fontSize);for(let S=0;S<y.length;S++){let w=y[S];switch(w.area=[0,0,0,0],w.area[0]=b,w.area[1]=m,w.area[3]=m+s,a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(w.show?w.color:e.legend.hiddenColor),a.setFillStyle(w.show?w.color:e.legend.hiddenColor),w.legendShape){case"line":a.moveTo(b,m+.5*s-2*e.pix),a.fillRect(b,m+.5*s-2*e.pix,15*e.pix,4*e.pix);break;case"triangle":a.moveTo(b+7.5*e.pix,m+.5*s-5*e.pix),a.lineTo(b*****e.pix,m+.5*s+5*e.pix),a.lineTo(b+12.5*e.pix,m+.5*s+5*e.pix),a.lineTo(b+7.5*e.pix,m+.5*s-5*e.pix);break;case"diamond":a.moveTo(b+7.5*e.pix,m+.5*s-5*e.pix),a.lineTo(b*****e.pix,m+.5*s),a.lineTo(b+7.5*e.pix,m+.5*s+5*e.pix),a.lineTo(b+12.5*e.pix,m+.5*s),a.lineTo(b+7.5*e.pix,m+.5*s-5*e.pix);break;case"circle":a.moveTo(b+7.5*e.pix,m+.5*s),a.arc(b+7.5*e.pix,m+.5*s,5*e.pix,0,2*Math.PI);break;case"rect":a.moveTo(b,m+.5*s-5*e.pix),a.fillRect(b,m+.5*s-5*e.pix,15*e.pix,10*e.pix);break;case"square":a.moveTo(b+5*e.pix,m+.5*s-5*e.pix),a.fillRect(b+5*e.pix,m+.5*s-5*e.pix,10*e.pix,10*e.pix);break;case"none":break;default:a.moveTo(b,m+.5*s-5*e.pix),a.fillRect(b,m+.5*s-5*e.pix,15*e.pix,10*e.pix)}a.closePath(),a.fill(),a.stroke(),b+=o+f;let C=.5*s+.5*g-2,P=w.legendText?w.legendText:w.name;a.beginPath(),a.setFontSize(g),a.setFillStyle(w.show?e.legend.fontColor:e.legend.hiddenColor),a.fillText(P,b,m+C),a.closePath(),a.stroke(),e.legend.position=="top"||e.legend.position=="bottom"?(b+=H(P,g,a)+u,w.area[2]=b):(w.area[2]=b+H(P,g,a)+u,b-=o+f,m+=s)}})}function Ue(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},e.type=="pie"?e.extra.pie:e.extra.ring),h={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2};l.pieChartLinePadding==0&&(l.pieChartLinePadding=r.activeRadius*e.pix);var d=Math.min((e.width-e.area[1]-e.area[3])/2-l.pieChartLinePadding-l.pieChartTextPadding-l._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-l.pieChartLinePadding-l.pieChartTextPadding);d=d<10?10:d,r.customRadius>0&&(d=r.customRadius*e.pix),i=We(i,d,t);var n=r.activeRadius*e.pix;if(r.customColor=ue(r.linearType,r.customColor,i,l),i=i.map(function(o){return o._start_+=r.offsetAngle*Math.PI/180,o}),i.forEach(function(o,f){e.tooltip&&e.tooltip.index==f&&(a.beginPath(),a.setFillStyle(M(o.color,r.activeOpacity||.5)),a.moveTo(h.x,h.y),a.arc(h.x,h.y,o._radius_+n,o._start_,o._start_+2*o._proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.lineJoin="round",a.setStrokeStyle(r.borderColor);var u=o.color;if(r.linearType=="custom"){var s;a.createCircularGradient?s=a.createCircularGradient(h.x,h.y,o._radius_):s=a.createRadialGradient(h.x,h.y,0,h.x,h.y,o._radius_),s.addColorStop(0,M(r.customColor[o.linearIndex],1)),s.addColorStop(1,M(o.color,1)),u=s}a.setFillStyle(u),a.moveTo(h.x,h.y),a.arc(h.x,h.y,o._radius_,o._start_,o._start_+2*o._proportion_*Math.PI),a.closePath(),a.fill(),r.border==!0&&a.stroke()}),e.type==="ring"){var g=d*.6;typeof r.ringWidth=="number"&&r.ringWidth>0&&(g=Math.max(0,d-r.ringWidth*e.pix)),a.beginPath(),a.setFillStyle(r.centerColor),a.moveTo(h.x,h.y),a.arc(h.x,h.y,g,0,2*Math.PI),a.closePath(),a.fill()}return e.dataLabel!==!1&&t===1&&la(i,e,l,a,d,h),t===1&&e.type==="ring"&&Be(e,l,a,h),{center:h,radius:d,series:i}}function fi(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},e.extra.rose);l.pieChartLinePadding==0&&(l.pieChartLinePadding=r.activeRadius*e.pix);var h={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},d=Math.min((e.width-e.area[1]-e.area[3])/2-l.pieChartLinePadding-l.pieChartTextPadding-l._pieTextMaxLength_,(e.height-e.area[0]-e.area[2])/2-l.pieChartLinePadding-l.pieChartTextPadding);d=d<10?10:d;var n=r.minRadius||d*.5;d<n&&(d=n+10),i=ia(i,r.type,n,d,t);var g=r.activeRadius*e.pix;return r.customColor=ue(r.linearType,r.customColor,i,l),i=i.map(function(o){return o._start_+=(r.offsetAngle||0)*Math.PI/180,o}),i.forEach(function(o,f){e.tooltip&&e.tooltip.index==f&&(a.beginPath(),a.setFillStyle(M(o.color,r.activeOpacity||.5)),a.moveTo(h.x,h.y),a.arc(h.x,h.y,g+o._radius_,o._start_,o._start_+2*o._rose_proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.lineJoin="round",a.setStrokeStyle(r.borderColor);var u=o.color;if(r.linearType=="custom"){var s;a.createCircularGradient?s=a.createCircularGradient(h.x,h.y,o._radius_):s=a.createRadialGradient(h.x,h.y,0,h.x,h.y,o._radius_),s.addColorStop(0,M(r.customColor[o.linearIndex],1)),s.addColorStop(1,M(o.color,1)),u=s}a.setFillStyle(u),a.moveTo(h.x,h.y),a.arc(h.x,h.y,o._radius_,o._start_,o._start_+2*o._rose_proportion_*Math.PI),a.closePath(),a.fill(),r.border==!0&&a.stroke()}),e.dataLabel!==!1&&t===1&&la(i,e,l,a,d,h),{center:h,radius:d,series:i}}function ui(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{startAngle:.75,endAngle:.25,type:"default",direction:"cw",lineCap:"round",width:12,gap:2,linearType:"none",customColor:[]},e.extra.arcbar);i=Ra(i,r,t);var h;r.centerX||r.centerY?h={x:r.centerX?r.centerX:e.width/2,y:r.centerY?r.centerY:e.height/2}:h={x:e.width/2,y:e.height/2};var d;r.radius?d=r.radius:(d=Math.min(h.x,h.y),d-=5*e.pix,d-=r.width/2),d=d<10?10:d,r.customColor=ue(r.linearType,r.customColor,i,l);for(let o=0;o<i.length;o++){let f=i[o];a.setLineWidth(r.width*e.pix),a.setStrokeStyle(r.backgroundColor||"#E9E9E9"),a.setLineCap(r.lineCap),a.beginPath(),r.type=="default"?a.arc(h.x,h.y,d-(r.width*e.pix+r.gap*e.pix)*o,r.startAngle*Math.PI,r.endAngle*Math.PI,r.direction=="ccw"):a.arc(h.x,h.y,d-(r.width*e.pix+r.gap*e.pix)*o,0,2*Math.PI,r.direction=="ccw"),a.stroke();var n=f.color;if(r.linearType=="custom"){var g=a.createLinearGradient(h.x-d,h.y,h.x+d,h.y);g.addColorStop(1,M(r.customColor[f.linearIndex],1)),g.addColorStop(0,M(f.color,1)),n=g}a.setLineWidth(r.width*e.pix),a.setStrokeStyle(n),a.setLineCap(r.lineCap),a.beginPath(),a.arc(h.x,h.y,d-(r.width*e.pix+r.gap*e.pix)*o,r.startAngle*Math.PI,f._proportion_*Math.PI,r.direction=="ccw"),a.stroke()}return Be(e,l,a,h),{center:h,radius:d,series:i}}function si(i,e,l,a,t){var r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:1,h=k({},{type:"default",startAngle:.75,endAngle:.25,width:15,labelOffset:13,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},l.extra.gauge);h.oldAngle==null&&(h.oldAngle=h.startAngle),h.oldData==null&&(h.oldData=0),i=Wa(i,h.startAngle,h.endAngle);var d={x:l.width/2,y:l.height/2},n=Math.min(d.x,d.y);n-=5*l.pix,n-=h.width/2,n=n<10?10:n;var g=n-h.width,o=0;if(h.type=="progress"){var f=n-h.width*3;t.beginPath();let u=t.createLinearGradient(d.x,d.y-f,d.x,d.y+f);u.addColorStop("0",M(e[0].color,.3)),u.addColorStop("1.0",M("#FFFFFF",.1)),t.setFillStyle(u),t.arc(d.x,d.y,f,0,2*Math.PI,!1),t.fill(),t.setLineWidth(h.width),t.setStrokeStyle(M(e[0].color,.3)),t.setLineCap("round"),t.beginPath(),t.arc(d.x,d.y,g,h.startAngle*Math.PI,h.endAngle*Math.PI,!1),t.stroke(),h.endAngle<h.startAngle?o=2+h.endAngle-h.startAngle:o=h.startAngle-h.endAngle;let s=o/h.splitLine.splitNumber,y=o/h.splitLine.splitNumber/h.splitLine.childNumber,c=-n-h.width*.5-h.splitLine.fixRadius,x=-n-h.width-h.splitLine.fixRadius+h.splitLine.width;t.save(),t.translate(d.x,d.y),t.rotate((h.startAngle-1)*Math.PI);let v=h.splitLine.splitNumber*h.splitLine.childNumber+1,b=e[0].data*r;for(let w=0;w<v;w++)t.beginPath(),b>w/v?t.setStrokeStyle(M(e[0].color,1)):t.setStrokeStyle(M(e[0].color,.3)),t.setLineWidth(3*l.pix),t.moveTo(c,0),t.lineTo(x,0),t.stroke(),t.rotate(y*Math.PI);t.restore(),e=Oa(e,h,r),t.setLineWidth(h.width),t.setStrokeStyle(e[0].color),t.setLineCap("round"),t.beginPath(),t.arc(d.x,d.y,g,h.startAngle*Math.PI,e[0]._proportion_*Math.PI,!1),t.stroke();let m=n-h.width*2.5;t.save(),t.translate(d.x,d.y),t.rotate((e[0]._proportion_-1)*Math.PI),t.beginPath(),t.setLineWidth(h.width/3);let S=t.createLinearGradient(0,-m*.6,0,m*.6);S.addColorStop("0",M("#FFFFFF",0)),S.addColorStop("0.5",M(e[0].color,1)),S.addColorStop("1.0",M("#FFFFFF",0)),t.setStrokeStyle(S),t.arc(0,0,m,.85*Math.PI,1.15*Math.PI,!1),t.stroke(),t.beginPath(),t.setLineWidth(1),t.setStrokeStyle(e[0].color),t.setFillStyle(e[0].color),t.moveTo(-m-h.width/3/2,-4),t.lineTo(-m-h.width/3/2-4,0),t.lineTo(-m-h.width/3/2,4),t.lineTo(-m-h.width/3/2,-4),t.stroke(),t.fill(),t.restore()}else{t.setLineWidth(h.width),t.setLineCap("butt");for(let v=0;v<i.length;v++){let b=i[v];t.beginPath(),t.setStrokeStyle(b.color),t.arc(d.x,d.y,n,b._startAngle_*Math.PI,b._endAngle_*Math.PI,!1),t.stroke()}t.save(),h.endAngle<h.startAngle?o=2+h.endAngle-h.startAngle:o=h.startAngle-h.endAngle;let u=o/h.splitLine.splitNumber,s=o/h.splitLine.splitNumber/h.splitLine.childNumber,y=-n-h.width*.5-h.splitLine.fixRadius,c=-n-h.width*.5-h.splitLine.fixRadius+h.splitLine.width,x=-n-h.width*.5-h.splitLine.fixRadius+h.splitLine.childWidth;t.translate(d.x,d.y),t.rotate((h.startAngle-1)*Math.PI);for(let v=0;v<h.splitLine.splitNumber+1;v++)t.beginPath(),t.setStrokeStyle(h.splitLine.color),t.setLineWidth(2*l.pix),t.moveTo(y,0),t.lineTo(c,0),t.stroke(),t.rotate(u*Math.PI);t.restore(),t.save(),t.translate(d.x,d.y),t.rotate((h.startAngle-1)*Math.PI);for(let v=0;v<h.splitLine.splitNumber*h.splitLine.childNumber+1;v++)t.beginPath(),t.setStrokeStyle(h.splitLine.color),t.setLineWidth(1*l.pix),t.moveTo(y,0),t.lineTo(x,0),t.stroke(),t.rotate(s*Math.PI);t.restore(),e=Ea(e,i,h,r);for(let v=0;v<e.length;v++){let b=e[v];t.save(),t.translate(d.x,d.y),t.rotate((b._proportion_-1)*Math.PI),t.beginPath(),t.setFillStyle(b.color),t.moveTo(h.pointer.width,0),t.lineTo(0,-h.pointer.width/2),t.lineTo(-g,0),t.lineTo(0,h.pointer.width/2),t.lineTo(h.pointer.width,0),t.closePath(),t.fill(),t.beginPath(),t.setFillStyle("#FFFFFF"),t.arc(0,0,h.pointer.width/6,0,2*Math.PI,!1),t.fill(),t.restore()}l.dataLabel!==!1&&Ja(h,n,d,l,a,t)}return Be(l,a,t,d),r===1&&l.type==="gauge"&&(l.extra.gauge.oldAngle=e[0]._proportion_,l.extra.gauge.oldData=e[0].data),{center:d,radius:n,innerRadius:g,categories:i,totalAngle:o}}function gi(i,e,l,a){var t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{gridColor:"#cccccc",gridType:"radar",gridEval:1,axisLabel:!1,axisLabelTofix:0,labelShow:!0,labelColor:"#666666",labelPointShow:!1,labelPointRadius:3,labelPointColor:"#cccccc",opacity:.2,gridCount:3,border:!1,borderWidth:2,linearType:"none",customColor:[]},e.extra.radar),h=va(e.categories.length),d={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.area[0]+(e.height-e.area[0]-e.area[2])/2},n=(e.width-e.area[1]-e.area[3])/2,g=(e.height-e.area[0]-e.area[2])/2,o=Math.min(n-(xa(e.categories,l.fontSize,a)+l.radarLabelTextMargin),g-l.radarLabelTextMargin);o-=l.radarLabelTextMargin*e.pix,o=o<10?10:o,o=r.radius?r.radius:o,a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(r.gridColor),h.forEach(function(y,c){var x=oe(o*Math.cos(y),o*Math.sin(y),d);a.moveTo(d.x,d.y),c%r.gridEval==0&&a.lineTo(x.x,x.y)}),a.stroke(),a.closePath();for(var f=function(c){var x={};if(a.beginPath(),a.setLineWidth(1*e.pix),a.setStrokeStyle(r.gridColor),r.gridType=="radar")h.forEach(function(b,m){var S=oe(o/r.gridCount*c*Math.cos(b),o/r.gridCount*c*Math.sin(b),d);m===0?(x=S,a.moveTo(S.x,S.y)):a.lineTo(S.x,S.y)}),a.lineTo(x.x,x.y);else{var v=oe(o/r.gridCount*c*Math.cos(1.5),o/r.gridCount*c*Math.sin(1.5),d);a.arc(d.x,d.y,d.y-v.y,0,2*Math.PI,!1)}a.stroke(),a.closePath()},u=1;u<=r.gridCount;u++)f(u);r.customColor=ue(r.linearType,r.customColor,i,l);var s=Ia(h,d,o,i,e,t);if(s.forEach(function(y,c){a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(y.color);var x=M(y.color,r.opacity);if(r.linearType=="custom"){var v;a.createCircularGradient?v=a.createCircularGradient(d.x,d.y,o):v=a.createRadialGradient(d.x,d.y,0,d.x,d.y,o),v.addColorStop(0,M(r.customColor[i[c].linearIndex],r.opacity)),v.addColorStop(1,M(y.color,r.opacity)),x=v}if(a.setFillStyle(x),y.data.forEach(function(m,S){S===0?a.moveTo(m.position.x,m.position.y):a.lineTo(m.position.x,m.position.y)}),a.closePath(),a.fill(),r.border===!0&&a.stroke(),a.closePath(),e.dataPointShape!==!1){var b=y.data.map(function(m){return m.position});Fe(b,y.color,y.pointShape,a,e)}}),r.axisLabel===!0){let y=Math.max(r.max,Math.max.apply(null,Ce(i))),c=o/r.gridCount,x=e.fontSize*e.pix;a.setFontSize(x),a.setFillStyle(e.fontColor),a.setTextAlign("left");for(var u=0;u<r.gridCount+1;u++){let b=u*y/r.gridCount;b=b.toFixed(r.axisLabelTofix),a.fillText(String(b),d.x+3*e.pix,d.y-u*c+x/2)}}return ja(h,o,d,e,l,a),e.dataLabel!==!1&&t===1&&(s.forEach(function(y,c){a.beginPath();var x=y.textSize*e.pix||l.fontSize;a.setFontSize(x),a.setFillStyle(y.textColor||e.fontColor),y.data.forEach(function(v,b){Math.abs(v.position.x-d.x)<2?v.position.y<d.y?(a.setTextAlign("center"),a.fillText(v.value,v.position.x,v.position.y-4)):(a.setTextAlign("center"),a.fillText(v.value,v.position.x,v.position.y+x+2)):v.position.x<d.x?(a.setTextAlign("right"),a.fillText(v.value,v.position.x-4,v.position.y+x/2-2)):(a.setTextAlign("left"),a.fillText(v.value,v.position.x+4,v.position.y+x/2-2))}),a.closePath(),a.stroke()}),a.setTextAlign("left")),{center:d,radius:o,angleList:h}}function ve(i,e){var l=Array(2),a=i*2003750834e-2/180,t=Math.log(Math.tan((90+e)*Math.PI/360))/(Math.PI/180);return t=t*2003750834e-2/180,l[0]=a,l[1]=t,l}function yi(i){var e={},l;e.xMin=180,e.xMax=0,e.yMin=90,e.yMax=0;for(var a=0;a<i.length;a++)for(var t=i[a].geometry.coordinates,r=0;r<t.length;r++){l=t[r],l.length==1&&(l=l[0]);for(var h=0;h<l.length;h++){var d=l[h][0],n=l[h][1],g={x:d,y:n};e.xMin=e.xMin<g.x?e.xMin:g.x,e.xMax=e.xMax>g.x?e.xMax:g.x,e.yMin=e.yMin<g.y?e.yMin:g.y,e.yMax=e.yMax>g.y?e.yMax:g.y}}return e}function Ze(i,e,l,a,t,r){return{x:(e-l.xMin)*a+t,y:(l.yMax-i)*a+r}}function ci(i,e,l,a,t,r){return{x:(e-t)/a+l.xMin,y:l.yMax-(i-r)/a}}function xi(i,e,l){return!(e[1]==l[1]||e[1]>i[1]&&l[1]>i[1]||e[1]<i[1]&&l[1]<i[1]||e[1]==i[1]&&l[1]>i[1]||l[1]==i[1]&&e[1]>i[1]||e[0]<i[0]&&l[1]<i[1]||l[0]-(l[0]-e[0])*(l[1]-i[1])/(l[1]-e[1])<i[0])}function vi(i,e,l){let a=0;for(let t=0;t<e.length;t++){let r=e[t][0];e.length==1&&(r=e[t][0]);for(let h=0;h<r.length-1;h++){let d=r[h],n=r[h+1];l&&(d=ve(r[h][0],r[h][1]),n=ve(r[h+1][0],r[h+1][1])),xi(i,d,n)&&(a+=1)}}return a%2==1}function mi(i,e,l,a){var t=k({},{border:!0,mercator:!1,borderWidth:1,active:!0,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},e.extra.map),r,h,d=i,n=yi(d);if(t.mercator){var g=ve(n.xMax,n.yMax),o=ve(n.xMin,n.yMin);n.xMax=g[0],n.yMax=g[1],n.xMin=o[0],n.yMin=o[1]}for(var f=e.width/Math.abs(n.xMax-n.xMin),u=e.height/Math.abs(n.yMax-n.yMin),s=f<u?f:u,y=e.width/2-Math.abs(n.xMax-n.xMin)/2*s,c=e.height/2-Math.abs(n.yMax-n.yMin)/2*s,x=0;x<d.length;x++){a.beginPath(),a.setLineWidth(t.borderWidth*e.pix),a.setStrokeStyle(t.borderColor),a.setFillStyle(M(i[x].color,i[x].fillOpacity||t.fillOpacity)),t.active==!0&&e.tooltip&&e.tooltip.index==x&&(a.setStrokeStyle(t.activeBorderColor),a.setFillStyle(M(t.activeFillColor,t.activeFillOpacity)));for(var v=d[x].geometry.coordinates,b=0;b<v.length;b++){r=v[b],r.length==1&&(r=r[0]);for(var m=0;m<r.length;m++){var S=Array(2);t.mercator?S=ve(r[m][0],r[m][1]):S=r[m],h=Ze(S[1],S[0],n,s,y,c),m===0?(a.beginPath(),a.moveTo(h.x,h.y)):a.lineTo(h.x,h.y)}a.fill(),t.border==!0&&a.stroke()}}if(e.dataLabel==!0)for(var x=0;x<d.length;x++){var w=d[x].properties.centroid;if(w){t.mercator&&(w=ve(d[x].properties.centroid[0],d[x].properties.centroid[1])),h=Ze(w[1],w[0],n,s,y,c);let P=d[x].textSize*e.pix||l.fontSize,A=d[x].textColor||e.fontColor;t.active&&t.activeTextColor&&e.tooltip&&e.tooltip.index==x&&(A=t.activeTextColor);let T=d[x].properties.name;a.beginPath(),a.setFontSize(P),a.setFillStyle(A),a.fillText(T,h.x-H(T,P,a)/2,h.y+P/2),a.closePath(),a.stroke()}}e.chartData.mapData={bounds:n,scale:s,xoffset:y,yoffset:c,mercator:t.mercator},ee(e,l,a,1),a.draw()}function xe(i,e,l){l=l==0?1:l;for(var a=[],t=0;t<l;t++)a[t]=Math.random();return Math.floor(a.reduce(function(r,h){return r+h})/l*(e-i))+i}function ze(i,e,l,a){var t=!1;for(let r=0;r<e.length;r++)if(e[r].area)if(i[3]<e[r].area[1]||i[0]>e[r].area[2]||i[1]>e[r].area[3]||i[2]<e[r].area[0])if(i[0]<0||i[1]<0||i[2]>l||i[3]>a){t=!0;break}else t=!1;else{t=!0;break}return t}function bi(i,e,l){let a=i.series;switch(e){case"normal":for(let r=0;r<a.length;r++){let h=a[r].name,d=a[r].textSize*i.pix,n=H(h,d,l),g,o,f,u=0;for(;u++,g=xe(-i.width/2,i.width/2,5)-n/2,o=xe(-i.height/2,i.height/2,5)+d/2,f=[g-5+i.width/2,o-5-d+i.height/2,g+n+5+i.width/2,o+5+i.height/2],!!ze(f,a,i.width,i.height);)if(u==1e3){f=[-100,-100,-100,-100];break}a[r].area=f}break;case"vertical":let t=function(){return Math.random()>.7};for(let r=0;r<a.length;r++){let h=a[r].name,d=a[r].textSize*i.pix,n=H(h,d,l),g=t(),o,f,u,s,y=0;for(;;){y++;let c;if(g?(o=xe(-i.width/2,i.width/2,5)-n/2,f=xe(-i.height/2,i.height/2,5)+d/2,u=[f-5-n+i.width/2,-o-5+i.height/2,f+5+i.width/2,-o+d+5+i.height/2],s=[i.width-(i.width/2-i.height/2)-(-o+d+5+i.height/2)-5,i.height/2-i.width/2+(f-5-n+i.width/2)-5,i.width-(i.width/2-i.height/2)-(-o+d+5+i.height/2)+d,i.height/2-i.width/2+(f-5-n+i.width/2)+n+5],c=ze(s,a,i.height,i.width)):(o=xe(-i.width/2,i.width/2,5)-n/2,f=xe(-i.height/2,i.height/2,5)+d/2,u=[o-5+i.width/2,f-5-d+i.height/2,o+n+5+i.width/2,f+5+i.height/2],c=ze(u,a,i.width,i.height)),!c)break;if(y==1e3){u=[-1e3,-1e3,-1e3,-1e3];break}}g?(a[r].area=s,a[r].areav=u):a[r].area=u,a[r].rotate=g}break}return a}function Ai(i,e,l,a){let t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{type:"normal",autoColors:!0},e.extra.word);e.chartData.wordCloudData||(e.chartData.wordCloudData=bi(e,r.type,a)),a.beginPath(),a.setFillStyle(e.background),a.rect(0,0,e.width,e.height),a.fill(),a.save();let h=e.chartData.wordCloudData;a.translate(e.width/2,e.height/2);for(let d=0;d<h.length;d++){a.save(),h[d].rotate&&a.rotate(90*Math.PI/180);let n=h[d].name,g=h[d].textSize*e.pix,o=H(n,g,a);a.beginPath(),a.setStrokeStyle(h[d].color),a.setFillStyle(h[d].color),a.setFontSize(g),h[d].rotate?h[d].areav[0]>0&&(e.tooltip&&e.tooltip.index==d?a.strokeText(n,(h[d].areav[0]+5-e.width/2)*t-o*(1-t)/2,(h[d].areav[1]+5+g-e.height/2)*t):a.fillText(n,(h[d].areav[0]+5-e.width/2)*t-o*(1-t)/2,(h[d].areav[1]+5+g-e.height/2)*t)):h[d].area[0]>0&&(e.tooltip&&e.tooltip.index==d?a.strokeText(n,(h[d].area[0]+5-e.width/2)*t-o*(1-t)/2,(h[d].area[1]+5+g-e.height/2)*t):a.fillText(n,(h[d].area[0]+5-e.width/2)*t-o*(1-t)/2,(h[d].area[1]+5+g-e.height/2)*t)),a.stroke(),a.restore()}a.restore()}function Ti(i,e,l,a){let t=arguments.length>4&&arguments[4]!==void 0?arguments[4]:1,r=k({},{type:"funnel",activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,minSize:0,labelAlign:"right",linearType:"none",customColor:[]},e.extra.funnel),h=(e.height-e.area[0]-e.area[2])/i.length,d={x:e.area[3]+(e.width-e.area[1]-e.area[3])/2,y:e.height-e.area[2]},n=r.activeWidth*e.pix,g=Math.min((e.width-e.area[1]-e.area[3])/2-n,(e.height-e.area[0]-e.area[2])/2-n),o=za(i,g,r,h,t);if(a.save(),a.translate(d.x,d.y),r.customColor=ue(r.linearType,r.customColor,i,l),r.type=="pyramid")for(let s=0;s<o.length;s++){if(s==o.length-1){e.tooltip&&e.tooltip.index==s&&(a.beginPath(),a.setFillStyle(M(o[s].color,r.activeOpacity)),a.moveTo(-n,-h),a.lineTo(-o[s].radius-n,0),a.lineTo(o[s].radius+n,0),a.lineTo(n,-h),a.lineTo(-n,-h),a.closePath(),a.fill()),o[s].funnelArea=[d.x-o[s].radius,d.y-h*(s+1),d.x+o[s].radius,d.y-h*s],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor);var f=M(o[s].color,r.fillOpacity);if(r.linearType=="custom"){var u=a.createLinearGradient(o[s].radius,-h,-o[s].radius,-h);u.addColorStop(0,M(o[s].color,r.fillOpacity)),u.addColorStop(.5,M(r.customColor[o[s].linearIndex],r.fillOpacity)),u.addColorStop(1,M(o[s].color,r.fillOpacity)),f=u}a.setFillStyle(f),a.moveTo(0,-h),a.lineTo(-o[s].radius,0),a.lineTo(o[s].radius,0),a.lineTo(0,-h),a.closePath(),a.fill(),r.border==!0&&a.stroke()}else{e.tooltip&&e.tooltip.index==s&&(a.beginPath(),a.setFillStyle(M(o[s].color,r.activeOpacity)),a.moveTo(0,0),a.lineTo(-o[s].radius-n,0),a.lineTo(-o[s+1].radius-n,-h),a.lineTo(o[s+1].radius+n,-h),a.lineTo(o[s].radius+n,0),a.lineTo(0,0),a.closePath(),a.fill()),o[s].funnelArea=[d.x-o[s].radius,d.y-h*(s+1),d.x+o[s].radius,d.y-h*s],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor);var f=M(o[s].color,r.fillOpacity);if(r.linearType=="custom"){var u=a.createLinearGradient(o[s].radius,-h,-o[s].radius,-h);u.addColorStop(0,M(o[s].color,r.fillOpacity)),u.addColorStop(.5,M(r.customColor[o[s].linearIndex],r.fillOpacity)),u.addColorStop(1,M(o[s].color,r.fillOpacity)),f=u}a.setFillStyle(f),a.moveTo(0,0),a.lineTo(-o[s].radius,0),a.lineTo(-o[s+1].radius,-h),a.lineTo(o[s+1].radius,-h),a.lineTo(o[s].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),r.border==!0&&a.stroke()}a.translate(0,-h)}else{a.translate(0,-(o.length-1)*h);for(let s=0;s<o.length;s++){if(s==o.length-1){e.tooltip&&e.tooltip.index==s&&(a.beginPath(),a.setFillStyle(M(o[s].color,r.activeOpacity)),a.moveTo(-n-r.minSize/2,0),a.lineTo(-o[s].radius-n,-h),a.lineTo(o[s].radius+n,-h),a.lineTo(n+r.minSize/2,0),a.lineTo(-n-r.minSize/2,0),a.closePath(),a.fill()),o[s].funnelArea=[d.x-o[s].radius,d.y-h,d.x+o[s].radius,d.y],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor);var f=M(o[s].color,r.fillOpacity);if(r.linearType=="custom"){var u=a.createLinearGradient(o[s].radius,-h,-o[s].radius,-h);u.addColorStop(0,M(o[s].color,r.fillOpacity)),u.addColorStop(.5,M(r.customColor[o[s].linearIndex],r.fillOpacity)),u.addColorStop(1,M(o[s].color,r.fillOpacity)),f=u}a.setFillStyle(f),a.moveTo(0,0),a.lineTo(-r.minSize/2,0),a.lineTo(-o[s].radius,-h),a.lineTo(o[s].radius,-h),a.lineTo(r.minSize/2,0),a.lineTo(0,0),a.closePath(),a.fill(),r.border==!0&&a.stroke()}else{e.tooltip&&e.tooltip.index==s&&(a.beginPath(),a.setFillStyle(M(o[s].color,r.activeOpacity)),a.moveTo(0,0),a.lineTo(-o[s+1].radius-n,0),a.lineTo(-o[s].radius-n,-h),a.lineTo(o[s].radius+n,-h),a.lineTo(o[s+1].radius+n,0),a.lineTo(0,0),a.closePath(),a.fill()),o[s].funnelArea=[d.x-o[s].radius,d.y-h*(o.length-s),d.x+o[s].radius,d.y-h*(o.length-s-1)],a.beginPath(),a.setLineWidth(r.borderWidth*e.pix),a.setStrokeStyle(r.borderColor);var f=M(o[s].color,r.fillOpacity);if(r.linearType=="custom"){var u=a.createLinearGradient(o[s].radius,-h,-o[s].radius,-h);u.addColorStop(0,M(o[s].color,r.fillOpacity)),u.addColorStop(.5,M(r.customColor[o[s].linearIndex],r.fillOpacity)),u.addColorStop(1,M(o[s].color,r.fillOpacity)),f=u}a.setFillStyle(f),a.moveTo(0,0),a.lineTo(-o[s+1].radius,0),a.lineTo(-o[s].radius,-h),a.lineTo(o[s].radius,-h),a.lineTo(o[s+1].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),r.border==!0&&a.stroke()}a.translate(0,h)}}return a.restore(),e.dataLabel!==!1&&t===1&&Si(o,e,a,h,r.labelAlign,n,d),t===1&&wi(o,e,a,h,r.labelAlign,n,d),{center:d,radius:g,series:o}}function Si(i,e,l,a,t,r,h){for(let d=0;d<i.length;d++){let n=i[d];if(n.labelShow===!1)continue;let g,o,f,u,s=n.formatter?n.formatter(n,d,i,e):fe.toFixed(n._proportion_*100)+"%";s=n.labelText?n.labelText:s,t=="right"&&(d==i.length-1?g=(n.funnelArea[2]+h.x)/2:g=(n.funnelArea[2]+i[d+1].funnelArea[2])/2,o=g+r*2,f=n.funnelArea[1]+a/2,u=n.textSize*e.pix||e.fontSize*e.pix,l.setLineWidth(1*e.pix),l.setStrokeStyle(n.color),l.setFillStyle(n.color),l.beginPath(),l.moveTo(g,f),l.lineTo(o,f),l.stroke(),l.closePath(),l.beginPath(),l.moveTo(o,f),l.arc(o,f,2*e.pix,0,2*Math.PI),l.closePath(),l.fill(),l.beginPath(),l.setFontSize(u),l.setFillStyle(n.textColor||e.fontColor),l.fillText(s,o+5,f+u/2-2),l.closePath(),l.stroke(),l.closePath()),t=="left"&&(d==i.length-1?g=(n.funnelArea[0]+h.x)/2:g=(n.funnelArea[0]+i[d+1].funnelArea[0])/2,o=g-r*2,f=n.funnelArea[1]+a/2,u=n.textSize*e.pix||e.fontSize*e.pix,l.setLineWidth(1*e.pix),l.setStrokeStyle(n.color),l.setFillStyle(n.color),l.beginPath(),l.moveTo(g,f),l.lineTo(o,f),l.stroke(),l.closePath(),l.beginPath(),l.moveTo(o,f),l.arc(o,f,2,0,2*Math.PI),l.closePath(),l.fill(),l.beginPath(),l.setFontSize(u),l.setFillStyle(n.textColor||e.fontColor),l.fillText(s,o-5-H(s,u,l),f+u/2-2),l.closePath(),l.stroke(),l.closePath())}}function wi(i,e,l,a,t,r,h){for(let d=0;d<i.length;d++){let n=i[d],g,o;n.centerText&&(g=n.funnelArea[1]+a/2,o=n.centerTextSize*e.pix||e.fontSize*e.pix,l.beginPath(),l.setFontSize(o),l.setFillStyle(n.centerTextColor||"#FFFFFF"),l.fillText(n.centerText,h.x-H(n.centerText,o,l)/2,g+o/2-2),l.closePath(),l.stroke(),l.closePath())}}function Z(i,e){e.save(),e.translate(0,.5),e.restore(),e.draw()}var Pi={easeIn:function(e){return Math.pow(e,3)},easeOut:function(e){return Math.pow(e-1,3)+1},easeInOut:function(e){return(e/=.5)<1?.5*Math.pow(e,3):.5*(Math.pow(e-2,3)+2)},linear:function(e){return e}};function j(i){this.isStop=!1,i.duration=typeof i.duration=="undefined"?1e3:i.duration,i.timing=i.timing||"easeInOut";var e=17;function l(){return typeof setTimeout!="undefined"?function(h,d){setTimeout(function(){var n=+new Date;h(n)},d)}:typeof requestAnimationFrame!="undefined"?requestAnimationFrame:function(h){h(null)}}var a=l(),t=null,r=function(d){if(d===null||this.isStop===!0){i.onProcess&&i.onProcess(1),i.onAnimationFinish&&i.onAnimationFinish();return}if(t===null&&(t=d),d-t<i.duration){var n=(d-t)/i.duration,g=Pi[i.timing];n=g(n),i.onProcess&&i.onProcess(n),a(r,e)}else i.onProcess&&i.onProcess(1),i.onAnimationFinish&&i.onAnimationFinish()};r=r.bind(this),a(r,e)}j.prototype.stop=function(){this.isStop=!0};function K(i,e,l,a){var t=this,r=e.series;(i==="pie"||i==="ring"||i==="mount"||i==="rose"||i==="funnel")&&(r=ya(r,e,l));var h=e.categories;if(i==="mount"){h=[];for(let y=0;y<r.length;y++)r[y].show!==!1&&h.push(r[y].name);e.categories=h}r=Le(r,e,l);var d=e.animation?e.duration:0;t.animationInstance&&t.animationInstance.stop();var n=null;if(i=="candle"){let y=k({},e.extra.candle.average);y.show?(n=sa(y.day,y.name,y.color,r[0].data),n=Le(n,e,l),e.seriesMA=n):e.seriesMA?n=e.seriesMA=Le(e.seriesMA,e,l):n=r}else n=r;e._series_=r=ea(r),e.area=new Array(4);for(let y=0;y<4;y++)e.area[y]=e.padding[y]*e.pix;var g=La(n,e,l,e.chartData,a),o=g.area.wholeHeight,f=g.area.wholeWidth;switch(e.legend.position){case"top":e.area[0]+=o;break;case"bottom":e.area[2]+=o;break;case"left":e.area[3]+=f;break;case"right":e.area[1]+=f;break}let u={},s=0;if(e.type==="line"||e.type==="column"||e.type==="mount"||e.type==="area"||e.type==="mix"||e.type==="candle"||e.type==="scatter"||e.type==="bubble"||e.type==="bar"){if(u=Ee(r,e,l,a),s=u.yAxisWidth,e.yAxis.showTitle){let x=0;for(let v=0;v<e.yAxis.data.length;v++)x=Math.max(x,e.yAxis.data[v].titleFontSize?e.yAxis.data[v].titleFontSize*e.pix:l.fontSize);e.area[0]+=x}let y=0,c=0;for(let x=0;x<s.length;x++)s[x].position=="left"?(c>0?e.area[3]+=s[x].width+e.yAxis.padding*e.pix:e.area[3]+=s[x].width,c+=1):s[x].position=="right"&&(y>0?e.area[1]+=s[x].width+e.yAxis.padding*e.pix:e.area[1]+=s[x].width,y+=1)}else l.yAxisWidth=s;if(e.chartData.yAxisData=u,e.categories&&e.categories.length&&e.type!=="radar"&&e.type!=="gauge"&&e.type!=="bar"){e.chartData.xAxisData=we(e.categories,e,l);let y=Xe(e.categories,e,l,e.chartData.xAxisData.eachSpacing,a),c=y.xAxisHeight,x=y.angle;l.xAxisHeight=c,l._xAxisTextAngle_=x,e.area[2]+=c,e.chartData.categoriesData=y}else if(e.type==="line"||e.type==="area"||e.type==="scatter"||e.type==="bubble"||e.type==="bar"){e.chartData.xAxisData=ka(r,e,l,a),h=e.chartData.xAxisData.rangesFormat;let y=Xe(h,e,l,e.chartData.xAxisData.eachSpacing,a),c=y.xAxisHeight,x=y.angle;l.xAxisHeight=c,l._xAxisTextAngle_=x,e.area[2]+=c,e.chartData.categoriesData=y}else e.chartData.xAxisData={xAxisPoints:[]};if(e.enableScroll&&e.xAxis.scrollAlign=="right"&&e._scrollDistance_===void 0){let y=0,c=e.chartData.xAxisData.xAxisPoints,x=e.chartData.xAxisData.startX,v=e.chartData.xAxisData.endX,m=e.chartData.xAxisData.eachSpacing*(c.length-1);y=v-x-m,t.scrollOption.currentOffset=y,t.scrollOption.startTouchX=y,t.scrollOption.distance=0,t.scrollOption.lastMoveTime=0,e._scrollDistance_=y}switch((i==="pie"||i==="ring"||i==="rose")&&(l._pieTextMaxLength_=e.dataLabel===!1?0:Ba(n,l,a,e)),i){case"word":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(y){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),Ai(r,e,l,a,y),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"map":a.clearRect(0,0,e.width,e.height),mi(r,e,l,a),setTimeout(()=>{this.uevent.trigger("renderComplete")},50);break;case"funnel":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(y){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),e.chartData.funnelData=Ti(r,e,l,a,y),Q(e.series,e,l,a,e.chartData),ee(e,l,a,y),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=di(r,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"scatter":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=ni(r,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"bubble":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=hi(r,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=oi(r,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=ai(r,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"mount":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=ii(r,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"bar":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),he(h,e,l,a);var x=ri(r,e,l,a,c),v=x.yAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.yAxisPoints=v,e.chartData.xAxisPoints=e.chartData.xAxisData.xAxisPoints,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=ti(r,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"ring":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),e.chartData.pieData=Ue(r,e,l,a,c),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"pie":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),e.chartData.pieData=Ue(r,e,l,a,c),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),e.chartData.pieData=fi(r,e,l,a,c),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),e.chartData.radarData=gi(r,e,l,a,c),Q(e.series,e,l,a,e.chartData),ee(e,l,a,c),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),e.chartData.arcbarData=ui(r,e,l,a,c),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),e.chartData.gaugeData=si(h,r,e,l,a,c),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new j({timing:e.timing,duration:d,onProcess:function(c){a.clearRect(0,0,e.width,e.height),e.rotate&&U(a,e),ge(h,e,l,a),he(h,e,l,a);var x=li(r,n,e,l,a,c),v=x.xAxisPoints,b=x.calPoints,m=x.eachSpacing;e.chartData.xAxisPoints=v,e.chartData.calPoints=b,e.chartData.eachSpacing=m,de(r,e,l,a),e.enableMarkLine!==!1&&c===1&&ne(e,l,a),Q(n||e.series,e,l,a,e.chartData),ee(e,l,a,c,m,v),Z(e,a)},onAnimationFinish:function(){t.uevent.trigger("renderComplete")}});break}}function Me(){this.events={}}Me.prototype.addEventListener=function(i,e){this.events[i]=this.events[i]||[],this.events[i].push(e)};Me.prototype.delEventListener=function(i){this.events[i]=[]};Me.prototype.trigger=function(){for(var i=arguments.length,e=Array(i),l=0;l<i;l++)e[l]=arguments[l];var a=e[0],t=e.slice(1);this.events[a]&&this.events[a].forEach(function(r){try{r.apply(null,t)}catch(h){}})};var ae=function(e){e.pix=e.pixelRatio?e.pixelRatio:1,e.fontSize=e.fontSize?e.fontSize:13,e.fontColor=e.fontColor?e.fontColor:Ae.fontColor,(e.background==""||e.background=="none")&&(e.background="#FFFFFF"),e.title=k({},e.title),e.subtitle=k({},e.subtitle),e.duration=e.duration?e.duration:1e3,e.yAxis=k({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,gridSet:"number",splitNumber:5,gridType:"solid",dashLength:4*e.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},e.yAxis),e.xAxis=k({},{rotateLabel:!1,rotateAngle:45,disabled:!1,disableGrid:!1,splitNumber:5,calibration:!1,fontColor:"#666666",fontSize:13,lineHeight:20,marginTop:0,gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc",titleFontSize:13,titleOffsetY:0,titleOffsetX:0,titleFontColor:"#666666"},e.xAxis),e.xAxis.scrollPosition=e.xAxis.scrollAlign,e.legend=k({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:e.fontSize,lineHeight:e.fontSize,fontColor:e.fontColor,formatter:{},hiddenColor:"#CECECE"},e.legend),e.extra=k({tooltip:{legendShape:"auto"}},e.extra),e.rotate=!!e.rotate,e.animation=!!e.animation,e.rotate=!!e.rotate,e.canvas2d=!!e.canvas2d;let l=k({},Ae);if(l.color=e.color?e.color:l.color,e.type=="pie"&&(l.pieChartLinePadding=e.dataLabel===!1?0:e.extra.pie.labelWidth*e.pix||l.pieChartLinePadding*e.pix),e.type=="ring"&&(l.pieChartLinePadding=e.dataLabel===!1?0:e.extra.ring.labelWidth*e.pix||l.pieChartLinePadding*e.pix),e.type=="rose"&&(l.pieChartLinePadding=e.dataLabel===!1?0:e.extra.rose.labelWidth*e.pix||l.pieChartLinePadding*e.pix),l.pieChartTextPadding=e.dataLabel===!1?0:l.pieChartTextPadding*e.pix,l.rotate=e.rotate,e.rotate){let a=e.width,t=e.height;e.width=t,e.height=a}if(e.padding=e.padding?e.padding:l.padding,l.yAxisWidth=Ae.yAxisWidth*e.pix,l.fontSize=e.fontSize*e.pix,l.titleFontSize=Ae.titleFontSize*e.pix,l.subtitleFontSize=Ae.subtitleFontSize*e.pix,!e.context)throw new Error("[uCharts] \u672A\u83B7\u53D6\u5230context\uFF01\u6CE8\u610F\uFF1Av2.0\u7248\u672C\u540E\uFF0C\u9700\u8981\u81EA\u884C\u83B7\u53D6canvas\u7684\u7ED8\u56FE\u4E0A\u4E0B\u6587\u5E76\u4F20\u5165opts.context\uFF01");this.context=e.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(a){return this.strokeStyle=a},this.context.setLineWidth=function(a){return this.lineWidth=a},this.context.setLineCap=function(a){return this.lineCap=a},this.context.setFontSize=function(a){return this.font=a+"px sans-serif"},this.context.setFillStyle=function(a){return this.fillStyle=a},this.context.setTextAlign=function(a){return this.textAlign=a},this.context.setTextBaseline=function(a){return this.textBaseline=a},this.context.setShadow=function(a,t,r,h){this.shadowColor=h,this.shadowOffsetX=a,this.shadowOffsetY=t,this.shadowBlur=r},this.context.draw=function(){}),this.context.setLineDash||(this.context.setLineDash=function(a){}),e.chartData={},this.uevent=new Me,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=e,this.config=l,K.call(this,e.type,e,l,this.context)};ae.prototype.updateData=function(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};switch(this.opts=k({},this.opts,i),this.opts.updateData=!0,i.scrollPosition||"current"){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":let l=Ee(this.opts.series,this.opts,this.config,this.context),a=l.yAxisWidth;this.config.yAxisWidth=a;let t=0,r=we(this.opts.categories,this.opts,this.config),h=r.xAxisPoints,d=r.startX,n=r.endX,o=r.eachSpacing*(h.length-1);t=n-d-o,this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0},this.opts._scrollDistance_=t;break}K.call(this,this.opts.type,this.opts,this.config,this.context)};ae.prototype.zoom=function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.opts.xAxis.itemCount;if(this.opts.enableScroll!==!0){console.log("[uCharts] \u8BF7\u542F\u7528\u6EDA\u52A8\u6761\u540E\u4F7F\u7528");return}let e=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=i.itemCount;let l=Ee(this.opts.series,this.opts,this.config,this.context),a=l.yAxisWidth;this.config.yAxisWidth=a;let t=0,r=we(this.opts.categories,this.opts,this.config),h=r.xAxisPoints,d=r.startX,n=r.endX,g=r.eachSpacing,o=g*e,f=n-d,u=f-g*(h.length-1);t=f/2-o,t>0&&(t=0),t<u&&(t=u),this.scrollOption={currentOffset:t,startTouchX:0,distance:0,lastMoveTime:0},Re(this,t,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=t,K.call(this,this.opts.type,this.opts,this.config,this.context)};ae.prototype.dobuleZoom=function(i){if(this.opts.enableScroll!==!0){console.log("[uCharts] \u8BF7\u542F\u7528\u6EDA\u52A8\u6761\u540E\u4F7F\u7528");return}let e=i.changedTouches;if(e.length<2)return;for(var l=0;l<e.length;l++)e[l].x=e[l].x?e[l].x:e[l].clientX,e[l].y=e[l].y?e[l].y:e[l].clientY;let a=[ye(e[0],this.opts,i),ye(e[1],this.opts,i)],t=Math.abs(a[0].x-a[1].x);if(!this.scrollOption.moveCount){let x={changedTouches:[{x:e[0].x,y:this.opts.area[0]/this.opts.pix+2}]},v={changedTouches:[{x:e[1].x,y:this.opts.area[0]/this.opts.pix+2}]};this.opts.rotate&&(x={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[0].y}]},v={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:e[1].y}]});let b=this.getCurrentDataIndex(x).index,m=this.getCurrentDataIndex(v).index,S=Math.abs(b-m);this.scrollOption.moveCount=S,this.scrollOption.moveCurrent1=Math.min(b,m),this.scrollOption.moveCurrent2=Math.max(b,m);return}let r=t/this.scrollOption.moveCount,h=(this.opts.width-this.opts.area[1]-this.opts.area[3])/r;h=h<=2?2:h,h=h>=this.opts.categories.length?this.opts.categories.length:h,this.opts.animation=!1,this.opts.xAxis.itemCount=h;let d=0,n=we(this.opts.categories,this.opts,this.config),g=n.xAxisPoints,o=n.startX,f=n.endX,u=n.eachSpacing,s=u*this.scrollOption.moveCurrent1,c=f-o-u*(g.length-1);d=-s+Math.min(a[0].x,a[1].x)-this.opts.area[3]-u,d>0&&(d=0),d<c&&(d=c),this.scrollOption.currentOffset=d,this.scrollOption.startTouchX=0,this.scrollOption.distance=0,Re(this,d,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=d,K.call(this,this.opts.type,this.opts,this.config,this.context)};ae.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()};ae.prototype.addEventListener=function(i,e){this.uevent.addEventListener(i,e)};ae.prototype.delEventListener=function(i){this.uevent.delEventListener(i)};ae.prototype.getCurrentDataIndex=function(i){var e=null;if(i.changedTouches?e=i.changedTouches[0]:e=i.mp.changedTouches[0],e){let l=ye(e,this.opts,i);return this.opts.type==="pie"||this.opts.type==="ring"?Da({x:l.x,y:l.y},this.opts.chartData.pieData,this.opts):this.opts.type==="rose"?pa({x:l.x,y:l.y},this.opts.chartData.pieData,this.opts):this.opts.type==="radar"?Pa({x:l.x,y:l.y},this.opts.chartData.radarData,this.opts.categories.length):this.opts.type==="funnel"?Ca({x:l.x,y:l.y},this.opts.chartData.funnelData):this.opts.type==="map"?Ma({x:l.x,y:l.y},this.opts):this.opts.type==="word"?Fa({x:l.x,y:l.y},this.opts.chartData.wordCloudData):this.opts.type==="bar"?Ta({x:l.x,y:l.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset)):Aa({x:l.x,y:l.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1};ae.prototype.getLegendDataIndex=function(i){var e=null;if(i.changedTouches?e=i.changedTouches[0]:e=i.mp.changedTouches[0],e){let l=ye(e,this.opts,i);return Sa({x:l.x,y:l.y},this.opts.chartData.legendData)}return-1};ae.prototype.touchLegend=function(i){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},l=null;if(i.changedTouches?l=i.changedTouches[0]:l=i.mp.changedTouches[0],l){var a=ye(l,this.opts,i),t=this.getLegendDataIndex(i);t>=0&&(this.opts.type=="candle"?this.opts.seriesMA[t].show=!this.opts.seriesMA[t].show:this.opts.series[t].show=!this.opts.series[t].show,this.opts.animation=!!e.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,K.call(this,this.opts.type,this.opts,this.config,this.context))}};ae.prototype.showToolTip=function(i){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},l=null;i.changedTouches?l=i.changedTouches[0]:l=i.mp.changedTouches[0],l||console.log("[uCharts] \u672A\u83B7\u53D6\u5230event\u5750\u6807\u4FE1\u606F");var a=ye(l,this.opts,i),t=this.scrollOption.currentOffset,r=k({},this.opts,{_scrollDistance_:t,animation:!1});if(this.opts.type==="line"||this.opts.type==="area"||this.opts.type==="column"||this.opts.type==="scatter"||this.opts.type==="bubble"){var h=this.getCurrentDataIndex(i),d=e.index==null?h.index:e.index;if(d>-1||d.length>0){var n=Te(this.opts.series,d,h.group);if(n.length!==0){var g=He(n,this.opts,d,h.group,this.opts.categories,e),o=g.textList,f=g.offset;f.y=a.y,r.tooltip={textList:e.textList!==void 0?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d,group:h.group}}}K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="mount"){var d=e.index==null?this.getCurrentDataIndex(i).index:e.index;if(d>-1){var r=k({},this.opts,{animation:!1}),n=k({},r._series_[d]),o=[{text:e.formatter?e.formatter(n,void 0,d,r):n.name+": "+n.data,color:n.color,legendShape:this.opts.extra.tooltip.legendShape=="auto"?n.legendShape:this.opts.extra.tooltip.legendShape}],f={x:r.chartData.calPoints[d].x,y:a.y};r.tooltip={textList:e.textList?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="bar"){var h=this.getCurrentDataIndex(i),d=e.index==null?h.index:e.index;if(d>-1||d.length>0){var n=Te(this.opts.series,d,h.group);if(n.length!==0){var g=He(n,this.opts,d,h.group,this.opts.categories,e),o=g.textList,f=g.offset;f.x=a.x,r.tooltip={textList:e.textList!==void 0?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}}K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="mix"){var h=this.getCurrentDataIndex(i),d=e.index==null?h.index:e.index;if(d>-1){var t=this.scrollOption.currentOffset,r=k({},this.opts,{_scrollDistance_:t,animation:!1}),n=Te(this.opts.series,d);if(n.length!==0){var u=ma(n,this.opts,d,this.opts.categories,e),o=u.textList,f=u.offset;f.y=a.y,r.tooltip={textList:e.textList?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}}K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="candle"){var h=this.getCurrentDataIndex(i),d=e.index==null?h.index:e.index;if(d>-1){var t=this.scrollOption.currentOffset,r=k({},this.opts,{_scrollDistance_:t,animation:!1}),n=Te(this.opts.series,d);if(n.length!==0){var g=ba(this.opts.series[0].data,n,this.opts,d,this.opts.categories,this.opts.extra.candle,e),o=g.textList,f=g.offset;f.y=a.y,r.tooltip={textList:e.textList?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}}K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="pie"||this.opts.type==="ring"||this.opts.type==="rose"||this.opts.type==="funnel"){var d=e.index==null?this.getCurrentDataIndex(i):e.index;if(d>-1){var r=k({},this.opts,{animation:!1}),n=k({},r._series_[d]),o=[{text:e.formatter?e.formatter(n,void 0,d,r):n.name+": "+n.data,color:n.color,legendShape:this.opts.extra.tooltip.legendShape=="auto"?n.legendShape:this.opts.extra.tooltip.legendShape}],f={x:a.x,y:a.y};r.tooltip={textList:e.textList?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="map"){var d=e.index==null?this.getCurrentDataIndex(i):e.index;if(d>-1){var r=k({},this.opts,{animation:!1}),n=k({},this.opts.series[d]);n.name=n.properties.name;var o=[{text:e.formatter?e.formatter(n,void 0,d,this.opts):n.name,color:n.color,legendShape:this.opts.extra.tooltip.legendShape=="auto"?n.legendShape:this.opts.extra.tooltip.legendShape}],f={x:a.x,y:a.y};r.tooltip={textList:e.textList?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}r.updateData=!1,K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="word"){var d=e.index==null?this.getCurrentDataIndex(i):e.index;if(d>-1){var r=k({},this.opts,{animation:!1}),n=k({},this.opts.series[d]),o=[{text:e.formatter?e.formatter(n,void 0,d,this.opts):n.name,color:n.color,legendShape:this.opts.extra.tooltip.legendShape=="auto"?n.legendShape:this.opts.extra.tooltip.legendShape}],f={x:a.x,y:a.y};r.tooltip={textList:e.textList?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}r.updateData=!1,K.call(this,r.type,r,this.config,this.context)}if(this.opts.type==="radar"){var d=e.index==null?this.getCurrentDataIndex(i):e.index;if(d>-1){var r=k({},this.opts,{animation:!1}),n=Te(this.opts.series,d);if(n.length!==0){var o=n.map(b=>({text:e.formatter?e.formatter(b,this.opts.categories[d],d,this.opts):b.name+": "+b.data,color:b.color,legendShape:this.opts.extra.tooltip.legendShape=="auto"?b.legendShape:this.opts.extra.tooltip.legendShape})),f={x:a.x,y:a.y};r.tooltip={textList:e.textList?e.textList:o,offset:e.offset!==void 0?e.offset:f,option:e,index:d}}}K.call(this,r.type,r,this.config,this.context)}};ae.prototype.translate=function(i){this.scrollOption={currentOffset:i,startTouchX:i,distance:0,lastMoveTime:0};let e=k({},this.opts,{_scrollDistance_:i,animation:!1});K.call(this,this.opts.type,e,this.config,this.context)};ae.prototype.scrollStart=function(i){var e=null;i.changedTouches?e=i.changedTouches[0]:e=i.mp.changedTouches[0];var l=ye(e,this.opts,i);e&&this.opts.enableScroll===!0&&(this.scrollOption.startTouchX=l.x)};ae.prototype.scroll=function(i){this.scrollOption.lastMoveTime===0&&(this.scrollOption.lastMoveTime=Date.now());let e=this.opts.touchMoveLimit||60,l=Date.now();if(!(l-this.scrollOption.lastMoveTime<Math.floor(1e3/e))&&this.scrollOption.startTouchX!=0){this.scrollOption.lastMoveTime=l;var t=null;if(i.changedTouches?t=i.changedTouches[0]:t=i.mp.changedTouches[0],t&&this.opts.enableScroll===!0){var r=ye(t,this.opts,i),h;h=r.x-this.scrollOption.startTouchX;var d=this.scrollOption.currentOffset,n=Re(this,d+h,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=h=n-d;var g=k({},this.opts,{_scrollDistance_:d+h,animation:!1});return this.opts=g,K.call(this,g.type,g,this.config,this.context),d+h}}};ae.prototype.scrollEnd=function(i){if(this.opts.enableScroll===!0){var e=this.scrollOption,l=e.currentOffset,a=e.distance;this.scrollOption.currentOffset=l+a,this.scrollOption.distance=0,this.scrollOption.moveCount=0}};var ta=ae;var V=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],Ci=(i,e)=>{var l=new Date;l.setTime(i*1e3);var a=l.getFullYear(),t=l.getMonth()+1;t=t<10?"0"+t:t;var r=l.getDate();r=r<10?"0"+r:r;var h=l.getHours();h=h<10?"0"+h:h;var d=l.getMinutes(),n=l.getSeconds();return d=d<10?"0"+d:d,n=n<10?"0"+n:n,e=="full"?a+"-"+t+"-"+r+" "+h+":"+d+":"+n:e=="y-m-d"?a+"-"+t+"-"+r:e=="h:m"?h+":"+d:e=="h:m:s"?h+":"+d+":"+n:[a,t,r,h,d,n]},Fi={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","mount","bar","area","radar","gauge","candle","mix","tline","tarea","scatter","bubble","demotype"],range:["\u997C\u72B6\u56FE","\u5706\u73AF\u56FE","\u73AB\u7470\u56FE","\u8BCD\u4E91\u56FE","\u6F0F\u6597\u56FE","\u5730\u56FE","\u5706\u5F27\u8FDB\u5EA6\u6761","\u6298\u7EBF\u56FE","\u67F1\u72B6\u56FE","\u5C71\u5CF0\u56FE","\u6761\u72B6\u56FE","\u533A\u57DF\u56FE","\u96F7\u8FBE\u56FE","\u4EEA\u8868\u76D8","K\u7EBF\u56FE","\u6DF7\u5408\u56FE","\u65F6\u95F4\u8F74\u6298\u7EBF","\u65F6\u95F4\u8F74\u533A\u57DF","\u6563\u70B9\u56FE","\u6C14\u6CE1\u56FE","\u81EA\u5B9A\u4E49\u7C7B\u578B"],categories:["line","column","mount","bar","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(i,e,l){return i+"\u5143"},yAxisDemo2:function(i,e,l){return i.toFixed(2)},xAxisDemo1:function(i,e,l){return i+"\u5E74"},xAxisDemo2:function(i,e,l){return Ci(i,"h:m")},seriesDemo1:function(i,e,l,a){return i+"\u5143"},tooltipDemo1:function(i,e,l,a){return l==0?"\u968F\u4FBF\u7528"+i.data+"\u5E74":"\u5176\u4ED6\u6211\u6CA1\u6539"+i.data+"\u5929"},pieDemo:function(i,e,l,a){if(e!==void 0)return l[e].name+"\uFF1A"+l[e].data+"\u5143"}},demotype:{type:"line",color:V,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:V,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:V,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"\u6536\u76CA\u7387",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:V,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:V,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:V,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:V,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:V,title:{name:"\u767E\u5206\u6BD4",fontSize:25,color:"#00FF00"},subtitle:{name:"\u9ED8\u8BA4\u6807\u9898",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:V,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2,activeType:"hollow"}}},tline:{type:"line",color:V,padding:[15,10,0,15],xAxis:{disableGrid:!1,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{line:{type:"curve",width:2,activeType:"hollow"}}},tarea:{type:"area",color:V,padding:[15,10,0,15],xAxis:{disableGrid:!0,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},column:{type:"column",color:V,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},mount:{type:"mount",color:V,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{mount:{type:"mount",widthRatio:1.5}}},bar:{type:"bar",color:V,padding:[15,30,0,5],xAxis:{boundaryGap:"justify",disableGrid:!1,min:0,axisLine:!1},yAxis:{},legend:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:V,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1,activeType:"hollow"}}},radar:{type:"radar",color:V,padding:[5,5,5,5],dataLabel:!1,legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,max:200,labelShow:!0}}},gauge:{type:"gauge",color:V,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"\u5B9E\u65F6\u901F\u5EA6",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:V,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:V,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},scatter:{type:"scatter",color:V,padding:[15,15,0,15],dataLabel:!1,xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0},yAxis:{disableGrid:!1,gridType:"dash"},legend:{},extra:{scatter:{}}},bubble:{type:"bubble",color:V,padding:[15,15,0,15],xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0,max:250},yAxis:{disableGrid:!1,gridType:"dash",data:[{min:0,max:150}]},legend:{},extra:{bubble:{border:2,opacity:.5}}}},D=Fi;var re=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],Mi={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(i){let e="";for(let l in i){l==0&&(e+=i[l].axisValueLabel+"\u5E74\u9500\u552E\u989D");let a="--";i[l].data!==null&&(a=i[l].data),e+=`
`+i[l].seriesName+"\uFF1A"+a+" \u4E07\u5143",e+="<br/>"+i[l].marker+i[l].seriesName+"\uFF1A"+a+" \u4E07\u5143"}return e},legendFormat:function(i){return"\u81EA\u5B9A\u4E49\u56FE\u4F8B+"+i},yAxisFormatDemo:function(i,e){return i+"\u5143"},seriesFormatDemo:function(i){return i.name+"\u5E74"+i.value+"\u5143"}},demotype:{color:re},column:{color:re,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:re,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:re,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:re,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:re,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:re,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],roseType:"area"}},funnel:{color:re,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:re,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"\u4E1A\u52A1\u6307\u6807",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"\u5B8C\u6210\u7387"}]}},candle:{xAxis:{data:[]},yAxis:{},color:re,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}},E=Mi;var q={},ce=null;function pe(i={},...e){for(let l in e)for(let a in e[l])e[l].hasOwnProperty(a)&&(i[a]=e[l][a]&&typeof e[l][a]=="object"?pe(Array.isArray(e[l][a])?[]:{},i[a],e[l][a]):e[l][a]);return i}function Ge(i,e){for(let l in i)i.hasOwnProperty(l)&&i[l]!==null&&typeof i[l]=="object"?Ge(i[l],e):l==="format"&&typeof i[l]=="string"&&(i.formatter=e[i[l]]?e[i[l]]:void 0);return i}var pi={data(){return{rid:null}},mounted(){ce={top:0,left:0},setTimeout(()=>{this.rid===null&&this.$ownerInstance&&this.$ownerInstance.callMethod("getRenderType")},200)},destroyed(){delete D.option[this.rid],delete D.instance[this.rid],delete E.option[this.rid],delete E.instance[this.rid]},methods:{ecinit(i,e,l,a){let t=JSON.stringify(i.id);this.rid=t,q[t]=this.$ownerInstance||a;let r=JSON.parse(JSON.stringify(i)),h=r.type;h&&E.type.includes(h)?E.option[t]=pe({},E[h],r):E.option[t]=pe({},r);let d=r.chartData;if(d){E.option[t].xAxis&&E.option[t].xAxis.type&&E.option[t].xAxis.type==="category"&&(E.option[t].xAxis.data=d.categories),E.option[t].yAxis&&E.option[t].yAxis.type&&E.option[t].yAxis.type==="category"&&(E.option[t].yAxis.data=d.categories),E.option[t].series=[];for(var n=0;n<d.series.length;n++){E.option[t].seriesTemplate=E.option[t].seriesTemplate?E.option[t].seriesTemplate:{};let g=pe({},E.option[t].seriesTemplate,d.series[n]);E.option[t].series.push(g)}}if(typeof window.echarts=="object")this.newEChart();else{let g=document.createElement("script");g.src="./uni_modules/qiun-data-charts/static/app-plus/echarts.min.js",g.onload=this.newEChart,document.head.appendChild(g)}},ecresize(i,e,l,a){E.instance[this.rid]&&E.instance[this.rid].resize()},newEChart(){let i=this.rid;E.instance[i]===void 0?(E.instance[i]=echarts.init(q[i].$el.children[0]),E.option[i].ontap===!0&&(E.instance[i].on("click",e=>{let l=JSON.parse(JSON.stringify({x:e.event.offsetX,y:e.event.offsetY}));q[i].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:l,currentIndex:e.dataIndex,value:e.data,seriesName:e.seriesName,id:i}})}),E.instance[i].on("highlight",e=>{q[i].callMethod("emitMsg",{name:"getHighlight",params:{type:"highlight",res:e,id:i}})})),this.updataEChart(i,E.option[i])):this.updataEChart(i,E.option[i])},updataEChart(i,e){if(e=Ge(e,E.formatter),e.tooltip&&(e.tooltip.show=!!e.tooltipShow,e.tooltip.position=this.tooltipPosition(),typeof e.tooltipFormat=="string"&&E.formatter[e.tooltipFormat]&&(e.tooltip.formatter=e.tooltip.formatter?e.tooltip.formatter:E.formatter[e.tooltipFormat])),e.series)for(let l in e.series){let a=e.series[l].linearGradient;a&&(e.series[l].color=new echarts.graphic.LinearGradient(a[0],a[1],a[2],a[3],a[4]))}E.instance[i].setOption(e,e.notMerge),E.instance[i].on("finished",function(){q[i].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:i}}),E.instance[i]&&E.instance[i].off("finished")}),typeof q[i].$el.children[0].clientWidth!="undefined"&&(Math.abs(q[i].$el.children[0].clientWidth-E.instance[i].getWidth())>3||Math.abs(q[i].$el.children[0].clientHeight-E.instance[i].getHeight())>3)&&this.ecresize()},tooltipPosition(){return(i,e,l,a,t)=>{let r=i[0],h=i[1],d=t.viewSize[0],n=t.viewSize[1],g=t.contentSize[0],o=t.contentSize[1],f=r+30,u=h+30;return f+g>d&&(f=r-g-30),u+o>n&&(u=h-o-30),[f,u]}},ucinit(i,e,l,a){if(JSON.stringify(i)==JSON.stringify(e)||!i.canvasId)return;let t=JSON.parse(JSON.stringify(i.canvasId));this.rid=t,q[t]=this.$ownerInstance||a,D.option[t]=JSON.parse(JSON.stringify(i)),D.option[t]=Ge(D.option[t],D.formatter);let r=document.getElementById(t);r&&r.children[0]&&(D.option[t].context=r.children[0].getContext("2d"),D.instance[t]&&D.option[t]&&D.option[t].update===!0?this.updataUChart():setTimeout(()=>{D.option[t].context.restore(),D.option[t].context.save(),this.newUChart()},100))},newUChart(){let i=this.rid;D.instance[i]=new ta(D.option[i]),D.instance[i].addEventListener("renderComplete",()=>{q[i].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:i,opts:D.instance[i].opts}}),D.instance[i].delEventListener("renderComplete")}),D.instance[i].addEventListener("scrollLeft",()=>{q[i].callMethod("emitMsg",{name:"scrollLeft",params:{type:"scrollLeft",scrollLeft:!0,id:i,opts:D.instance[i].opts}})}),D.instance[i].addEventListener("scrollRight",()=>{q[i].callMethod("emitMsg",{name:"scrollRight",params:{type:"scrollRight",scrollRight:!0,id:i,opts:D.instance[i].opts}})})},updataUChart(){let i=this.rid;D.instance[i].updateData(D.option[i])},tooltipDefault(i,e,l,a){if(e){let t=i.data;return typeof i.data=="object"&&(t=i.data.value),e+" "+i.name+":"+t}else return i.properties&&i.properties.name?i.properties.name:i.name+":"+i.data},showTooltip(i,e){let l=D.option[e].tooltipCustom;if(l&&l!==void 0&&l!==null){let a;l.x>=0&&l.y>=0&&(a={x:l.x,y:l.y+10}),D.instance[e].showToolTip(i,{index:l.index,offset:a,textList:l.textList,formatter:(t,r,h,d)=>typeof D.option[e].tooltipFormat=="string"&&D.formatter[D.option[e].tooltipFormat]?D.formatter[D.option[e].tooltipFormat](t,r,h,d):this.tooltipDefault(t,r,h,d)})}else D.instance[e].showToolTip(i,{formatter:(a,t,r,h)=>typeof D.option[e].tooltipFormat=="string"&&D.formatter[D.option[e].tooltipFormat]?D.formatter[D.option[e].tooltipFormat](a,t,r,h):this.tooltipDefault(a,t,r,h)})},tap(i){let e=this.rid,l=D.option[e].ontap,a=D.option[e].tooltipShow,t=D.option[e].tapLegend;if(l==!1)return;let r=null,h=null,d=document.getElementById("UC"+e).getBoundingClientRect(),n={};i.detail.x?n={x:i.detail.x-d.left,y:i.detail.y-d.top+ce.top}:n={x:i.clientX-d.left,y:i.clientY-d.top+ce.top},i.changedTouches=[],i.changedTouches.unshift(n),r=D.instance[e].getCurrentDataIndex(i),h=D.instance[e].getLegendDataIndex(i),t===!0&&D.instance[e].touchLegend(i),a==!0&&this.showTooltip(i,e),q[e].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:n,currentIndex:r,legendIndex:h,id:e,opts:D.instance[e].opts}})},touchStart(i){let e=this.rid;D.option[e].ontouch!=!1&&(D.option[e].enableScroll===!0&&i.touches.length==1&&D.instance[e].scrollStart(i),q[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"touchStart",event:i.changedTouches[0],id:e,opts:D.instance[e].opts}}))},touchMove(i){let e=this.rid,l=D.option[e].ontouch;if(l!=!1){if(D.option[e].enableScroll===!0&&i.changedTouches.length==1&&D.instance[e].scroll(i),D.option[e].ontap===!0&&D.option[e].enableScroll===!1&&D.option[e].onmovetip===!0){let a=document.getElementById("UC"+e).getBoundingClientRect(),t={x:i.changedTouches[0].clientX-a.left,y:i.changedTouches[0].clientY-a.top+ce.top};i.changedTouches.unshift(t),D.option[e].tooltipShow===!0&&this.showTooltip(i,e)}l===!0&&D.option[e].enableScroll===!0&&D.option[e].onzoom===!0&&i.changedTouches.length==2&&D.instance[e].dobuleZoom(i),q[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"touchMove",event:i.changedTouches[0],id:e,opts:D.instance[e].opts}})}},touchEnd(i){let e=this.rid;D.option[e].ontouch!=!1&&(D.option[e].enableScroll===!0&&i.touches.length==0&&D.instance[e].scrollEnd(i),q[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"touchEnd",event:i.changedTouches[0],id:e,opts:D.instance[e].opts}}))},mouseDown(i){let e=this.rid;if(D.option[e].onmouse==!1)return;let a=document.getElementById("UC"+e).getBoundingClientRect(),t={};t={x:i.clientX-a.left,y:i.clientY-a.top+ce.top},i.changedTouches=[],i.changedTouches.unshift(t),D.instance[e].scrollStart(i),D.option[e].mousedown=!0,q[e].callMethod("emitMsg",{name:"getTouchStart",params:{type:"mouseDown",event:t,id:e,opts:D.instance[e].opts}})},mouseMove(i){let e=this.rid,l=D.option[e].onmouse,a=D.option[e].tooltipShow;if(l==!1)return;let t=document.getElementById("UC"+e).getBoundingClientRect(),r={};r={x:i.clientX-t.left,y:i.clientY-t.top+ce.top},i.changedTouches=[],i.changedTouches.unshift(r),D.option[e].mousedown?(D.instance[e].scroll(i),q[e].callMethod("emitMsg",{name:"getTouchMove",params:{type:"mouseMove",event:r,id:e,opts:D.instance[e].opts}})):D.instance[e]&&a==!0&&this.showTooltip(i,e)},mouseUp(i){let e=this.rid;if(D.option[e].onmouse==!1)return;let a=document.getElementById("UC"+e).getBoundingClientRect(),t={};t={x:i.clientX-a.left,y:i.clientY-a.top+ce.top},i.changedTouches=[],i.changedTouches.unshift(t),D.instance[e].scrollEnd(i),D.option[e].mousedown=!1,q[e].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"mouseUp",event:t,id:e,opts:D.instance[e].opts}})}}};return ua(Di);})();
