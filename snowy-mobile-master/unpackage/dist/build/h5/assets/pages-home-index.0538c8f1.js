import{a as e,s as t,p as i,l as a,m as o,o as n,b as r,w as s,e as l,q as c,h,v as d,f as p,t as u,i as g,k as f,x,y as m,z as y,A as v,B as b,C as S,D as w,E as T,F as _,G as A,H as P,J as C,K as k,L as D,M as L,N as I,O as M,g as F,P as O,Q as E,R,S as z,T as B,U as N,V as U,W,X as j,Y as q,Z as $,_ as H,$ as G,a0 as K,a1 as J,a2 as X,a3 as V,r as Y,a4 as Q,a5 as Z,a6 as ee,a7 as te,a8 as ie,a9 as ae,c as oe,u as ne}from"./index-58695647.js";import{_ as re}from"./uv-swiper.874acae4.js";import{_ as se}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as le}from"./uv-calendars.cf386575.js";import{_ as ce}from"./uv-icon.019f93fe.js";import{_ as he,a as de}from"./uv-row.88bea320.js";import{_ as pe,a as ue,b as ge}from"./uv-form.bce81edd.js";import{_ as fe}from"./tui-button.600cfc04.js";import{_ as xe}from"./uv-popup.f10e3fa8.js";import"./uv-transition.8392ab8b.js";const me={__name:"home-swiper",setup(s){const l=e([{image:`${t.getters.allEnv[t.getters.envKey].baseUrl}${i("/mobile/")}swiper/swiper1.jpg`},{image:`${t.getters.allEnv[t.getters.envKey].baseUrl}${i("/mobile/")}swiper/swiper2.jpg`}]);return(e,t)=>{const i=a(o("uv-swiper"),re);return n(),r(i,{list:l,keyName:"image",indicator:"",indicatorMode:"line",circular:""},null,8,["list"])}}};const ye=se({name:"tuiSection",emits:["click"],props:{title:{type:String,default:""},size:{type:[Number,String],default:32},color:{type:String,default:"#333"},fontWeight:{type:[Number,String],default:400},descr:{type:String,default:""},descrSize:{type:[Number,String],default:24},descrColor:{type:String,default:"#7a7a7a"},descrTop:{type:[Number,String],default:16},isLine:{type:Boolean,default:!1},lineWidth:{type:[Number,String],default:8},lineColor:{type:String,default:""},lineCap:{type:String,default:"circle"},lineRight:{type:[Number,String],default:16},lineGap:{type:[Number,String],default:4},background:{type:String,default:"transparent"},padding:{type:String,default:"20rpx 30rpx"},margin:{type:String,default:"0"}},computed:{getLineColor(){return this.lineColor||uni&&uni.$tui&&uni.$tui.color.primary||"#5677fc"}},methods:{handleClick(){this.$emit("click",{title:this.title})}}},[["render",function(e,t,i,a,o,x){const m=g,y=f;return n(),r(m,{class:"tui-section",style:c({margin:i.margin,background:i.background,padding:i.padding})},{default:s((()=>[l(m,{class:"tui-section__title",onClick:x.handleClick},{default:s((()=>[i.isLine?(n(),r(m,{key:0,class:"tui-section__decorate",style:c({background:x.getLineColor,width:i.lineWidth+"rpx",left:`-${i.lineRight}rpx`,top:i.lineGap+"rpx",bottom:i.lineGap+"rpx",borderRadius:"circle"===i.lineCap?`${i.lineWidth}rpx`:0})},null,8,["style"])):h("",!0),d(e.$slots,"left",{},void 0,!0),i.title?(n(),r(y,{key:1,style:c({fontSize:i.size+"rpx",color:i.color,fontWeight:i.fontWeight})},{default:s((()=>[p(u(i.title),1)])),_:1},8,["style"])):h("",!0),d(e.$slots,"default",{},void 0,!0)])),_:3},8,["onClick"]),i.descr?(n(),r(m,{key:0,class:"tui-section__sub",style:c({paddingTop:i.descrTop+"rpx"})},{default:s((()=>[l(y,{class:"tui-section__descr",style:c({fontSize:i.descrSize+"rpx",color:i.descrColor})},{default:s((()=>[p(u(i.descr),1)])),_:1},8,["style"])])),_:1},8,["style"])):h("",!0),d(e.$slots,"descr",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-4adff773"]]),ve={pages:[{path:"pages/login",style:{navigationBarTitleText:"登录",enablePullDownRefresh:!1,navigationStyle:"custom"}},{path:"pages/home/<USER>",style:{navigationBarTitleText:"小诺移动端框架",navigationStyle:"custom"}},{path:"pages/work/index",style:{navigationBarTitleText:"工作台",navigationStyle:"custom"}},{path:"pages/msg/index",style:{navigationBarTitleText:"消息",enablePullDownRefresh:!0,onReachBottomDistance:50,navigationStyle:"custom"}},{path:"pages/msg/detail",style:{navigationBarTitleText:"消息详情",navigationStyle:"custom"}},{path:"pages/mine/index",style:{navigationBarTitleText:"我的",navigationStyle:"custom"}}],subPackages:[{root:"pages/config",pages:[{path:"index",style:{navigationBarTitleText:"环境设置",enablePullDownRefresh:!1,navigationStyle:"custom"}},{path:"form",style:{navigationBarTitleText:"环境管理",enablePullDownRefresh:!1,navigationStyle:"custom"}}]},{root:"pages/common",pages:[{path:"webview/index",style:{navigationBarTitleText:"浏览网页",enablePullDownRefresh:!1,navigationStyle:"custom"}}]},{root:"pages/biz/org",pages:[{path:"index",style:{navigationBarTitleText:"机构管理",enablePullDownRefresh:!1,navigationStyle:"custom"}},{path:"form",style:{navigationBarTitleText:"机构管理",enablePullDownRefresh:!1,navigationStyle:"custom"}}]},{root:"pages/biz/position",pages:[{path:"index",style:{navigationBarTitleText:"职位管理",enablePullDownRefresh:!0,navigationStyle:"custom"}},{path:"form",style:{navigationBarTitleText:"职位管理",enablePullDownRefresh:!1,navigationStyle:"custom"}}]},{root:"pages/biz/user",pages:[{path:"index",style:{navigationBarTitleText:"用户管理",enablePullDownRefresh:!0,onReachBottomDistance:50,navigationStyle:"custom"}},{path:"form",style:{navigationBarTitleText:"用户管理",enablePullDownRefresh:!1,onReachBottomDistance:50,navigationStyle:"custom"}}]},{root:"pages/mine/info",pages:[{path:"index",style:{navigationBarTitleText:"个人信息",enablePullDownRefresh:!1,navigationStyle:"custom"}},{path:"edit",style:{navigationBarTitleText:"编辑资料",enablePullDownRefresh:!1,navigationStyle:"custom"}}]},{root:"pages/mine/home-config",pages:[{path:"index",style:{navigationBarTitleText:"首页设置",enablePullDownRefresh:!1,navigationStyle:"custom"}}]},{root:"pages/mine/pwd",pages:[{path:"index",style:{navigationBarTitleText:"修改密码",enablePullDownRefresh:!1,navigationStyle:"custom"}}]}],tabBar:{color:"#000000",selectedColor:"#000000",borderStyle:"white",backgroundColor:"#ffffff",list:[{pagePath:"pages/home/<USER>",iconPath:"static/images/tabbar/home.png",selectedIconPath:"static/images/tabbar/home_.png",text:"首页"},{pagePath:"pages/work/index",iconPath:"static/images/tabbar/work.png",selectedIconPath:"static/images/tabbar/work_.png",text:"工作台"},{pagePath:"pages/msg/index",iconPath:"static/images/tabbar/msg.png",selectedIconPath:"static/images/tabbar/msg_.png",text:"消息"},{pagePath:"pages/mine/index",iconPath:"static/images/tabbar/mine.png",selectedIconPath:"static/images/tabbar/mine_.png",text:"我的"}]},easycom:{autoscan:!0,custom:{"tui-(.*)":"@/components/thorui/thorui/tui-$1/tui-$1.vue","snowy-(.*)":"@/components/snowy/snowy-$1/snowy-$1.vue"}},globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},uniIdRouter:{}};function be(e,t,i){return e(i={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&i.path)}},i.exports),i.exports}var Se=be((function(e,t){var i;e.exports=(i=i||function(e,t){var i=Object.create||function(){function e(){}return function(t){var i;return e.prototype=t,i=new e,e.prototype=null,i}}(),a={},o=a.lib={},n=o.Base={extend:function(e){var t=i(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},r=o.WordArray=n.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,i=e.words,a=this.sigBytes,o=e.sigBytes;if(this.clamp(),a%4)for(var n=0;n<o;n++){var r=i[n>>>2]>>>24-n%4*8&255;t[a+n>>>2]|=r<<24-(a+n)%4*8}else for(n=0;n<o;n+=4)t[a+n>>>2]=i[n>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,i=this.sigBytes;t[i>>>2]&=4294967295<<32-i%4*8,t.length=e.ceil(i/4)},clone:function(){var e=n.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var i,a=[],o=function(t){var i=987654321,a=4294967295;return function(){var o=((i=36969*(65535&i)+(i>>16)&a)<<16)+(t=18e3*(65535&t)+(t>>16)&a)&a;return o/=4294967296,(o+=.5)*(e.random()>.5?1:-1)}},n=0;n<t;n+=4){var s=o(4294967296*(i||e.random()));i=987654071*s(),a.push(4294967296*s()|0)}return new r.init(a,t)}}),s=a.enc={},l=s.Hex={stringify:function(e){for(var t=e.words,i=e.sigBytes,a=[],o=0;o<i;o++){var n=t[o>>>2]>>>24-o%4*8&255;a.push((n>>>4).toString(16)),a.push((15&n).toString(16))}return a.join("")},parse:function(e){for(var t=e.length,i=[],a=0;a<t;a+=2)i[a>>>3]|=parseInt(e.substr(a,2),16)<<24-a%8*4;return new r.init(i,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,i=e.sigBytes,a=[],o=0;o<i;o++){var n=t[o>>>2]>>>24-o%4*8&255;a.push(String.fromCharCode(n))}return a.join("")},parse:function(e){for(var t=e.length,i=[],a=0;a<t;a++)i[a>>>2]|=(255&e.charCodeAt(a))<<24-a%4*8;return new r.init(i,t)}},h=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},d=o.BufferedBlockAlgorithm=n.extend({reset:function(){this._data=new r.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=h.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var i=this._data,a=i.words,o=i.sigBytes,n=this.blockSize,s=o/(4*n),l=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*n,c=e.min(4*l,o);if(l){for(var h=0;h<l;h+=n)this._doProcessBlock(a,h);var d=a.splice(0,l);i.sigBytes-=c}return new r.init(d,c)},clone:function(){var e=n.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});o.Hasher=d.extend({cfg:n.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,i){return new e.init(i).finalize(t)}},_createHmacHelper:function(e){return function(t,i){return new p.HMAC.init(e,i).finalize(t)}}});var p=a.algo={};return a}(Math),i)})),we=Se,Te=(be((function(e,t){var i;e.exports=(i=we,function(e){var t=i,a=t.lib,o=a.WordArray,n=a.Hasher,r=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var l=r.MD5=n.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var i=0;i<16;i++){var a=t+i,o=e[a];e[a]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var n=this._hash.words,r=e[t+0],l=e[t+1],u=e[t+2],g=e[t+3],f=e[t+4],x=e[t+5],m=e[t+6],y=e[t+7],v=e[t+8],b=e[t+9],S=e[t+10],w=e[t+11],T=e[t+12],_=e[t+13],A=e[t+14],P=e[t+15],C=n[0],k=n[1],D=n[2],L=n[3];C=c(C,k,D,L,r,7,s[0]),L=c(L,C,k,D,l,12,s[1]),D=c(D,L,C,k,u,17,s[2]),k=c(k,D,L,C,g,22,s[3]),C=c(C,k,D,L,f,7,s[4]),L=c(L,C,k,D,x,12,s[5]),D=c(D,L,C,k,m,17,s[6]),k=c(k,D,L,C,y,22,s[7]),C=c(C,k,D,L,v,7,s[8]),L=c(L,C,k,D,b,12,s[9]),D=c(D,L,C,k,S,17,s[10]),k=c(k,D,L,C,w,22,s[11]),C=c(C,k,D,L,T,7,s[12]),L=c(L,C,k,D,_,12,s[13]),D=c(D,L,C,k,A,17,s[14]),C=h(C,k=c(k,D,L,C,P,22,s[15]),D,L,l,5,s[16]),L=h(L,C,k,D,m,9,s[17]),D=h(D,L,C,k,w,14,s[18]),k=h(k,D,L,C,r,20,s[19]),C=h(C,k,D,L,x,5,s[20]),L=h(L,C,k,D,S,9,s[21]),D=h(D,L,C,k,P,14,s[22]),k=h(k,D,L,C,f,20,s[23]),C=h(C,k,D,L,b,5,s[24]),L=h(L,C,k,D,A,9,s[25]),D=h(D,L,C,k,g,14,s[26]),k=h(k,D,L,C,v,20,s[27]),C=h(C,k,D,L,_,5,s[28]),L=h(L,C,k,D,u,9,s[29]),D=h(D,L,C,k,y,14,s[30]),C=d(C,k=h(k,D,L,C,T,20,s[31]),D,L,x,4,s[32]),L=d(L,C,k,D,v,11,s[33]),D=d(D,L,C,k,w,16,s[34]),k=d(k,D,L,C,A,23,s[35]),C=d(C,k,D,L,l,4,s[36]),L=d(L,C,k,D,f,11,s[37]),D=d(D,L,C,k,y,16,s[38]),k=d(k,D,L,C,S,23,s[39]),C=d(C,k,D,L,_,4,s[40]),L=d(L,C,k,D,r,11,s[41]),D=d(D,L,C,k,g,16,s[42]),k=d(k,D,L,C,m,23,s[43]),C=d(C,k,D,L,b,4,s[44]),L=d(L,C,k,D,T,11,s[45]),D=d(D,L,C,k,P,16,s[46]),C=p(C,k=d(k,D,L,C,u,23,s[47]),D,L,r,6,s[48]),L=p(L,C,k,D,y,10,s[49]),D=p(D,L,C,k,A,15,s[50]),k=p(k,D,L,C,x,21,s[51]),C=p(C,k,D,L,T,6,s[52]),L=p(L,C,k,D,g,10,s[53]),D=p(D,L,C,k,S,15,s[54]),k=p(k,D,L,C,l,21,s[55]),C=p(C,k,D,L,v,6,s[56]),L=p(L,C,k,D,P,10,s[57]),D=p(D,L,C,k,m,15,s[58]),k=p(k,D,L,C,_,21,s[59]),C=p(C,k,D,L,f,6,s[60]),L=p(L,C,k,D,w,10,s[61]),D=p(D,L,C,k,u,15,s[62]),k=p(k,D,L,C,b,21,s[63]),n[0]=n[0]+C|0,n[1]=n[1]+k|0,n[2]=n[2]+D|0,n[3]=n[3]+L|0},_doFinalize:function(){var t=this._data,i=t.words,a=8*this._nDataBytes,o=8*t.sigBytes;i[o>>>5]|=128<<24-o%32;var n=e.floor(a/4294967296),r=a;i[15+(o+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),i[14+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(i.length+1),this._process();for(var s=this._hash,l=s.words,c=0;c<4;c++){var h=l[c];l[c]=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8)}return s},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,i,a,o,n,r){var s=e+(t&i|~t&a)+o+r;return(s<<n|s>>>32-n)+t}function h(e,t,i,a,o,n,r){var s=e+(t&a|i&~a)+o+r;return(s<<n|s>>>32-n)+t}function d(e,t,i,a,o,n,r){var s=e+(t^i^a)+o+r;return(s<<n|s>>>32-n)+t}function p(e,t,i,a,o,n,r){var s=e+(i^(t|~a))+o+r;return(s<<n|s>>>32-n)+t}t.MD5=n._createHelper(l),t.HmacMD5=n._createHmacHelper(l)}(Math),i.MD5)})),be((function(e,t){var i,a,o;e.exports=(a=(i=we).lib.Base,o=i.enc.Utf8,void(i.algo.HMAC=a.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=o.parse(t));var i=e.blockSize,a=4*i;t.sigBytes>a&&(t=e.finalize(t)),t.clamp();for(var n=this._oKey=t.clone(),r=this._iKey=t.clone(),s=n.words,l=r.words,c=0;c<i;c++)s[c]^=1549556828,l[c]^=909522486;n.sigBytes=r.sigBytes=a,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,i=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(i))}})))})),be((function(e,t){e.exports=we.HmacMD5}))),_e=be((function(e,t){e.exports=we.enc.Utf8})),Ae=be((function(e,t){var i,a,o;e.exports=(o=(a=i=we).lib.WordArray,a.enc.Base64={stringify:function(e){var t=e.words,i=e.sigBytes,a=this._map;e.clamp();for(var o=[],n=0;n<i;n+=3)for(var r=(t[n>>>2]>>>24-n%4*8&255)<<16|(t[n+1>>>2]>>>24-(n+1)%4*8&255)<<8|t[n+2>>>2]>>>24-(n+2)%4*8&255,s=0;s<4&&n+.75*s<i;s++)o.push(a.charAt(r>>>6*(3-s)&63));var l=a.charAt(64);if(l)for(;o.length%4;)o.push(l);return o.join("")},parse:function(e){var t=e.length,i=this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var n=0;n<i.length;n++)a[i.charCodeAt(n)]=n}var r=i.charAt(64);if(r){var s=e.indexOf(r);-1!==s&&(t=s)}return function(e,t,i){for(var a=[],n=0,r=0;r<t;r++)if(r%4){var s=i[e.charCodeAt(r-1)]<<r%4*2,l=i[e.charCodeAt(r)]>>>6-r%4*2;a[n>>>2]|=(s|l)<<24-n%4*8,n++}return o.create(a,n)}(e,t,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},i.enc.Base64)}));const Pe="FUNCTION",Ce="pending",ke="rejected";function De(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function Le(e){return"object"===De(e)}function Ie(e){return"function"==typeof e}function Me(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const Fe="REJECTED",Oe="NOT_PENDING";class Ee{constructor({createPromise:e,retryRule:t=Fe}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case Fe:return this.status===ke;case Oe:return this.status!==Ce}}exec(){return this.needRetry?(this.status=Ce,this.promise=this.createPromise().then((e=>(this.status="fulfilled",Promise.resolve(e))),(e=>(this.status=ke,Promise.reject(e)))),this.promise):this.promise}}function Re(e){return e&&"string"==typeof e?JSON.parse(e):e}const ze=Re([]);Re("");const Be=Re("[]")||[];let Ne="";try{Ne="__UNI__EF8711B"}catch(Ct){}let Ue={};function We(e,t={}){var i,a;return i=Ue,a=e,Object.prototype.hasOwnProperty.call(i,a)||(Ue[e]=t),Ue[e]}const je=["invoke","success","fail","complete"],qe=We("_globalUniCloudInterceptor");function $e(e,t){qe[e]||(qe[e]={}),Le(t)&&Object.keys(t).forEach((i=>{je.indexOf(i)>-1&&function(e,t,i){let a=qe[e][t];a||(a=qe[e][t]=[]),-1===a.indexOf(i)&&Ie(i)&&a.push(i)}(e,i,t[i])}))}function He(e,t){qe[e]||(qe[e]={}),Le(t)?Object.keys(t).forEach((i=>{je.indexOf(i)>-1&&function(e,t,i){const a=qe[e][t];if(!a)return;const o=a.indexOf(i);o>-1&&a.splice(o,1)}(e,i,t[i])})):delete qe[e]}function Ge(e,t){return e&&0!==e.length?e.reduce(((e,i)=>e.then((()=>i(t)))),Promise.resolve()):Promise.resolve()}function Ke(e,t){return qe[e]&&qe[e][t]||[]}function Je(e){$e("callObject",e)}const Xe=We("_globalUniCloudListener"),Ve="response",Ye="needLogin",Qe="refreshToken",Ze="clientdb",et="cloudfunction",tt="cloudobject";function it(e){return Xe[e]||(Xe[e]=[]),Xe[e]}function at(e,t){const i=it(e);i.includes(t)||i.push(t)}function ot(e,t){const i=it(e),a=i.indexOf(t);-1!==a&&i.splice(a,1)}function nt(e,t){const i=it(e);for(let a=0;a<i.length;a++)(0,i[a])(t)}let rt,st=!1;function lt(){return rt||(rt=new Promise((e=>{st&&e(),function t(){if("function"==typeof y){const t=y();t&&t[0]&&(st=!0,e())}st||setTimeout((()=>{t()}),30)}()})),rt)}function ct(e){const t={};for(const i in e){const a=e[i];Ie(a)&&(t[i]=Me(a))}return t}class ht extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var dt={request:e=>C(e),uploadFile:e=>k(e),setStorageSync:(e,t)=>D(e,t),getStorageSync:e=>L(e),removeStorageSync:e=>I(e),clearStorageSync:()=>M()};function pt(e){return e&&pt(e.__v_raw)||e}function ut(){return{token:dt.getStorageSync("uni_id_token")||dt.getStorageSync("uniIdToken"),tokenExpired:dt.getStorageSync("uni_id_token_expired")}}function gt({token:e,tokenExpired:t}={}){e&&dt.setStorageSync("uni_id_token",e),t&&dt.setStorageSync("uni_id_token_expired",t)}let ft,xt;function mt(){return ft||(ft=F()),ft}function yt(){let e,t;try{if(W){if(W.toString().indexOf("not yet implemented")>-1)return;const{scene:i,channel:a}=W();e=a,t=i}}catch(i){}return{channel:e,scene:t}}function vt(){const e=U&&U()||"en";if(xt)return{...xt,locale:e,LOCALE:e};const t=mt(),{deviceId:i,osName:a,uniPlatform:o,appId:n}=t,r=["pixelRatio","brand","model","system","language","version","platform","host","SDKVersion","swanNativeVersion","app","AppPlatform","fontSizeSetting"];for(let s=0;s<r.length;s++)delete t[r[s]];return xt={PLATFORM:o,OS:a,APPID:n,DEVICEID:i,...yt(),...t},{...xt,locale:e,LOCALE:e}}var bt=function(e,t){let i="";return Object.keys(e).sort().forEach((function(t){e[t]&&(i=i+"&"+t+"="+e[t])})),i=i.slice(1),Te(i,t).toString()},St=function(e,t){return new Promise(((i,a)=>{t(Object.assign(e,{complete(e){e||(e={});const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400)return a(new ht({code:"SYS_ERR",message:e.errMsg||"request:fail",requestId:t}));const o=e.data;if(o.error)return a(new ht({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,i(o)}}))}))},wt=function(e){return Ae.stringify(_e.parse(e))},Tt=class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=dt,this._getAccessTokenPromiseHub=new Ee({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new ht({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:Oe})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return St(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,i)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?i(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=bt(t.data,this.config.clientSecret),t}setupRequest(e,t){const i=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),a={"Content-Type":"application/json"};return"auth"!==t&&(i.token=this.accessToken,a["x-basement-token"]=this.accessToken),a["x-serverless-sign"]=bt(i,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:i,dataType:"json",header:a}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(this.setupRequest(t))}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:i,filePath:a,fileType:o,onUploadProgress:n}){return new Promise(((r,s)=>{const l=this.adapter.uploadFile({url:e,formData:t,name:i,filePath:a,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?r(e):s(new ht({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){s(new ht({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof n&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((e=>{n({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:i="image",cloudPathAsRealPath:a=!1,onUploadProgress:o,config:n}){if("string"!==De(t))throw new ht({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ht({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ht({code:"INVALID_PARAM",message:"cloudPath不合法"});const r=n&&n.envType||this.config.envType;if(a&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new ht({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const s=(await this.getOSSUploadOptionsFromPath({env:r,filename:a?t.split("/").pop():t,fileId:a?t:void 0})).result,l="https://"+s.cdnDomain+"/"+s.ossPath,{securityToken:c,accessKeyId:h,signature:d,host:p,ossPath:u,id:g,policy:f,ossCallbackUrl:x}=s,m={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:h,Signature:d,host:p,id:g,key:u,policy:f,success_action_status:200};if(c&&(m["x-oss-security-token"]=c),x){const e=JSON.stringify({callbackUrl:x,callbackBody:JSON.stringify({fileId:g,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});m.callback=wt(e)}const y={url:"https://"+s.host,formData:m,fileName:"file",name:"file",filePath:e,fileType:i};if(await this.uploadFileToOSS(Object.assign({},y,{onUploadProgress:o})),x)return{success:!0,filePath:e,fileID:l};if((await this.reportOSSUpload({id:g})).success)return{success:!0,filePath:e,fileID:l};throw new ht({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,i)=>{Array.isArray(e)&&0!==e.length||i(new ht({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new ht({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}},_t={init(e){const t=new Tt(e),i={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return i},t.customAuth=t.auth,t}};const At="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var Pt,Ct;(Ct=Pt||(Pt={})).local="local",Ct.none="none",Ct.session="session";var kt=function(){},Dt=be((function(e,t){var i;e.exports=(i=we,function(e){var t=i,a=t.lib,o=a.WordArray,n=a.Hasher,r=t.algo,s=[],l=[];!function(){function t(t){for(var i=e.sqrt(t),a=2;a<=i;a++)if(!(t%a))return!1;return!0}function i(e){return 4294967296*(e-(0|e))|0}for(var a=2,o=0;o<64;)t(a)&&(o<8&&(s[o]=i(e.pow(a,.5))),l[o]=i(e.pow(a,1/3)),o++),a++}();var c=[],h=r.SHA256=n.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(e,t){for(var i=this._hash.words,a=i[0],o=i[1],n=i[2],r=i[3],s=i[4],h=i[5],d=i[6],p=i[7],u=0;u<64;u++){if(u<16)c[u]=0|e[t+u];else{var g=c[u-15],f=(g<<25|g>>>7)^(g<<14|g>>>18)^g>>>3,x=c[u-2],m=(x<<15|x>>>17)^(x<<13|x>>>19)^x>>>10;c[u]=f+c[u-7]+m+c[u-16]}var y=a&o^a&n^o&n,v=(a<<30|a>>>2)^(a<<19|a>>>13)^(a<<10|a>>>22),b=p+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&h^~s&d)+l[u]+c[u];p=d,d=h,h=s,s=r+b|0,r=n,n=o,o=a,a=b+(v+y)|0}i[0]=i[0]+a|0,i[1]=i[1]+o|0,i[2]=i[2]+n|0,i[3]=i[3]+r|0,i[4]=i[4]+s|0,i[5]=i[5]+h|0,i[6]=i[6]+d|0,i[7]=i[7]+p|0},_doFinalize:function(){var t=this._data,i=t.words,a=8*this._nDataBytes,o=8*t.sigBytes;return i[o>>>5]|=128<<24-o%32,i[14+(o+64>>>9<<4)]=e.floor(a/4294967296),i[15+(o+64>>>9<<4)]=a,t.sigBytes=4*i.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=n._createHelper(h),t.HmacSHA256=n._createHmacHelper(h)}(Math),i.SHA256)})),Lt=Dt,It=be((function(e,t){e.exports=we.HmacSHA256}));const Mt=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new ht({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,i)=>{e=(e,a)=>e?i(e):t(a)}));return e.promise=t,e};function Ft(e){return void 0===e}function Ot(e){return"[object Null]"===Object.prototype.toString.call(e)}var Et;!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Et||(Et={}));const Rt={adapter:null,runtime:void 0},zt=["anonymousUuidKey"];class Bt extends kt{constructor(){super(),Rt.adapter.root.tcbObject||(Rt.adapter.root.tcbObject={})}setItem(e,t){Rt.adapter.root.tcbObject[e]=t}getItem(e){return Rt.adapter.root.tcbObject[e]}removeItem(e){delete Rt.adapter.root.tcbObject[e]}clear(){delete Rt.adapter.root.tcbObject}}function Nt(e,t){switch(e){case"local":return t.localStorage||new Bt;case"none":return new Bt;default:return t.sessionStorage||new Bt}}class Ut{constructor(e){if(!this._storage){this._persistence=Rt.adapter.primaryStorage||e.persistence,this._storage=Nt(this._persistence,Rt.adapter);const t=`access_token_${e.env}`,i=`access_token_expire_${e.env}`,a=`refresh_token_${e.env}`,o=`anonymous_uuid_${e.env}`,n=`login_type_${e.env}`,r=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:i,refreshTokenKey:a,anonymousUuidKey:o,loginTypeKey:n,userInfoKey:r}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const i=Nt(e,Rt.adapter);for(const a in this.keys){const e=this.keys[a];if(t&&zt.includes(a))continue;const o=this._storage.getItem(e);Ft(o)||Ot(o)||(i.setItem(e,o),this._storage.removeItem(e))}this._storage=i}setStore(e,t,i){if(!this._storage)return;const a={version:i||"localCachev1",content:t},o=JSON.stringify(a);try{this._storage.setItem(e,o)}catch(n){throw n}}getStore(e,t){try{if(!this._storage)return}catch(a){return""}t=t||"localCachev1";const i=this._storage.getItem(e);return i&&i.indexOf(t)>=0?JSON.parse(i).content:""}removeStore(e){this._storage.removeItem(e)}}const Wt={},jt={};function qt(e){return Wt[e]}class $t{constructor(e,t){this.data=t||null,this.name=e}}class Ht extends $t{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const Gt=new class{constructor(){this._listeners={}}on(e,t){return i=e,a=t,(o=this._listeners)[i]=o[i]||[],o[i].push(a),this;var i,a,o}off(e,t){return function(e,t,i){if(i&&i[e]){const a=i[e].indexOf(t);-1!==a&&i[e].splice(a,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Ht)return console.error(e.error),this;const i="string"==typeof e?new $t(e,t||{}):e,a=i.name;if(this._listens(a)){i.target=this;const e=this._listeners[a]?[...this._listeners[a]]:[];for(const t of e)t.call(this,i)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function Kt(e,t){Gt.on(e,t)}function Jt(e,t={}){Gt.fire(e,t)}function Xt(e,t){Gt.off(e,t)}const Vt="loginStateChanged",Yt="loginStateExpire",Qt="loginTypeChanged",Zt="anonymousConverted",ei="refreshAccessToken";var ti;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(ti||(ti={}));const ii=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],ai={"X-SDK-Version":"1.3.5"};function oi(e,t,i){const a=e[t];e[t]=function(t){const o={},n={};i.forEach((i=>{const{data:a,headers:r}=i.call(e,t);Object.assign(o,a),Object.assign(n,r)}));const r=t.data;return r&&(()=>{var e;if(e=r,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...r,...o};else for(const t in o)r.append(t,o[t])})(),t.headers={...t.headers||{},...n},a.call(e,t)}}function ni(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...ai,"x-seqid":e}}}class ri{constructor(e={}){var t;this.config=e,this._reqClass=new Rt.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=qt(this.config.env),this._localCache=(t=this.config.env,jt[t]),oi(this._reqClass,"post",[ni]),oi(this._reqClass,"upload",[ni]),oi(this._reqClass,"download",[ni])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(i){t=i}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:i,loginTypeKey:a,anonymousUuidKey:o}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let n=this._cache.getStore(i);if(!n)throw new ht({message:"未登录CloudBase"});const r={refresh_token:n},s=await this.request("auth.fetchAccessTokenWithRefreshToken",r);if(s.data.code){const{code:e}=s.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(a)===ti.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(o),t=this._cache.getStore(i),a=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(a.refresh_token),this._refreshAccessToken()}Jt(Yt),this._cache.removeStore(i)}throw new ht({code:s.data.code,message:`刷新access token失败：${s.data.code}`})}if(s.data.access_token)return Jt(ei),this._cache.setStore(e,s.data.access_token),this._cache.setStore(t,s.data.access_token_expire+Date.now()),{accessToken:s.data.access_token,accessTokenExpire:s.data.access_token_expire};s.data.refresh_token&&(this._cache.removeStore(i),this._cache.setStore(i,s.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:i}=this._cache.keys;if(!this._cache.getStore(i))throw new ht({message:"refresh token不存在，登录状态异常"});let a=this._cache.getStore(e),o=this._cache.getStore(t),n=!0;return this._shouldRefreshAccessTokenHook&&!(await this._shouldRefreshAccessTokenHook(a,o))&&(n=!1),(!a||!o||o<Date.now())&&n?this.refreshAccessToken():{accessToken:a,accessTokenExpire:o}}async request(e,t,i){const a=`x-tcb-trace_${this.config.env}`;let o="application/x-www-form-urlencoded";const n={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};if(-1===ii.indexOf(e)){const{refreshTokenKey:e}=this._cache.keys;this._cache.getStore(e)&&(n.access_token=(await this.getAccessToken()).accessToken)}let r;if("storage.uploadFile"===e){r=new FormData;for(let e in r)r.hasOwnProperty(e)&&void 0!==r[e]&&r.append(e,n[e]);o="multipart/form-data"}else{o="application/json",r={};for(let e in n)void 0!==n[e]&&(r[e]=n[e])}let s={headers:{"content-type":o}};i&&i.onUploadProgress&&(s.onUploadProgress=i.onUploadProgress);const l=this._localCache.getStore(a);l&&(s.headers["X-TCB-Trace"]=l);const{parse:c,inQuery:h,search:d}=t;let p={env:this.config.env};c&&(p.parse=!0),h&&(p={...h,...p});let u=function(e,t,i={}){const a=/\?/.test(t);let o="";for(let n in i)""===o?!a&&(t+="?"):o+="&",o+=`${n}=${encodeURIComponent(i[n])}`;return/^http(s)?\:\/\//.test(t+=o)?t:`${e}${t}`}(At,"//tcb-api.tencentcloudapi.com/web",p);d&&(u+=d);const g=await this.post({url:u,data:r,...s}),f=g.header&&g.header["x-tcb-trace"];if(f&&this._localCache.setStore(a,f),200!==Number(g.status)&&200!==Number(g.statusCode)||!g.data)throw new ht({code:"NETWORK_ERROR",message:"network request error"});return g}async send(e,t={}){const i=await this.request(e,t,{onUploadProgress:t.onUploadProgress});if("ACCESS_TOKEN_EXPIRED"===i.data.code&&-1===ii.indexOf(e)){await this.refreshAccessToken();const i=await this.request(e,t,{onUploadProgress:t.onUploadProgress});if(i.data.code)throw new ht({code:i.data.code,message:i.data.message});return i.data}if(i.data.code)throw new ht({code:i.data.code,message:i.data.message});return i.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:i,refreshTokenKey:a}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(i),this._cache.setStore(a,e)}}const si={};function li(e){return si[e]}class ci{constructor(e){this.config=e,this._cache=qt(e.env),this._request=li(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:i,refreshTokenKey:a}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(i),this._cache.setStore(a,e)}setAccessToken(e,t){const{accessTokenKey:i,accessTokenExpireKey:a}=this._cache.keys;this._cache.setStore(i,e),this._cache.setStore(a,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class hi{constructor(e){if(!e)throw new ht({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=qt(this._envId),this._request=li(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new ht({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new ht({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:i}=e;return i.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:i,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:i,avatarUrl:a,province:o,country:n,city:r}=e,{data:s}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:i,avatarUrl:a,province:o,country:n,city:r});this.setLocalUserInfo(s)}async refresh(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class di{constructor(e){if(!e)throw new ht({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=qt(e);const{refreshTokenKey:t,accessTokenKey:i,accessTokenExpireKey:a}=this._cache.keys,o=this._cache.getStore(t),n=this._cache.getStore(i),r=this._cache.getStore(a);this.credential={refreshToken:o,accessToken:n,accessTokenExpire:r},this.user=new hi(e)}get isAnonymousAuth(){return this.loginType===ti.ANONYMOUS}get isCustomAuth(){return this.loginType===ti.CUSTOM}get isWeixinAuth(){return this.loginType===ti.WECHAT||this.loginType===ti.WECHAT_OPEN||this.loginType===ti.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class pi extends ci{async signIn(){this._cache.updatePersistence("local");const{anonymousUuidKey:e,refreshTokenKey:t}=this._cache.keys,i=this._cache.getStore(e)||void 0,a=this._cache.getStore(t)||void 0,o=await this._request.send("auth.signInAnonymously",{anonymous_uuid:i,refresh_token:a});if(o.uuid&&o.refresh_token){this._setAnonymousUUID(o.uuid),this.setRefreshToken(o.refresh_token),await this._request.refreshAccessToken(),Jt(Vt),Jt(Qt,{env:this.config.env,loginType:ti.ANONYMOUS,persistence:"local"});const e=new di(this.config.env);return await e.user.refresh(),e}throw new ht({message:"匿名登录失败"})}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:i}=this._cache.keys,a=this._cache.getStore(t),o=this._cache.getStore(i),n=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:a,refresh_token:o,ticket:e});if(n.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),Jt(Zt,{env:this.config.env}),Jt(Qt,{loginType:ti.CUSTOM,persistence:"local"}),{credential:{refreshToken:n.refresh_token}};throw new ht({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:i}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(i,ti.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class ui extends ci{async signIn(e){if("string"!=typeof e)throw new ht({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,i=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(i.refresh_token)return this.setRefreshToken(i.refresh_token),await this._request.refreshAccessToken(),Jt(Vt),Jt(Qt,{env:this.config.env,loginType:ti.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new di(this.config.env);throw new ht({message:"自定义登录失败"})}}class gi extends ci{async signIn(e,t){if("string"!=typeof e)throw new ht({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:i}=this._cache.keys,a=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(i)||""}),{refresh_token:o,access_token:n,access_token_expire:r}=a;if(o)return this.setRefreshToken(o),n&&r?this.setAccessToken(n,r):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Jt(Vt),Jt(Qt,{env:this.config.env,loginType:ti.EMAIL,persistence:this.config.persistence}),new di(this.config.env);throw a.code?new ht({code:a.code,message:`邮箱登录失败: ${a.message}`}):new ht({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class fi extends ci{async signIn(e,t){if("string"!=typeof e)throw new ht({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:i}=this._cache.keys,a=await this._request.send("auth.signIn",{loginType:ti.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(i)||""}),{refresh_token:o,access_token_expire:n,access_token:r}=a;if(o)return this.setRefreshToken(o),r&&n?this.setAccessToken(r,n):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Jt(Vt),Jt(Qt,{env:this.config.env,loginType:ti.USERNAME,persistence:this.config.persistence}),new di(this.config.env);throw a.code?new ht({code:a.code,message:`用户名密码登录失败: ${a.message}`}):new ht({message:"用户名密码登录失败"})}}class xi{constructor(e){this.config=e,this._cache=qt(e.env),this._request=li(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Kt(Qt,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new pi(this.config)}customAuthProvider(){return new ui(this.config)}emailAuthProvider(){return new gi(this.config)}usernameAuthProvider(){return new fi(this.config)}async signInAnonymously(){return new pi(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new gi(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new fi(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new pi(this.config)),Kt(Zt,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===ti.ANONYMOUS)throw new ht({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:i}=this._cache.keys,a=this._cache.getStore(e);if(!a)return;const o=await this._request.send("auth.logout",{refresh_token:a});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(i),Jt(Vt),Jt(Qt,{env:this.config.env,loginType:ti.NULL,persistence:this.config.persistence}),o}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){Kt(Vt,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){Kt(Yt,e.bind(this))}onAccessTokenRefreshed(e){Kt(ei,e.bind(this))}onAnonymousConverted(e){Kt(Zt,e.bind(this))}onLoginTypeChanged(e){Kt(Qt,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{refreshTokenKey:e}=this._cache.keys;return this._cache.getStore(e)?new di(this.config.env):null}async isUsernameRegistered(e){if("string"!=typeof e)throw new ht({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new ui(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,i=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+i}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:i,env:a}=e.data;a===this.config.env&&(this._cache.updatePersistence(i),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const mi=function(e,t){t=t||Mt();const i=li(this.config.env),{cloudPath:a,filePath:o,onUploadProgress:n,fileType:r="image"}=e;return i.send("storage.getUploadMetadata",{path:a}).then((e=>{const{data:{url:s,authorization:l,token:c,fileId:h,cosFileId:d},requestId:p}=e,u={key:a,signature:l,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":c};i.upload({url:s,data:u,file:o,name:a,fileType:r,onUploadProgress:n}).then((e=>{201===e.statusCode?t(null,{fileID:h,requestId:p}):t(new ht({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},yi=function(e,t){t=t||Mt();const i=li(this.config.env),{cloudPath:a}=e;return i.send("storage.getUploadMetadata",{path:a}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},vi=function({fileList:e},t){if(t=t||Mt(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let a of e)if(!a||"string"!=typeof a)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const i={fileid_list:e};return li(this.config.env).send("storage.batchDeleteFile",i).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},bi=function({fileList:e},t){t=t||Mt(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let i=[];for(let o of e)"object"==typeof o?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),i.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?i.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const a={file_list:i};return li(this.config.env).send("storage.batchGetDownloadUrl",a).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},Si=async function({fileID:e},t){const i=(await bi.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==i.code)return t?t(i):new Promise((e=>{e(i)}));const a=li(this.config.env);let o=i.download_url;if(o=encodeURI(o),!t)return a.download({url:o});t(await a.download({url:o}))},wi=function({name:e,data:t,query:i,parse:a,search:o},n){const r=n||Mt();let s;try{s=t?JSON.stringify(t):""}catch(c){return Promise.reject(c)}if(!e)return Promise.reject(new ht({code:"PARAM_ERROR",message:"函数名不能为空"}));const l={inQuery:i,parse:a,search:o,function_name:e,request_data:s};return li(this.config.env).send("functions.invokeFunction",l).then((e=>{if(e.code)r(null,e);else{let i=e.data.response_data;if(a)r(null,{result:i,requestId:e.requestId});else try{i=JSON.parse(e.data.response_data),r(null,{result:i,requestId:e.requestId})}catch(t){r(new ht({message:"response data must be json"}))}}return r.promise})).catch((e=>{r(e)})),r.promise},Ti={timeout:15e3,persistence:"session"},_i={};class Ai{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(Rt.adapter||(this.requestClient=new Rt.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Ti,...e},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new Ai(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||Rt.adapter.primaryStorage||Ti.persistence;var i;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;Wt[t]=new Ut(e),jt[t]=new Ut({...e,persistence:"local"})}(this.config),i=this.config,si[i.env]=new ri(i),this.authObj=new xi(this.config),this.authObj}on(e,t){return Kt.apply(this,[e,t])}off(e,t){return Xt.apply(this,[e,t])}callFunction(e,t){return wi.apply(this,[e,t])}deleteFile(e,t){return vi.apply(this,[e,t])}getTempFileURL(e,t){return bi.apply(this,[e,t])}downloadFile(e,t){return Si.apply(this,[e,t])}uploadFile(e,t){return mi.apply(this,[e,t])}getUploadMetadata(e,t){return yi.apply(this,[e,t])}registerExtension(e){_i[e.name]=e}async invokeExtension(e,t){const i=_i[e];if(!i)throw new ht({message:`扩展${e} 必须先注册`});return await i.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:i}=function(e){const t=(i=e,"[object Array]"===Object.prototype.toString.call(i)?e:[e]);var i;for(const a of t){const{isMatch:e,genAdapter:t,runtime:i}=a;if(e())return{adapter:t(),runtime:i}}}(e)||{};t&&(Rt.adapter=t),i&&(Rt.runtime=i)}}var Pi=new Ai;function Ci(e,t,i){void 0===i&&(i={});var a=/\?/.test(t),o="";for(var n in i)""===o?!a&&(t+="?"):o+="&",o+=n+"="+encodeURIComponent(i[n]);return/^http(s)?:\/\//.test(t+=o)?t:""+e+t}class ki{post(e){const{url:t,data:i,headers:a}=e;return new Promise(((e,o)=>{dt.request({url:Ci("https:",t),data:i,method:"POST",header:a,success(t){e(t)},fail(e){o(e)}})}))}upload(e){return new Promise(((t,i)=>{const{url:a,file:o,data:n,headers:r,fileType:s}=e,l=dt.uploadFile({url:Ci("https:",a),name:"file",formData:Object.assign({},n),filePath:o,fileType:s,header:r,success(e){const i={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&n.success_action_status&&(i.statusCode=parseInt(n.success_action_status,10)),t(i)},fail(e){i(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const Di={setItem(e,t){dt.setStorageSync(e,t)},getItem:e=>dt.getStorageSync(e),removeItem(e){dt.removeStorageSync(e)},clear(){dt.clearStorageSync()}};var Li={genAdapter:function(){return{root:{},reqClass:ki,localStorage:Di,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Pi.useAdapters(Li);const Ii=Pi,Mi=Ii.init;Ii.init=function(e){e.env=e.spaceId;const t=Mi.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const i=t.auth;return t.auth=function(e){const t=i.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var i;t[e]=(i=t[e],function(e){e=e||{};const{success:t,fail:a,complete:o}=ct(e);if(!(t||a||o))return i.call(this,e);i.call(this,e).then((e=>{t&&t(e),o&&o(e)}),(e=>{a&&a(e),o&&o(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Fi=Ii,Oi=class extends Tt{getAccessToken(){return new Promise(((e,t)=>{const i="Anonymous_Access_token";this.setAccessToken(i),e(i)}))}setupRequest(e,t){const i=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),a={"Content-Type":"application/json"};"auth"!==t&&(i.token=this.accessToken,a["x-basement-token"]=this.accessToken),a["x-serverless-sign"]=bt(i,this.config.clientSecret);const o=vt();a["x-client-info"]=encodeURIComponent(JSON.stringify(o));const{token:n}=ut();return a["x-client-token"]=n,{url:this.config.requestUrl,method:"POST",data:i,dataType:"json",header:JSON.parse(JSON.stringify(a))}}uploadFileToOSS({url:e,formData:t,name:i,filePath:a,fileType:o,onUploadProgress:n}){return new Promise(((r,s)=>{const l=this.adapter.uploadFile({url:e,formData:t,name:i,filePath:a,fileType:o,success(e){e&&e.statusCode<400?r(e):s(new ht({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){s(new ht({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof n&&l&&"function"==typeof l.onProgressUpdate&&l.onProgressUpdate((e=>{n({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}uploadFile({filePath:e,cloudPath:t,fileType:i="image",onUploadProgress:a}){if(!t)throw new ht({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let o;return this.getOSSUploadOptionsFromPath({cloudPath:t}).then((t=>{const{url:n,formData:r,name:s}=t.result;o=t.result.fileUrl;const l={url:n,formData:r,name:s,filePath:e,fileType:i};return this.uploadFileToOSS(Object.assign({},l,{onUploadProgress:a}))})).then((()=>this.reportOSSUpload({cloudPath:t}))).then((t=>new Promise(((i,a)=>{t.success?i({success:!0,filePath:e,fileID:o}):a(new ht({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(this.setupRequest(t)).then((e=>{if(e.success)return e.result;throw new ht({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new ht({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const i={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(this.setupRequest(i)).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new ht({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}},Ei={init(e){const t=new Oi(e),i={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return i},t.customAuth=t.auth,t}},Ri=be((function(e,t){e.exports=we.enc.Hex}));function zi(e="",t={}){const{data:i,functionName:a,method:o,headers:n,signHeaderKeys:r=[],config:s}=t,l=Date.now(),c="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})),h=Object.assign({},n,{"x-from-app-id":s.spaceAppId,"x-from-env-id":s.spaceId,"x-to-env-id":s.spaceId,"x-from-instance-id":l,"x-from-function-name":a,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":c,"x-alipay-callid":c,"x-trace-id":c}),d=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(r),[p="",u=""]=e.split("?")||[],g=function(e){const t=e.signedHeaders.join(";"),i=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),a=Lt(e.body).toString(Ri),o=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${i}\n${t}\n${a}\n`,n=Lt(o).toString(Ri),r=`HMAC-SHA256\n${e.timestamp}\n${n}\n`,s=It(r,e.secretKey).toString(Ri);return`HMAC-SHA256 Credential=${e.secretId}, SignedHeaders=${t}, Signature=${s}`}({path:p,query:u,method:o,headers:h,timestamp:l,body:JSON.stringify(i),secretId:s.accessKey,secretKey:s.secretKey,signedHeaders:d.sort()});return{url:`${s.endpoint}${e}`,headers:Object.assign({},h,{Authorization:g})}}function Bi({url:e,data:t,method:i="POST",headers:a={}}){return new Promise(((o,n)=>{dt.request({url:e,method:i,data:t,header:a,dataType:"json",complete:(e={})=>{const t=a["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:i,errMsg:a,trace_id:o}=e.data||{};return n(new ht({code:"SYS_ERR",message:i||a||"request:fail",requestId:o||t}))}o({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Ni(e,t){const{path:i,data:a,method:o="GET"}=e,{url:n,headers:r}=zi(i,{functionName:"",data:a,method:o,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Bi({url:n,data:a,method:o,headers:r}).then((e=>{const t=e.data||{};if(!t.success)throw new ht({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new ht({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Ui(e=""){const t=e.trim().replace(/^cloud:\/\//,""),i=t.indexOf("/");if(i<=0)throw new ht({code:"INVALID_PARAM",message:"fileID不合法"});const a=t.substring(0,i),o=t.substring(i+1);return a!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),o}function Wi(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var ji={init:e=>{e.provider="alipay";const t=new class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`})}callFunction(e){return function(e,t){const{name:i,data:a}=e,o="POST",{url:n,headers:r}=zi("/functions/invokeFunction",{functionName:i,data:a,method:o,headers:{"x-to-function-name":i},signHeaderKeys:["x-to-function-name"],config:t});return Bi({url:n,data:a,method:o,headers:r}).then((e=>({errCode:0,success:!0,requestId:e.requestId,result:e.data}))).catch((e=>{throw new ht({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:i,formData:a,onUploadProgress:o}){return new Promise(((n,r)=>{const s=dt.uploadFile({url:e,filePath:t,fileType:i,formData:a,name:"file",success(e){e&&e.statusCode<400?n(e):r(new ht({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){r(new ht({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((e=>{o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:i="image",onUploadProgress:a}){if("string"!==De(t))throw new ht({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ht({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ht({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=await Ni({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:n,upload_url:r,form_data:s}=o,l=s&&s.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:r,filePath:e,fileType:i,formData:l,onUploadProgress:a}).then((()=>({fileID:n})))}async getTempFileURL({fileList:e}){return new Promise(((t,i)=>{(!e||e.length<0)&&i(new ht({errCode:"INVALID_PARAM",errMsg:"fileList不能为空数组"})),e.length>50&&i(new ht({errCode:"INVALID_PARAM",errMsg:"fileList数组长度不能超过50"}));const a=[];for(const o of e){"string"!==De(o)&&i(new ht({errCode:"INVALID_PARAM",errMsg:"fileList的元素必须是非空的字符串"}));const e=Ui.call(this,o);a.push({file_id:e,expire:600})}Ni({path:"/?download_url",data:{file_list:a},method:"POST"},this.config).then((e=>{const{file_list:i=[]}=e;t({fileList:i.map((e=>({fileID:Wi.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>i(e)))}))}}(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function qi({data:e}){let t;t=vt();const i=JSON.parse(JSON.stringify(e||{}));if(Object.assign(i,{clientInfo:t}),!i.uniIdToken){const{token:e}=ut();e&&(i.uniIdToken=e)}return i}const $i=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var Hi=/[\\^$.*+?()[\]{}|]/g,Gi=RegExp(Hi.source);function Ki(e,t,i){return e.replace(new RegExp((a=t)&&Gi.test(a)?a.replace(Hi,"\\$&"):a,"g"),i);var a}const Ji=2e4,Xi={code:20101,message:"Invalid client"};function Vi(e){const{errSubject:t,subject:i,errCode:a,errMsg:o,code:n,message:r,cause:s}=e||{};return new ht({subject:t||i||"uni-secure-network",code:a||n||Ji,message:o||r,cause:s})}let Yi;function Qi({secretType:e}={}){return"request"===e||"response"===e||"both"===e}function Zi({functionName:e,result:t,logPvd:i}){}function ea(e){const t=e.callFunction,i=function(i){const a=i.name;i.data=qi.call(e,{data:i.data});const o={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay"}[this.config.provider],n=Qi(i)||false;return t.call(this,i).then((e=>(e.errCode=0,!n&&Zi.call(this,{functionName:a,result:e,logPvd:o}),Promise.resolve(e))),(e=>(!n&&Zi.call(this,{functionName:a,result:e,logPvd:o}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:i=[]}={}){for(let a=0;a<i.length;a++){const{rule:o,content:n,mode:r}=i[a],s=e.match(o);if(!s)continue;let l=n;for(let e=1;e<s.length;e++)l=Ki(l,`{$${e}}`,s[e]);for(const e in t)l=Ki(l,`{${e}}`,t[e]);return"replace"===r?l:e+l}return e}({message:`[${i.name}]: ${e.message}`,formatter:$i,extraInfo:{functionName:a}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:a,spaceId:o}=e.config,n=t.name;let r,s;return t.data=t.data||{},r=i,r=r.bind(e),s=Qi(t)?new Yi({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(i.bind(e))(t):function({provider:e,spaceId:t,functionName:i}={}){const{appId:a,uniPlatform:o,osName:n}=mt();let r=o;"app"===o&&(r=n);const s=function({provider:e,spaceId:t}={}){if(!ze)return{};var i;e="tencent"===(i=e)?"tcb":i;const a=ze.find((i=>i.provider===e&&i.spaceId===t));return a&&a.config}({provider:e,spaceId:t});if(!s||!s.accessControl||!s.accessControl.enable)return!1;const l=s.accessControl.function||{},c=Object.keys(l);if(0===c.length)return!0;const h=function(e,t){let i,a,o;for(let n=0;n<e.length;n++){const r=e[n];r!==t?"*"!==r?r.split(",").map((e=>e.trim())).indexOf(t)>-1&&(a=r):o=r:i=r}return i||a||o}(c,i);if(!h)return!1;if((l[h]||[]).find(((e={})=>e.appId===a&&(e.platform||"").toLowerCase()===r.toLowerCase())))return!0;throw console.error(`此应用[appId: ${a}, platform: ${r}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),Vi(Xi)}({provider:a,spaceId:o,functionName:n})?new Yi({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(i.bind(e))(t):r(t),Object.defineProperty(s,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),s}}Yi=class{constructor(){throw Vi({message:"Platform web is not supported by secure network"})}};const ta=Symbol("CLIENT_DB_INTERNAL");function ia(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=ta,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,i,a){if("_uniClient"===i)return null;if("symbol"==typeof i)return e[i];if(i in e||"string"!=typeof i){const t=e[i];return"function"==typeof t?t.bind(e):t}return t.get(e,i,a)}})}function aa(e){return{on:(t,i)=>{e[t]=e[t]||[],e[t].indexOf(i)>-1||e[t].push(i)},off:(t,i)=>{e[t]=e[t]||[];const a=e[t].indexOf(i);-1!==a&&e[t].splice(a,1)}}}const oa=["db.Geo","db.command","command.aggregate"];function na(e,t){return oa.indexOf(`${e}.${t}`)>-1}function ra(e){switch(De(e=pt(e))){case"array":return e.map((e=>ra(e)));case"object":return e._internalType===ta||Object.keys(e).forEach((t=>{e[t]=ra(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function sa(e){return e&&e.content&&e.content.$method}class la{constructor(e,t,i){this.content=e,this.prevStage=t||null,this.udb=null,this._database=i}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:ra(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=sa(e),i=sa(e.prevStage);if("aggregate"===t&&"collection"===i||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===sa(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=sa(e),i=sa(e.prevStage);if("aggregate"===t&&"command"===i)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return ca({$method:e,$param:ra(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const i=this.getAction(),a=this.getCommand();return a.$db.push({$method:e,$param:ra(t)}),this._database._callCloudFunction({action:i,command:a})}}function ca(e,t,i){return ia(new la(e,t,i),{get(e,t){let a="db";return e&&e.content&&(a=e.content.$method),na(a,t)?ca({$method:t},e,i):function(){return ca({$method:t,$param:ra(Array.from(arguments))},e,i)}}})}function ha({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function da(e,t={}){return ia(new e(t),{get:(e,t)=>na("db",t)?ca({$method:t},null,e):function(){return ca({$method:t,$param:ra(Array.from(arguments))},null,e)}})}class pa extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=We("_globalUniCloudDatabaseCallback")),t||(this.auth=aa(this._authCallBacks)),this._isJQL=t,Object.assign(this,aa(this._dbCallBacks)),this.env=ia({},{get:(e,t)=>({$env:t})}),this.Geo=ia({},{get:(e,t)=>ha({path:["Geo"],method:t})}),this.serverDate=ha({path:[],method:"serverDate"}),this.RegExp=ha({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const i=this._dbCallBacks;i[e]&&i[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const i=this._authCallBacks;i[e]&&i[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),i=e.getCommand();if("getTemp"!==i.$db[i.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:i}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:i,queryList:a}){function o(e,t){if(i&&a)for(let i=0;i<a.length;i++){const o=a[i];o.udb&&"function"==typeof o.udb.setResult&&(t?o.udb.setResult(t):o.udb.setResult(e.result.dataList[i]))}}const n=this,r=this._isJQL?"databaseForJQL":"database";function s(e){return n._callback("error",[e]),Ge(Ke(r,"fail"),e).then((()=>Ge(Ke(r,"complete"),e))).then((()=>(o(null,e),nt(Ve,{type:Ze,content:e}),Promise.reject(e))))}const l=Ge(Ke(r,"invoke")),c=this._uniClient;return l.then((()=>c.callFunction({name:"DCloud-clientDB",type:"CLIENT_DB",data:{action:e,command:t,multiCommand:i}}))).then((e=>{const{code:t,message:i,token:a,tokenExpired:l,systemInfo:c=[]}=e.result;if(c)for(let o=0;o<c.length;o++){const{level:e,message:t,detail:i}=c[o],a=console[e]||console.log;let n="[System Info]"+t;i&&(n=`${n}\n详细信息：${i}`),a(n)}if(t)return s(new ht({code:t,message:i,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,a&&l&&(gt({token:a,tokenExpired:l}),this._callbackAuth("refreshToken",[{token:a,tokenExpired:l}]),this._callback("refreshToken",[{token:a,tokenExpired:l}]),nt(Qe,{token:a,tokenExpired:l}));const h=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let o=0;o<h.length;o++){const{prop:t,tips:i}=h[o];if(t in e.result){const a=e.result[t];Object.defineProperty(e.result,t,{get:()=>(console.warn(i),a)})}}return d=e,Ge(Ke(r,"success"),d).then((()=>Ge(Ke(r,"complete"),d))).then((()=>{o(d,null);const e=n._parseResult(d);return nt(Ve,{type:Ze,content:e}),Promise.resolve(e)}));var d}),(e=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),s(new ht({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId})))))}}const ua="token无效，跳转登录页面",ga="token过期，跳转登录页面",fa={TOKEN_INVALID_TOKEN_EXPIRED:ga,TOKEN_INVALID_INVALID_CLIENTID:ua,TOKEN_INVALID:ua,TOKEN_INVALID_WRONG_TOKEN:ua,TOKEN_INVALID_ANONYMOUS_USER:ua},xa={"uni-id-token-expired":ga,"uni-id-check-token-failed":ua,"uni-id-token-not-exist":ua,"uni-id-check-device-feature-failed":ua};function ma(e,t){let i="";return i=e?`${e}/${t}`:t,i.replace(/^\//,"")}function ya(e=[],t=""){const i=[],a=[];return e.forEach((e=>{!0===e.needLogin?i.push(ma(t,e.path)):!1===e.needLogin&&a.push(ma(t,e.path))})),{needLoginPage:i,notNeedLoginPage:a}}function va(e){return e.split("?")[0].replace(/^\//,"")}function ba(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=y();return e[e.length-1]}())}function Sa(){return va(ba())}function wa(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const i=t.list,a=va(e);return i.some((e=>e.pagePath===a))}const Ta=!!ve.uniIdRouter,{loginPage:_a,routerNeedLogin:Aa,resToLogin:Pa,needLoginPage:Ca,notNeedLoginPage:ka,loginPageInTabBar:Da}=function({pages:e=[],subPackages:t=[],uniIdRouter:i={},tabBar:a={}}=ve){const{loginPage:o,needLogin:n=[],resToLogin:r=!0}=i,{needLoginPage:s,notNeedLoginPage:l}=ya(e),{needLoginPage:c,notNeedLoginPage:h}=function(e=[]){const t=[],i=[];return e.forEach((e=>{const{root:a,pages:o=[]}=e,{needLoginPage:n,notNeedLoginPage:r}=ya(o,a);t.push(...n),i.push(...r)})),{needLoginPage:t,notNeedLoginPage:i}}(t);return{loginPage:o,routerNeedLogin:n,resToLogin:r,needLoginPage:[...s,...c],notNeedLoginPage:[...l,...h],loginPageInTabBar:wa(o,a)}}();if(Ca.indexOf(_a)>-1)throw new Error(`Login page [${_a}] should not be "needLogin", please check your pages.json`);function La(e){const t=Sa();if("/"===e.charAt(0))return e;const[i,a]=e.split("?"),o=i.replace(/^\//,"").split("/"),n=t.split("/");n.pop();for(let r=0;r<o.length;r++){const e=o[r];".."===e?n.pop():"."!==e&&n.push(e)}return""===n[0]&&n.shift(),"/"+n.join("/")+(a?"?"+a:"")}function Ia({redirect:e}){const t=va(e),i=va(_a);return Sa()!==i&&t!==i}function Ma({api:e,redirect:t}={}){if(!t||!Ia({redirect:t}))return;const i=(o=t,"/"!==(a=_a).charAt(0)&&(a="/"+a),o?a.indexOf("?")>-1?a+`&uniIdRedirectUrl=${encodeURIComponent(o)}`:a+`?uniIdRedirectUrl=${encodeURIComponent(o)}`:a);var a,o;Da?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const n={navigateTo:O,redirectTo:E,switchTab:R,reLaunch:z};setTimeout((()=>{n[e]({url:i})}))}function Fa({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},i=function(){const{token:e,tokenExpired:t}=ut();let i;if(e){if(t<Date.now()){const e="uni-id-token-expired";i={errCode:e,errMsg:xa[e]}}}else{const e="uni-id-check-token-failed";i={errCode:e,errMsg:xa[e]}}return i}();if(function(e){const t=va(La(e));return!(ka.indexOf(t)>-1)&&(Ca.indexOf(t)>-1||Aa.some((t=>{return i=e,new RegExp(t).test(i);var i})))}(e)&&i){if(i.uniIdRedirectUrl=e,it(Ye).length>0)return setTimeout((()=>{nt(Ye,i)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Oa(){!function(){const e=ba(),{abortLoginPageJump:t,autoToLoginPage:i}=Fa({url:e});t||i&&Ma({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const i=e[t];v(i,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:a}=Fa({url:e.url});return t?e:a?(Ma({api:i,redirect:La(e.url)}),!1):e}})}}function Ea(){this.onResponse((e=>{const{type:t,content:i}=e;let a=!1;switch(t){case"cloudobject":a=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in xa}(i);break;case"clientdb":a=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in fa}(i)}a&&function(e={}){const t=it(Ye);lt().then((()=>{const i=ba();if(i&&Ia({redirect:i}))return t.length>0?nt(Ye,Object.assign({uniIdRedirectUrl:i},e)):void(_a&&Ma({api:"navigateTo",redirect:i}))}))}(i)}))}function Ra(e){var t;(t=e).onResponse=function(e){at(Ve,e)},t.offResponse=function(e){ot(Ve,e)},function(e){e.onNeedLogin=function(e){at(Ye,e)},e.offNeedLogin=function(e){ot(Ye,e)},Ta&&(We("_globalUniCloudStatus").needLoginInit||(We("_globalUniCloudStatus").needLoginInit=!0,lt().then((()=>{Oa.call(e)})),Pa&&Ea.call(e)))}(e),function(e){e.onRefreshToken=function(e){at(Qe,e)},e.offRefreshToken=function(e){ot(Qe,e)}}(e)}let za;const Ba="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Na=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Ua(){const e=ut().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let i;try{i=JSON.parse((a=t[1],decodeURIComponent(za(a).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}var a;return i.tokenExpired=1e3*i.exp,delete i.exp,delete i.iat,i}za="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Na.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var i,a,o="",n=0;n<e.length;)t=Ba.indexOf(e.charAt(n++))<<18|Ba.indexOf(e.charAt(n++))<<12|(i=Ba.indexOf(e.charAt(n++)))<<6|(a=Ba.indexOf(e.charAt(n++))),o+=64===i?String.fromCharCode(t>>16&255):64===a?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return o}:atob;var Wa=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(be((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const i="chooseAndUploadFile:ok",a="chooseAndUploadFile:fail";function o(e,t){return e.tempFiles.forEach(((e,i)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+i+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function n(e,t,{onChooseFile:a,onUploadProgress:o}){return t.then((e=>{if(a){const t=a(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:i,tempFilePaths:[],tempFiles:[]}:function(e,t,a=5,o){(t=Object.assign({},t)).errMsg=i;const n=t.tempFiles,r=n.length;let s=0;return new Promise((i=>{for(;s<a;)l();function l(){const a=s++;if(a>=r)return void(!n.find((e=>!e.url&&!e.errMsg))&&i(t));const c=n[a];e.uploadFile({filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress(e){e.index=a,e.tempFile=c,e.tempFilePath=c.path,o&&o(e)}}).then((e=>{c.url=e.fileID,a<r&&l()})).catch((e=>{c.errMsg=e.errMsg||e.message,a<r&&l()}))}}))}(e,t,5,o)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?n(e,function(e){const{count:t,sizeType:i,sourceType:n=["album","camera"],extension:r}=e;return new Promise(((e,s)=>{x({count:t,sizeType:i,sourceType:n,extension:r,success(t){e(o(t,"image"))},fail(e){s({errMsg:e.errMsg.replace("chooseImage:fail",a)})}})}))}(t),t):"video"===t.type?n(e,function(e){const{camera:t,compressed:i,maxDuration:n,sourceType:r=["album","camera"],extension:s}=e;return new Promise(((e,l)=>{m({camera:t,compressed:i,maxDuration:n,sourceType:r,extension:s,success(t){const{tempFilePath:i,duration:a,size:n,height:r,width:s}=t;e(o({errMsg:"chooseVideo:ok",tempFilePaths:[i],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:i,size:n,type:t.tempFile&&t.tempFile.type||"",width:s,height:r,duration:a,fileType:"video",cloudPath:""}]},"video"))},fail(e){l({errMsg:e.errMsg.replace("chooseVideo:fail",a)})}})}))}(t),t):n(e,function(e){const{count:t,extension:i}=e;return new Promise(((e,n)=>{let r=b;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(r=wx.chooseMessageFile),"function"!=typeof r)return n({errMsg:a+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});r({type:"all",count:t,extension:i,success(t){e(o(t))},fail(e){n({errMsg:e.errMsg.replace("chooseFile:fail",a)})}})}))}(t),t)}}})));function ja(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{}}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if("manual"===this.loadtime)return;let i=!1;const a=[];for(let o=2;o<e.length;o++)e[o]!==t[o]&&(a.push(e[o]),i=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(i,a)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:i}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomGet().then((i=>{this.mixinDatacomLoading=!1;const{data:a,count:o}=i.result;this.getcount&&(this.mixinDatacomPage.count=o),this.mixinDatacomHasMore=a.length<this.pageSize;const n=e?a.length?a[0]:void 0:a;this.mixinDatacomResData=n,t&&t(n)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,i&&i(e)})))},mixinDatacomGet(t={}){let i=e.database(this.spaceInfo);const a=t.action||this.action;a&&(i=i.action(a));const o=t.collection||this.collection;i=Array.isArray(o)?i.collection(...o):i.collection(o);const n=t.where||this.where;n&&Object.keys(n).length&&(i=i.where(n));const r=t.field||this.field;r&&(i=i.field(r));const s=t.foreignKey||this.foreignKey;s&&(i=i.foreignKey(s));const l=t.groupby||this.groupby;l&&(i=i.groupBy(l));const c=t.groupField||this.groupField;c&&(i=i.groupField(c)),!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(i=i.distinct());const h=t.orderby||this.orderby;h&&(i=i.orderBy(h));const d=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,p=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,u=void 0!==t.getcount?t.getcount:this.getcount,g=void 0!==t.gettree?t.gettree:this.gettree,f=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,x={getCount:u},m={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return g&&(x.getTree=m),f&&(x.getTreePath=m),i=i.skip(p*(d-1)).limit(p).get(x),i}}}}function qa(e){return We("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function $a({openid:e,callLoginByWeixin:t=!1}={}){throw qa(this),new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `web`")}async function Ha(e){const t=qa(this);return t.initPromise||(t.initPromise=$a.call(this,e)),t.initPromise}function Ga(e){const t={getSystemInfo:B,getPushClientId:N};return function(i){return new Promise(((a,o)=>{t[e]({...i,success(e){a(e)},fail(e){o(e)}})}))}}class Ka extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const i=this._callback[e];if(!i)return;const a=function(e,t){for(let i=e.length-1;i>=0;i--)if(e[i]===t)return i;return-1}(i,t);i.splice(a,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const i=this._callback[e];if(i)for(let a=0;a<i.length;a++)i[a](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([Ga("getSystemInfo")(),Ga("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:i,messageId:a,message:o}=t;this._payloadQueue.push({action:i,messageId:a,message:o}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:i,message:a}=e;"end"===t?this._end({messageId:i,message:a}):"message"===t&&this._appendMessage({messageId:i,message:a})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){A(this._uniPushMessageCallback)}_destroy(){P(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}const Ja={tcb:Fi,tencent:Fi,aliyun:_t,private:Ei,alipay:ji};let Xa=new class{init(e){let t={};const i=Ja[e.provider];if(!i)throw new Error("未提供正确的provider参数");var a;return t=i.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new Ee({createPromise:function(){let t=Promise.resolve();t=new Promise((e=>{setTimeout((()=>{e()}),1)}));const i=e.auth();return t.then((()=>i.getLoginState())).then((e=>e?Promise.resolve():i.signInAnonymously()))}}))}(t),ea(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),(a=t).database=function(e){if(e&&Object.keys(e).length>0)return a.init(e).database();if(this._database)return this._database;const t=da(pa,{uniClient:a});return this._database=t,t},a.databaseForJQL=function(e){if(e&&Object.keys(e).length>0)return a.init(e).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const t=da(pa,{uniClient:a,isJQL:!0});return this._databaseForJQL=t,t},function(e){e.getCurrentUserInfo=Ua,e.chooseAndUploadFile=Wa.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return ja(e)}}),e.SSEChannel=Ka,e.initSecureNetworkByWeixin=function(e){return function({openid:t,callLoginByWeixin:i=!1}={}){return Ha.call(e,{openid:t,callLoginByWeixin:i})}}(e),e.importObject=function(t){return function(i,a={}){a=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},a);const{customUI:o,loadingOptions:n,errorOptions:r,parseSystemError:s}=a,l=!o;return new Proxy({},{get(o,c){switch(c){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:i}={}){return async function(...a){const o=i?i({params:a}):{};let n,r;try{return await Ge(Ke(t,"invoke"),{...o}),n=await e(...a),await Ge(Ke(t,"success"),{...o,result:n}),n}catch(s){throw r=s,await Ge(Ke(t,"fail"),{...o,error:r}),r}finally{await Ge(Ke(t,"complete"),r?{...o,error:r}:{...o,result:n})}}}({fn:async function o(...h){let d;l&&S({title:n.title,mask:n.mask});const p={name:i,type:"OBJECT",data:{method:c,params:h}};"object"==typeof a.secretMethods&&function(e,t){const i=t.data.method,a=e.secretMethods||{},o=a[i]||a["*"];o&&(t.secretType=o)}(a,p);let u=!1;try{d=await t.callFunction(p)}catch(e){u=!0,d={result:new ht(e)}}const{errSubject:g,errCode:f,errMsg:x,newToken:m}=d.result||{};if(l&&w(),m&&m.token&&m.tokenExpired&&(gt(m),nt(Qe,{...m})),f){let e=x;if(u&&s&&(e=(await s({objectName:i,methodName:c,params:h,errSubject:g,errCode:f,errMsg:x})).errMsg||x),l)if("toast"===r.type)T({title:e,icon:"none"});else{if("modal"!==r.type)throw new Error(`Invalid errorOptions.type: ${r.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:i,cancelText:a,confirmText:o}={}){return new Promise(((n,r)=>{_({title:e,content:t,showCancel:i,cancelText:a,confirmText:o,success(e){n(e)},fail(){n({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:r.retry,cancelText:"取消",confirmText:r.retry?"重试":"确定"});if(r.retry&&t)return o(...h)}}const t=new ht({subject:g,code:f,message:x,requestId:d.requestId});throw t.detail=d.result,nt(Ve,{type:tt,content:t}),t}return nt(Ve,{type:tt,content:d.result}),d.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:i,methodName:c,params:e}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const i=t[e];var a,o;t[e]=function(){return i.apply(t,Array.from(arguments))},t[e]=(a=t[e],o=e,function(e){let t=!1;if("callFunction"===o){const i=e&&e.type||Pe;t=i!==Pe}const i="callFunction"===o&&!t,n=this._initPromiseHub.exec();e=e||{};const{success:r,fail:s,complete:l}=ct(e),c=n.then((()=>t?Promise.resolve():Ge(Ke(o,"invoke"),e))).then((()=>a.call(this,e))).then((e=>t?Promise.resolve(e):Ge(Ke(o,"success"),e).then((()=>Ge(Ke(o,"complete"),e))).then((()=>(i&&nt(Ve,{type:et,content:e}),Promise.resolve(e))))),(e=>t?Promise.reject(e):Ge(Ke(o,"fail"),e).then((()=>Ge(Ke(o,"complete"),e))).then((()=>(nt(Ve,{type:et,content:e}),Promise.reject(e))))));if(!(r||s||l))return c;c.then((e=>{r&&r(e),l&&l(e),i&&nt(Ve,{type:et,content:e})}),(e=>{s&&s(e),l&&l(e),i&&nt(Ve,{type:et,content:e})}))}).bind(t)})),t.init=this.init,t}};(()=>{const e=Be;let t={};if(e&&1===e.length)t=e[0],Xa=Xa.init(t),Xa._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let i;i=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",t.forEach((e=>{Xa[e]=function(){return console.error(i),Promise.reject(new ht({code:"SYS_ERR",message:i}))}}))}Object.assign(Xa,{get mixinDatacom(){return ja(Xa)}}),Ra(Xa),Xa.addInterceptor=$e,Xa.removeInterceptor=He,Xa.interceptObject=Je})();var Va=Xa;const Ya=se({components:{Loading1:se({name:"loading1",data:()=>({})},[["render",function(e,t,i,a,o,c){const h=g;return n(),r(h,{class:"container loading1"},{default:s((()=>[l(h,{class:"shape shape1"}),l(h,{class:"shape shape2"}),l(h,{class:"shape shape3"}),l(h,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-76c74c2c"]]),Loading2:se({name:"loading2",data:()=>({})},[["render",function(e,t,i,a,o,c){const h=g;return n(),r(h,{class:"container loading2"},{default:s((()=>[l(h,{class:"shape shape1"}),l(h,{class:"shape shape2"}),l(h,{class:"shape shape3"}),l(h,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-3ac378a0"]]),Loading3:se({name:"loading3",data:()=>({})},[["render",function(e,t,i,a,o,c){const h=g;return n(),r(h,{class:"container loading3"},{default:s((()=>[l(h,{class:"shape shape1"}),l(h,{class:"shape shape2"}),l(h,{class:"shape shape3"}),l(h,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-1eae5fe9"]]),Loading4:se({name:"loading5",data:()=>({})},[["render",function(e,t,i,a,o,c){const h=g;return n(),r(h,{class:"container loading5"},{default:s((()=>[l(h,{class:"shape shape1"}),l(h,{class:"shape shape2"}),l(h,{class:"shape shape3"}),l(h,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-df582bd7"]]),Loading5:se({name:"loading6",data:()=>({})},[["render",function(e,t,i,a,o,c){const h=g;return n(),r(h,{class:"container loading6"},{default:s((()=>[l(h,{class:"shape shape1"}),l(h,{class:"shape shape2"}),l(h,{class:"shape shape3"}),l(h,{class:"shape shape4"})])),_:1})}],["__scopeId","data-v-148834aa"]])},name:"qiun-loading",props:{loadingType:{type:Number,default:2}},data:()=>({})},[["render",function(e,t,i,a,o,l){const c=j("Loading1"),d=j("Loading2"),p=j("Loading3"),u=j("Loading4"),f=j("Loading5"),x=g;return n(),r(x,null,{default:s((()=>[1==i.loadingType?(n(),r(c,{key:0})):h("",!0),2==i.loadingType?(n(),r(d,{key:1})):h("",!0),3==i.loadingType?(n(),r(p,{key:2})):h("",!0),4==i.loadingType?(n(),r(u,{key:3})):h("",!0),5==i.loadingType?(n(),r(f,{key:4})):h("",!0)])),_:1})}]]);const Qa=se({name:"qiun-error",props:{errorMessage:{type:String,default:null}},data:()=>({})},[["render",function(e,t,i,a,o,c){const h=g;return n(),r(h,{class:"chartsview"},{default:s((()=>[l(h,{class:"charts-error"}),l(h,{class:"charts-font"},{default:s((()=>[p(u(null==i.errorMessage?"请点击重试":i.errorMessage),1)])),_:1})])),_:1})}],["__scopeId","data-v-04fb35a8"]]);var Za={version:"v2.5.0-20230101",yAxisWidth:15,xAxisHeight:22,padding:[10,10,10,10],rotate:!1,fontSize:13,fontColor:"#666666",dataPointShape:["circle","circle","circle","circle"],color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],linearColor:["#0EE2F8","#2BDCA8","#FA7D8D","#EB88E2","#2AE3A0","#0EE2F8","#EB88E2","#6773E3","#F78A85"],pieChartLinePadding:15,pieChartTextPadding:5,titleFontSize:20,subtitleFontSize:15,radarLabelTextMargin:13},eo=function(e,...t){if(null==e)throw new TypeError("[uCharts] Cannot convert undefined or null to object");if(!t||t.length<=0)return e;function i(e,t){for(let a in t)e[a]=e[a]&&"[object Object]"===e[a].toString()?i(e[a],t[a]):e[a]=t[a];return e}return t.forEach((t=>{e=i(e,t)})),e},to={toFixed:function(e,t){return t=t||2,this.isFloat(e)&&(e=e.toFixed(t)),e},isFloat:function(e){return e%1!=0},approximatelyEqual:function(e,t){return Math.abs(e-t)<1e-10},isSameSign:function(e,t){return Math.abs(e)===e&&Math.abs(t)===t||Math.abs(e)!==e&&Math.abs(t)!==t},isSameXCoordinateArea:function(e,t){return this.isSameSign(e.x,t.x)},isCollision:function(e,t){return e.end={},e.end.x=e.start.x+e.width,e.end.y=e.start.y-e.height,t.end={},t.end.x=t.start.x+t.width,t.end.y=t.start.y-t.height,!(t.start.x>e.end.x||t.end.x<e.start.x||t.end.y>e.start.y||t.start.y<e.end.y)}};function io(e,t){var i=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(e,t,i,a){return t+t+i+i+a+a})),a=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(i);return"rgba("+parseInt(a[1],16)+","+parseInt(a[2],16)+","+parseInt(a[3],16)+","+t+")"}function ao(e,t,i){if(isNaN(e))throw new Error("[uCharts] series数据需为Number格式");i=i||10,t=t||"upper";for(var a=1;i<1;)i*=10,a*=10;for(e="upper"===t?Math.ceil(e*a):Math.floor(e*a);e%i!=0;)if("upper"===t){if(e==e+1)break;e++}else e--;return e/a}function oo(e,t,i,a,o){var n=o.width-o.area[1]-o.area[3],r=i.eachSpacing*(o.chartData.xAxisData.xAxisPoints.length-1);"mount"==o.type&&o.extra&&o.extra.mount&&o.extra.mount.widthRatio&&o.extra.mount.widthRatio>1&&(o.extra.mount.widthRatio>2&&(o.extra.mount.widthRatio=2),r+=(o.extra.mount.widthRatio-1)*i.eachSpacing);var s=t;return t>=0?(s=0,e.uevent.trigger("scrollLeft"),e.scrollOption.position="left",o.xAxis.scrollPosition="left"):Math.abs(t)>=r-n?(s=n-r,e.uevent.trigger("scrollRight"),e.scrollOption.position="right",o.xAxis.scrollPosition="right"):(e.scrollOption.position=t,o.xAxis.scrollPosition=t),s}function no(e,t,i){function a(e){for(;e<0;)e+=2*Math.PI;for(;e>2*Math.PI;)e-=2*Math.PI;return e}return e=a(e),(t=a(t))>(i=a(i))&&(i+=2*Math.PI,e<t&&(e+=2*Math.PI)),e>=t&&e<=i}function ro(e,t){function i(e,t){return!(!e[t-1]||!e[t+1])&&(e[t].y>=Math.max(e[t-1].y,e[t+1].y)||e[t].y<=Math.min(e[t-1].y,e[t+1].y))}function a(e,t){return!(!e[t-1]||!e[t+1])&&(e[t].x>=Math.max(e[t-1].x,e[t+1].x)||e[t].x<=Math.min(e[t-1].x,e[t+1].x))}var o=.2,n=.2,r=null,s=null,l=null,c=null;if(t<1?(r=e[0].x+(e[1].x-e[0].x)*o,s=e[0].y+(e[1].y-e[0].y)*o):(r=e[t].x+(e[t+1].x-e[t-1].x)*o,s=e[t].y+(e[t+1].y-e[t-1].y)*o),t>e.length-3){var h=e.length-1;l=e[h].x-(e[h].x-e[h-1].x)*n,c=e[h].y-(e[h].y-e[h-1].y)*n}else l=e[t+1].x-(e[t+2].x-e[t].x)*n,c=e[t+1].y-(e[t+2].y-e[t].y)*n;return i(e,t+1)&&(c=e[t+1].y),i(e,t)&&(s=e[t].y),a(e,t+1)&&(l=e[t+1].x),a(e,t)&&(r=e[t].x),(s>=Math.max(e[t].y,e[t+1].y)||s<=Math.min(e[t].y,e[t+1].y))&&(s=e[t].y),(c>=Math.max(e[t].y,e[t+1].y)||c<=Math.min(e[t].y,e[t+1].y))&&(c=e[t+1].y),(r>=Math.max(e[t].x,e[t+1].x)||r<=Math.min(e[t].x,e[t+1].x))&&(r=e[t].x),(l>=Math.max(e[t].x,e[t+1].x)||l<=Math.min(e[t].x,e[t+1].x))&&(l=e[t+1].x),{ctrA:{x:r,y:s},ctrB:{x:l,y:c}}}function so(e,t,i){return{x:i.x+e,y:i.y-t}}function lo(e,t){if(t)for(;to.isCollision(e,t);)e.start.x>0?e.start.y--:e.start.x<0||e.start.y>0?e.start.y++:e.start.y--;return e}function co(e,t,i){for(var a=0,o=0;o<e.length;o++){let n=e[o];if(n.color||(n.color=i.color[a],a=(a+1)%i.color.length),n.linearIndex||(n.linearIndex=o),n.index||(n.index=0),n.type||(n.type=t.type),void 0===n.show&&(n.show=!0),n.type||(n.type=t.type),n.pointShape||(n.pointShape="circle"),!n.legendShape)switch(n.type){case"line":n.legendShape="line";break;case"column":case"bar":n.legendShape="rect";break;case"area":case"mount":n.legendShape="triangle";break;default:n.legendShape="circle"}}return e}function ho(e,t,i,a){var o=t||[];if("custom"==e&&0==o.length&&(o=a.linearColor),"custom"==e&&o.length<i.length){let e=i.length-o.length;for(var n=0;n<e;n++)o.push(a.linearColor[(n+1)%a.linearColor.length])}return o}function po(e,t,i){var a=0;if(e=String(e),!1!==i&&void 0!==i&&i.setFontSize&&i.measureText)return i.setFontSize(t),i.measureText(e).width;e=e.split("");for(let o=0;o<e.length;o++){let t=e[o];/[a-zA-Z]/.test(t)?a+=7:/[0-9]/.test(t)?a+=5.5:/\./.test(t)?a+=2.7:/-/.test(t)?a+=3.25:/:/.test(t)?a+=2.5:/[\u4e00-\u9fa5]/.test(t)?a+=10:/\(|\)/.test(t)?a+=3.73:/\s/.test(t)?a+=2.5:/%/.test(t)?a+=8:a+=10}return a*t/10}function uo(e){return e.reduce((function(e,t){return(e.data?e.data:e).concat(t.data)}),[])}function go(e,t){for(var i=new Array(t),a=0;a<i.length;a++)i[a]=0;for(var o=0;o<e.length;o++)for(a=0;a<i.length;a++)i[a]+=e[o].data[a];return e.reduce((function(e,t){return(e.data?e.data:e).concat(t.data).concat(i)}),[])}function fo(e,t,i){let a,o;return e.clientX?t.rotate?(o=t.height-e.clientX*t.pix,a=(e.pageY-i.currentTarget.offsetTop-t.height/t.pix/2*(t.pix-1))*t.pix):(a=e.clientX*t.pix,o=(e.pageY-i.currentTarget.offsetTop-t.height/t.pix/2*(t.pix-1))*t.pix):t.rotate?(o=t.height-e.x*t.pix,a=e.y*t.pix):(a=e.x*t.pix,o=e.y*t.pix),{x:a,y:o}}function xo(e,t,i){var a=[],o=[],n=t.constructor.toString().indexOf("Array")>-1;if(n){let t=wo(e);for(var r=0;r<i.length;r++)o.push(t[i[r]])}else o=e;for(let s=0;s<o.length;s++){let e=o[s],i=-1;if(i=n?t[s]:t,null!==e.data[i]&&void 0!==e.data[i]&&e.show){let t={};t.color=e.color,t.type=e.type,t.style=e.style,t.pointShape=e.pointShape,t.disableLegend=e.disableLegend,t.legendShape=e.legendShape,t.name=e.name,t.show=e.show,t.data=e.formatter?e.formatter(e.data[i]):e.data[i],a.push(t)}}return a}function mo(e,t,i){var a=e.map((function(e){return po(e,t,i)}));return Math.max.apply(null,a)}function yo(e){for(var t=2*Math.PI/e,i=[],a=0;a<e;a++)i.push(t*a);return i.map((function(e){return-1*e+Math.PI/2}))}function vo(e,t,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},r=t.chartData.calPoints?t.chartData.calPoints:[];let s={};if(a.length>0){let e=[];for(let t=0;t<a.length;t++)e.push(r[a[t]]);s=e[0][i[0]]}else for(let h=0;h<r.length;h++)if(r[h][i]){s=r[h][i];break}var l=e.map((function(e){let a=null;return t.categories&&t.categories.length>0&&(a=o[i]),{text:n.formatter?n.formatter(e,a,i,t):e.name+": "+e.data,color:e.color,legendShape:"auto"==t.extra.tooltip.legendShape?e.legendShape:t.extra.tooltip.legendShape}})),c={x:Math.round(s.x),y:Math.round(s.y)};return{textList:l,offset:c}}function bo(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},n=t.chartData.xAxisPoints[i]+t.chartData.eachSpacing/2,r=e.map((function(e){return{text:o.formatter?o.formatter(e,a[i],i,t):e.name+": "+e.data,color:e.color,disableLegend:!!e.disableLegend,legendShape:"auto"==t.extra.tooltip.legendShape?e.legendShape:t.extra.tooltip.legendShape}}));r=r.filter((function(e){if(!0!==e.disableLegend)return e}));var s={x:Math.round(n),y:0};return{textList:r,offset:s}}function So(e,t,i,a,o,n){var r=i.chartData.calPoints;let s=n.color.upFill,l=n.color.downFill,c=[s,s,l,s];var h=[];t.map((function(t){0==a?t.data[1]-t.data[0]<0?c[1]=l:c[1]=s:(t.data[0]<e[a-1][1]&&(c[0]=l),t.data[1]<t.data[0]&&(c[1]=l),t.data[2]>e[a-1][1]&&(c[2]=s),t.data[3]<e[a-1][1]&&(c[3]=l));let o={text:"开盘："+t.data[0],color:c[0],legendShape:"auto"==i.extra.tooltip.legendShape?t.legendShape:i.extra.tooltip.legendShape},n={text:"收盘："+t.data[1],color:c[1],legendShape:"auto"==i.extra.tooltip.legendShape?t.legendShape:i.extra.tooltip.legendShape},r={text:"最低："+t.data[2],color:c[2],legendShape:"auto"==i.extra.tooltip.legendShape?t.legendShape:i.extra.tooltip.legendShape},d={text:"最高："+t.data[3],color:c[3],legendShape:"auto"==i.extra.tooltip.legendShape?t.legendShape:i.extra.tooltip.legendShape};h.push(o,n,r,d)}));var d=[],p={x:0,y:0};for(let u=0;u<r.length;u++){let e=r[u];void 0!==e[a]&&null!==e[a]&&d.push(e[a])}return p.x=Math.round(d[0][0].x),{textList:h,offset:p}}function wo(e){let t=[];for(let i=0;i<e.length;i++)1==e[i].show&&t.push(e[i]);return t}function To(e,t,i){return e.x<=t.width-t.area[1]+10&&e.x>=t.area[3]-10&&e.y>=t.area[0]&&e.y<=t.height-t.area[2]}function _o(e,t,i){return Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)<=Math.pow(i,2)}function Ao(e,t){var i=[],a=[];return e.forEach((function(e,o){t.connectNulls?null!==e&&a.push(e):null!==e?a.push(e):(a.length&&i.push(a),a=[])})),a.length&&i.push(a),i}function Po(e,t,i,a,o){var n={angle:0,xAxisHeight:t.xAxis.lineHeight*t.pix+t.xAxis.marginTop*t.pix},r=t.xAxis.fontSize*t.pix,s=e.map((function(e,i){var a=t.xAxis.formatter?t.xAxis.formatter(e,i,t):e;return po(String(a),r,o)})),l=Math.max.apply(this,s);if(1==t.xAxis.rotateLabel){n.angle=t.xAxis.rotateAngle*Math.PI/180;let e=t.xAxis.marginTop*t.pix*2+Math.abs(l*Math.sin(n.angle));e=e<r+t.xAxis.marginTop*t.pix*2?e+t.xAxis.marginTop*t.pix*2:e,n.xAxisHeight=e}return t.enableScroll&&t.xAxis.scrollShow&&(n.xAxisHeight+=6*t.pix),t.xAxis.disabled&&(n.xAxisHeight=0),n}function Co(e,t,i,a){var o=eo({},{type:""},t.extra.bar),n={angle:0,xAxisHeight:t.xAxis.lineHeight*t.pix+t.xAxis.marginTop*t.pix};n.ranges=function(e,t,i,a){var o,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-1;o="stack"==a?go(e,t.categories.length):uo(e);var r=[];(o=o.filter((function(e){return"object"==typeof e&&null!==e?e.constructor.toString().indexOf("Array")>-1?null!==e:null!==e.value:null!==e}))).map((function(e){"object"==typeof e?e.constructor.toString().indexOf("Array")>-1?"candle"==t.type?e.map((function(e){r.push(e)})):r.push(e[0]):r.push(e.value):r.push(e)}));var s=0,l=0;if(r.length>0&&(s=Math.min.apply(this,r),l=Math.max.apply(this,r)),n>-1?("number"==typeof t.xAxis.data[n].min&&(s=Math.min(t.xAxis.data[n].min,s)),"number"==typeof t.xAxis.data[n].max&&(l=Math.max(t.xAxis.data[n].max,l))):("number"==typeof t.xAxis.min&&(s=Math.min(t.xAxis.min,s)),"number"==typeof t.xAxis.max&&(l=Math.max(t.xAxis.max,l))),s===l){l+=l||10}for(var c=s,h=[],d=(l-c)/t.xAxis.splitNumber,p=0;p<=t.xAxis.splitNumber;p++)h.push(c+d*p);return h}(e,t,i,o.type),n.rangesFormat=n.ranges.map((function(e){return e=to.toFixed(e,2)}));var r=n.ranges.map((function(e){return e=to.toFixed(e,2)}));return(n=Object.assign(n,Wo(r,t))).eachSpacing,r.map((function(e){return po(e,t.xAxis.fontSize*t.pix,a)})),!0===t.xAxis.disabled&&(n.xAxisHeight=0),n}function ko(e,t,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=o.extra.radar||{};r.max=r.max||0;var s=Math.max(r.max,Math.max.apply(null,uo(a))),l=[];for(let c=0;c<a.length;c++){let o=a[c],r={};r.color=o.color,r.legendShape=o.legendShape,r.pointShape=o.pointShape,r.data=[],o.data.forEach((function(a,o){let l={};l.angle=e[o],l.proportion=a/s,l.value=a,l.position=so(i*l.proportion*n*Math.cos(l.angle),i*l.proportion*n*Math.sin(l.angle),t),r.data.push(l)})),l.push(r)}return l}function Do(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,a=0,o=0;for(let n=0;n<e.length;n++){let t=e[n];t.data=null===t.data?0:t.data,a+=t.data}for(let n=0;n<e.length;n++){let o=e[n];o.data=null===o.data?0:o.data,o._proportion_=0===a?1/e.length*i:o.data/a*i,o._radius_=t}for(let n=0;n<e.length;n++){let t=e[n];t._start_=o,o+=2*t._proportion_*Math.PI}return e}function Lo(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;for(let n=0;n<e.length;n++)"funnel"==i.type?e[n].radius=e[n].data/e[0].data*t*o:e[n].radius=a*(e.length-n)/(a*e.length)*t*o,e[n]._proportion_=e[n].data/e[0].data;return e}function Io(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=0,r=0,s=[];for(let d=0;d<e.length;d++){let t=e[d];t.data=null===t.data?0:t.data,n+=t.data,s.push(t.data)}var l=Math.min.apply(null,s),c=Math.max.apply(null,s),h=a-i;for(let d=0;d<e.length;d++){let r=e[d];r.data=null===r.data?0:r.data,0===n?(r._proportion_=1/e.length*o,r._rose_proportion_=1/e.length*o):(r._proportion_=r.data/n*o,r._rose_proportion_="area"==t?1/e.length*o:r.data/n*o),r._radius_=i+h*((r.data-l)/(c-l))||a}for(let d=0;d<e.length;d++){let t=e[d];t._start_=r,r+=2*t._rose_proportion_*Math.PI}return e}function Mo(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==i&&(i=.999999);for(let a=0;a<e.length;a++){let o,n=e[a];n.data=null===n.data?0:n.data,o="circle"==t.type?2:"ccw"==t.direction?t.startAngle<t.endAngle?2+t.startAngle-t.endAngle:t.startAngle-t.endAngle:t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle,n._proportion_=o*n.data*i+t.startAngle,"ccw"==t.direction&&(n._proportion_=t.startAngle-o*n.data*i),n._proportion_>=2&&(n._proportion_=n._proportion_%2)}return e}function Fo(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;1==i&&(i=.999999);for(let a=0;a<e.length;a++){let o,n=e[a];n.data=null===n.data?0:n.data,o="circle"==t.type?2:t.endAngle<t.startAngle?2+t.endAngle-t.startAngle:t.startAngle-t.endAngle,n._proportion_=o*n.data*i+t.startAngle,n._proportion_>=2&&(n._proportion_=n._proportion_%2)}return e}function Oo(e,t,i){let a;a=i<t?2+i-t:t-i;let o=t;for(let n=0;n<e.length;n++)e[n].value=null===e[n].value?0:e[n].value,e[n]._startAngle_=o,e[n]._endAngle_=a*e[n].value+t,e[n]._endAngle_>=2&&(e[n]._endAngle_=e[n]._endAngle_%2),o=e[n]._endAngle_;return e}function Eo(e,t,i){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;for(let o=0;o<e.length;o++){let n,r=e[o];if(r.data=null===r.data?0:r.data,"auto"==i.pointer.color){for(let e=0;e<t.length;e++)if(r.data<=t[e].value){r.color=t[e].color;break}}else r.color=i.pointer.color;n=i.endAngle<i.startAngle?2+i.endAngle-i.startAngle:i.startAngle-i.endAngle,r._endAngle_=n*r.data+i.startAngle,r._oldAngle_=i.oldAngle,i.oldAngle<i.endAngle&&(r._oldAngle_+=2),r.data>=i.oldData?r._proportion_=(r._endAngle_-r._oldAngle_)*a+i.oldAngle:r._proportion_=r._oldAngle_-(r._oldAngle_-r._endAngle_)*a,r._proportion_>=2&&(r._proportion_=r._proportion_%2)}return e}function Ro(e,t,i,a,o,n){return e.map((function(e){if(null===e)return null;var o=0,r=0;return"mix"==n.type?(o=n.extra.mix.column.seriesGap*n.pix||0,r=n.extra.mix.column.categoryGap*n.pix||0):(o=n.extra.column.seriesGap*n.pix||0,r=n.extra.column.categoryGap*n.pix||0),o=Math.min(o,t/i),r=Math.min(r,t/i),e.width=Math.ceil((t-2*r-o*(i-1))/i),n.extra.mix&&n.extra.mix.column.width&&+n.extra.mix.column.width>0&&(e.width=Math.min(e.width,+n.extra.mix.column.width*n.pix)),n.extra.column&&n.extra.column.width&&+n.extra.column.width>0&&(e.width=Math.min(e.width,+n.extra.column.width*n.pix)),e.width<=0&&(e.width=1),e.x+=(a+.5-i/2)*(e.width+o),e}))}function zo(e,t,i,a,o,n){return e.map((function(e){if(null===e)return null;var o=0,r=0;return o=n.extra.bar.seriesGap*n.pix||0,r=n.extra.bar.categoryGap*n.pix||0,o=Math.min(o,t/i),r=Math.min(r,t/i),e.width=Math.ceil((t-2*r-o*(i-1))/i),n.extra.bar&&n.extra.bar.width&&+n.extra.bar.width>0&&(e.width=Math.min(e.width,+n.extra.bar.width*n.pix)),e.width<=0&&(e.width=1),e.y+=(a+.5-i/2)*(e.width+o),e}))}function Bo(e,t,i,a,o,n,r){var s=n.extra.column.categoryGap*n.pix||0;return e.map((function(e){return null===e?null:(e.width=t-2*s,n.extra.column&&n.extra.column.width&&+n.extra.column.width>0&&(e.width=Math.min(e.width,+n.extra.column.width*n.pix)),a>0&&(e.width-=r),e)}))}function No(e,t,i,a,o,n,r){var s=n.extra.column.categoryGap*n.pix||0;return e.map((function(e,i){return null===e?null:(e.width=Math.ceil(t-2*s),n.extra.column&&n.extra.column.width&&+n.extra.column.width>0&&(e.width=Math.min(e.width,+n.extra.column.width*n.pix)),e.width<=0&&(e.width=1),e)}))}function Uo(e,t,i,a,o,n,r){var s=n.extra.bar.categoryGap*n.pix||0;return e.map((function(e,i){return null===e?null:(e.width=Math.ceil(t-2*s),n.extra.bar&&n.extra.bar.width&&+n.extra.bar.width>0&&(e.width=Math.min(e.width,+n.extra.bar.width*n.pix)),e.width<=0&&(e.width=1),e)}))}function Wo(e,t,i){var a=t.width-t.area[1]-t.area[3],o=t.enableScroll?Math.min(t.xAxis.itemCount,e.length):e.length;("line"==t.type||"area"==t.type||"scatter"==t.type||"bubble"==t.type||"bar"==t.type)&&o>1&&"justify"==t.xAxis.boundaryGap&&(o-=1);var n=0;"mount"==t.type&&t.extra&&t.extra.mount&&t.extra.mount.widthRatio&&t.extra.mount.widthRatio>1&&(t.extra.mount.widthRatio>2&&(t.extra.mount.widthRatio=2),o+=n=t.extra.mount.widthRatio-1);var r=a/o,s=[],l=t.area[3],c=t.width-t.area[1];return e.forEach((function(e,t){s.push(l+n/2*r+t*r)})),"justify"!==t.xAxis.boundaryGap&&(!0===t.enableScroll?s.push(l+n*r+e.length*r):s.push(c)),{xAxisPoints:s,startX:l,endX:c,eachSpacing:r}}function jo(e,t,i,a,o,n,r){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l=[],c=n.height-n.area[0]-n.area[2];return e.forEach((function(e,r){if(null===e)l.push(null);else{var h=[];e.forEach((function(e,l){var d={};d.x=a[r]+Math.round(o/2);var p=e.value||e,u=c*(p-t)/(i-t);u*=s,d.y=n.height-Math.round(u)-n.area[2],h.push(d)})),l.push(h)}})),l}function qo(e,t,i,a,o,n,r){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l="center";"line"!=n.type&&"area"!=n.type&&"scatter"!=n.type&&"bubble"!=n.type||(l=n.xAxis.boundaryGap);var c=[],h=n.height-n.area[0]-n.area[2],d=n.width-n.area[1]-n.area[3];return e.forEach((function(e,r){if(null===e)c.push(null);else{var p={};p.color=e.color,p.x=a[r];var u=e;if("object"==typeof e&&null!==e)if(e.constructor.toString().indexOf("Array")>-1){let t,i,a;t=[].concat(n.chartData.xAxisData.ranges),i=t.shift(),a=t.pop(),u=e[1],p.x=n.area[3]+d*(e[0]-i)/(a-i),"bubble"==n.type&&(p.r=e[2],p.t=e[3])}else u=e.value;"center"==l&&(p.x+=o/2);var g=h*(u-t)/(i-t);g*=s,p.y=n.height-g-n.area[2],c.push(p)}})),c}function $o(e,t,i,a,o,n,r,s,l){l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=n.xAxis.boundaryGap,h=[],d=n.height-n.area[0]-n.area[2],p=n.width-n.area[1]-n.area[3];return e.forEach((function(e,r){if(null===e)h.push(null);else{var u={};if(u.color=e.color,"vertical"==s.animation){u.x=a[r];var g=e;if("object"==typeof e&&null!==e)if(e.constructor.toString().indexOf("Array")>-1){let t,i,a;t=[].concat(n.chartData.xAxisData.ranges),i=t.shift(),a=t.pop(),g=e[1],u.x=n.area[3]+p*(e[0]-i)/(a-i)}else g=e.value;"center"==c&&(u.x+=o/2);var f=d*(g-t)/(i-t);f*=l,u.y=n.height-f-n.area[2],h.push(u)}else{u.x=a[0]+o*r*l;g=e;"center"==c&&(u.x+=o/2);f=d*(g-t)/(i-t);u.y=n.height-f-n.area[2],h.push(u)}}})),h}function Ho(e,t,i,a,o,n,r,s,l){l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1;var c=[],h=n.height-n.area[0]-n.area[2],d=n.width-n.area[1]-n.area[3];return e.forEach((function(e,r){if(null===e)c.push(null);else{var s={};s.color=e.color,s.x=a[r];var p=e;if("object"==typeof e&&null!==e)if(e.constructor.toString().indexOf("Array")>-1){let t,i,a;t=[].concat(n.chartData.xAxisData.ranges),i=t.shift(),a=t.pop(),p=e[1],s.x=n.area[3]+d*(e[0]-i)/(a-i)}else p=e.value;s.x+=o/2;var u=h*(p*l-t)/(i-t);s.y=n.height-u-n.area[2],c.push(s)}})),c}function Go(e,t,i,a,o,n,r,s){var l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:1,c=[],h=n.height-n.area[0]-n.area[2];n.width,n.area[1],n.area[3];var d=o*r.widthRatio;return e.forEach((function(e,r){if(null===e)c.push(null);else{var s={};s.color=e.color,s.x=a[r],s.x+=o/2;var p=e.data,u=h*(p*l-t)/(i-t);s.y=n.height-u-n.area[2],s.value=p,s.width=d,c.push(s)}})),c}function Ko(e,t,i,a,o,n,r){var s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:1,l=[];n.height,n.area[0],n.area[2];var c=n.width-n.area[1]-n.area[3];return e.forEach((function(e,o){if(null===e)l.push(null);else{var r={};r.color=e.color,r.y=a[o];var h=e;"object"==typeof e&&null!==e&&(h=e.value);var d=c*(h-t)/(i-t);d*=s,r.height=d,r.value=h,r.x=d+n.area[3],l.push(r)}})),l}function Jo(e,t,i,a,o,n,r,s,l){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],d=n.height-n.area[0]-n.area[2];return e.forEach((function(e,r){if(null===e)h.push(null);else{var p={};if(p.color=e.color,p.x=a[r]+Math.round(o/2),s>0){var u=0;for(let e=0;e<=s;e++)u+=l[e].data[r];var g=d*(u-t)/(i-t),f=d*(u-e-t)/(i-t)}else{u=e;"object"==typeof e&&null!==e&&(u=e.value);g=d*(u-t)/(i-t),f=0}var x=f;g*=c,x*=c,p.y=n.height-Math.round(g)-n.area[2],p.y0=n.height-Math.round(x)-n.area[2],h.push(p)}})),h}function Xo(e,t,i,a,o,n,r,s,l){var c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:1,h=[],d=n.width-n.area[1]-n.area[3];return e.forEach((function(e,o){if(null===e)h.push(null);else{var r={};if(r.color=e.color,r.y=a[o],s>0){var p=0;for(let e=0;e<=s;e++)p+=l[e].data[o];var u=d*(p-t)/(i-t),g=d*(p-e-t)/(i-t)}else{p=e;"object"==typeof e&&null!==e&&(p=e.value);u=d*(p-t)/(i-t),g=0}var f=g;u*=c,f*=c,r.height=u-f,r.x=n.area[3]+u,r.x0=n.area[3]+f,h.push(r)}})),h}function Vo(e,t,i,a,o){var n;n="stack"==a?go(e,t.categories.length):uo(e);var r=[];(n=n.filter((function(e){return"object"==typeof e&&null!==e?e.constructor.toString().indexOf("Array")>-1?null!==e:null!==e.value:null!==e}))).map((function(e){"object"==typeof e?e.constructor.toString().indexOf("Array")>-1?"candle"==t.type?e.map((function(e){r.push(e)})):r.push(e[1]):r.push(e.value):r.push(e)}));var s=o.min||0,l=o.max||0;r.length>0&&(s=Math.min.apply(this,r),l=Math.max.apply(this,r)),s===l&&(0==l?l=10:s=0);for(var c=function(e,t){var i=0,a=t-e;return{minRange:ao(e,"lower",i=a>=1e4?1e3:a>=1e3?100:a>=100?10:a>=10?5:a>=1?1:a>=.1?.1:a>=.01?.01:a>=.001?.001:a>=1e-4?1e-4:a>=1e-5?1e-5:1e-6),maxRange:ao(t,"upper",i)}}(s,l),h=void 0===o.min||null===o.min?c.minRange:o.min,d=((void 0===o.max||null===o.max?c.maxRange:o.max)-h)/t.yAxis.splitNumber,p=[],u=0;u<=t.yAxis.splitNumber;u++)p.push(h+d*u);return p.reverse()}function Yo(e,t,i,a){var o=eo({},{type:""},t.extra.column),n=t.yAxis.data.length,r=new Array(n);if(n>0){for(let t=0;t<n;t++){r[t]=[];for(let i=0;i<e.length;i++)e[i].index==t&&r[t].push(e[i])}var s=new Array(n),l=new Array(n),c=new Array(n);for(let e=0;e<n;e++){let n=t.yAxis.data[e];1==t.yAxis.disabled&&(n.disabled=!0),"categories"===n.type?(n.formatter||(n.formatter=(e,t,i)=>e+(n.unit||"")),n.categories=n.categories||t.categories,s[e]=n.categories):(n.formatter||(n.formatter=(e,t,i)=>to.toFixed(e,n.tofix||0)+(n.unit||"")),s[e]=Vo(r[e],t,0,o.type,n));let h=n.fontSize*t.pix||i.fontSize;c[e]={position:n.position?n.position:"left",width:0},l[e]=s[e].map((function(i,o){return i=n.formatter(i,o,t),c[e].width=Math.max(c[e].width,po(i,h,a)+5),i}));let d=n.calibration?4*t.pix:0;c[e].width+=d+3*t.pix,!0===n.disabled&&(c[e].width=0)}}else{s=new Array(1),l=new Array(1),c=new Array(1);"bar"===t.type?(s[0]=t.categories,t.yAxis.formatter||(t.yAxis.formatter=(e,t,i)=>e+(i.yAxis.unit||""))):(t.yAxis.formatter||(t.yAxis.formatter=(e,t,i)=>e.toFixed(i.yAxis.tofix)+(i.yAxis.unit||"")),s[0]=Vo(e,t,0,o.type,{})),c[0]={position:"left",width:0};var h=t.yAxis.fontSize*t.pix||i.fontSize;l[0]=s[0].map((function(e,i){return e=t.yAxis.formatter(e,i,t),c[0].width=Math.max(c[0].width,po(e,h,a)+5),e})),c[0].width+=3*t.pix,!0===t.yAxis.disabled?(c[0]={position:"left",width:0},t.yAxis.data[0]={disabled:!0}):(t.yAxis.data[0]={disabled:!1,position:"left",max:t.yAxis.max,min:t.yAxis.min,formatter:t.yAxis.formatter},"bar"===t.type&&(t.yAxis.data[0].categories=t.categories,t.yAxis.data[0].type="categories"))}return{rangesFormat:l,ranges:s,yAxisWidth:c}}function Qo(e,t){!0!==t.rotateLock?(e.translate(t.height,0),e.rotate(90*Math.PI/180)):!0!==t._rotate_&&(e.translate(t.height,0),e.rotate(90*Math.PI/180),t._rotate_=!0)}function Zo(e,t,i,a,o){if(a.beginPath(),"hollow"==o.dataPointShapeType?(a.setStrokeStyle(t),a.setFillStyle(o.background),a.setLineWidth(2*o.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(t),a.setLineWidth(1*o.pix)),"diamond"===i)e.forEach((function(e,t){null!==e&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y),a.lineTo(e.x,e.y****),a.lineTo(e.x****,e.y),a.lineTo(e.x,e.y-4.5))}));else if("circle"===i)e.forEach((function(e,t){null!==e&&(a.moveTo(e.x*****o.pix,e.y),a.arc(e.x,e.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===i)e.forEach((function(e,t){null!==e&&(a.moveTo(e.x-3.5,e.y-3.5),a.rect(e.x-3.5,e.y-3.5,7,7))}));else if("triangle"===i)e.forEach((function(e,t){null!==e&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y****),a.lineTo(e.x****,e.y****),a.lineTo(e.x,e.y-4.5))}));else if("none"===i)return;a.closePath(),a.fill(),a.stroke()}function en(e,t,i,a,o,n,r){if(o.tooltip&&!(o.tooltip.group.length>0&&0==o.tooltip.group.includes(r))){var s="number"==typeof o.tooltip.index?o.tooltip.index:o.tooltip.index[o.tooltip.group.indexOf(r)];if(a.beginPath(),"hollow"==n.activeType?(a.setStrokeStyle(t),a.setFillStyle(o.background),a.setLineWidth(2*o.pix)):(a.setStrokeStyle("#ffffff"),a.setFillStyle(t),a.setLineWidth(1*o.pix)),"diamond"===i)e.forEach((function(e,t){null!==e&&s==t&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y),a.lineTo(e.x,e.y****),a.lineTo(e.x****,e.y),a.lineTo(e.x,e.y-4.5))}));else if("circle"===i)e.forEach((function(e,t){null!==e&&s==t&&(a.moveTo(e.x*****o.pix,e.y),a.arc(e.x,e.y,3*o.pix,0,2*Math.PI,!1))}));else if("square"===i)e.forEach((function(e,t){null!==e&&s==t&&(a.moveTo(e.x-3.5,e.y-3.5),a.rect(e.x-3.5,e.y-3.5,7,7))}));else if("triangle"===i)e.forEach((function(e,t){null!==e&&s==t&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y****),a.lineTo(e.x****,e.y****),a.lineTo(e.x,e.y-4.5))}));else if("none"===i)return;a.closePath(),a.fill(),a.stroke()}}function tn(e,t,i,a){var o=e.title.fontSize||t.titleFontSize,n=e.subtitle.fontSize||t.subtitleFontSize,r=e.title.name||"",s=e.subtitle.name||"",l=e.title.color||e.fontColor,c=e.subtitle.color||e.fontColor,h=r?o:0,d=s?n:0;if(s){var p=po(s,n*e.pix,i),u=a.x-p/2+(e.subtitle.offsetX||0)*e.pix,g=a.y+n*e.pix/2+(e.subtitle.offsetY||0)*e.pix;r&&(g+=(h*e.pix+5)/2),i.beginPath(),i.setFontSize(n*e.pix),i.setFillStyle(c),i.fillText(s,u,g),i.closePath(),i.stroke()}if(r){var f=po(r,o*e.pix,i),x=a.x-f/2+(e.title.offsetX||0),m=a.y+o*e.pix/2+(e.title.offsetY||0)*e.pix;s&&(m-=(d*e.pix+5)/2),i.beginPath(),i.setFontSize(o*e.pix),i.setFillStyle(l),i.fillText(r,x,m),i.closePath(),i.stroke()}}function an(e,t,i,a,o){var n=t.data,r=t.textOffset?t.textOffset:0;e.forEach((function(e,s){if(null!==e){a.beginPath();var l=t.textSize?t.textSize*o.pix:i.fontSize;a.setFontSize(l),a.setFillStyle(t.textColor||o.fontColor);var c=n[s];"object"==typeof n[s]&&null!==n[s]&&(c=n[s].constructor.toString().indexOf("Array")>-1?n[s][1]:n[s].value);var h=t.formatter?t.formatter(c,s,t,o):c;a.setTextAlign("center"),a.fillText(String(h),e.x,e.y-4+r*o.pix),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function on(e,t,i,a,o){var n=t.data,r=t.textOffset?t.textOffset:0,s=o.extra.column.labelPosition;e.forEach((function(e,l){if(null!==e){a.beginPath();var c=t.textSize?t.textSize*o.pix:i.fontSize;a.setFontSize(c),a.setFillStyle(t.textColor||o.fontColor);var h=n[l];"object"==typeof n[l]&&null!==n[l]&&(h=n[l].constructor.toString().indexOf("Array")>-1?n[l][1]:n[l].value);var d=t.formatter?t.formatter(h,l,t,o):h;a.setTextAlign("center");var p=e.y-4*o.pix+r*o.pix;e.y>t.zeroPoints&&(p=e.y+r*o.pix+c),"insideTop"==s&&(p=e.y+c+r*o.pix,e.y>t.zeroPoints&&(p=e.y-r*o.pix-4*o.pix)),"center"==s&&(p=e.y+r*o.pix+(o.height-o.area[2]-e.y+c)/2,t.zeroPoints<o.height-o.area[2]&&(p=e.y+r*o.pix+(t.zeroPoints-e.y+c)/2),e.y>t.zeroPoints&&(p=e.y-r*o.pix-(e.y-t.zeroPoints-c)/2),"stack"==o.extra.column.type&&(p=e.y+r*o.pix+(e.y0-e.y+c)/2)),"bottom"==s&&(p=o.height-o.area[2]+r*o.pix-4*o.pix,t.zeroPoints<o.height-o.area[2]&&(p=t.zeroPoints+r*o.pix-4*o.pix),e.y>t.zeroPoints&&(p=t.zeroPoints-r*o.pix+c+2*o.pix),"stack"==o.extra.column.type&&(p=e.y0+r*o.pix-4*o.pix)),a.fillText(String(d),e.x,p),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function nn(e,t,i,a,o,n){t.data;var r=t.textOffset?t.textOffset:0;o.extra.mount.labelPosition,e.forEach((function(e,s){if(null!==e){a.beginPath();var l=t[s].textSize?t[s].textSize*o.pix:i.fontSize;a.setFontSize(l),a.setFillStyle(t[s].textColor||o.fontColor);var c=e.value,h=t[s].formatter?t[s].formatter(c,s,t,o):c;a.setTextAlign("center");var d=e.y-4*o.pix+r*o.pix;e.y>n&&(d=e.y+r*o.pix+l),a.fillText(String(h),e.x,d),a.closePath(),a.stroke(),a.setTextAlign("left")}}))}function rn(e,t,i,a,o){var n=t.data;t.textOffset&&t.textOffset,e.forEach((function(e,r){if(null!==e){a.beginPath();var s=t.textSize?t.textSize*o.pix:i.fontSize;a.setFontSize(s),a.setFillStyle(t.textColor||o.fontColor);var l=n[r];"object"==typeof n[r]&&null!==n[r]&&(l=n[r].value);var c=t.formatter?t.formatter(l,r,t,o):l;a.setTextAlign("left"),a.fillText(String(c),e.x+4*o.pix,e.y+s/2-3),a.closePath(),a.stroke()}}))}function sn(e,t,i,a,o,n){let r;t=(t-=e.width/2+e.labelOffset*a.pix)<10?10:t,r=e.endAngle<e.startAngle?2+e.endAngle-e.startAngle:e.startAngle-e.endAngle;let s=r/e.splitLine.splitNumber,l=(e.endNumber-e.startNumber)/e.splitLine.splitNumber,c=e.startAngle,h=e.startNumber;for(let f=0;f<e.splitLine.splitNumber+1;f++){var d={x:t*Math.cos(c*Math.PI),y:t*Math.sin(c*Math.PI)},p=e.formatter?e.formatter(h,f,a):h;d.x+=i.x-po(p,o.fontSize,n)/2,d.y+=i.y;var u=d.x,g=d.y;n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(e.labelColor||a.fontColor),n.fillText(p,u,g+o.fontSize/2),n.closePath(),n.stroke(),c+=s,c>=2&&(c%=2),h+=l}}function ln(e,t,i,a,o,n){var r=a.extra.radar||{};e.forEach((function(e,s){if(!0===r.labelPointShow&&""!==a.categories[s]){var l={x:t*Math.cos(e),y:t*Math.sin(e)},c=so(l.x,l.y,i);n.setFillStyle(r.labelPointColor),n.beginPath(),n.arc(c.x,c.y,r.labelPointRadius*a.pix,0,2*Math.PI,!1),n.closePath(),n.fill()}if(!0===r.labelShow){var h={x:(t+o.radarLabelTextMargin*a.pix)*Math.cos(e),y:(t+o.radarLabelTextMargin*a.pix)*Math.sin(e)},d=so(h.x,h.y,i),p=d.x,u=d.y;to.approximatelyEqual(h.x,0)?p-=po(a.categories[s]||"",o.fontSize,n)/2:h.x<0&&(p-=po(a.categories[s]||"",o.fontSize,n)),n.beginPath(),n.setFontSize(o.fontSize),n.setFillStyle(r.labelColor||a.fontColor),n.fillText(a.categories[s]||"",p,u+o.fontSize/2),n.closePath(),n.stroke()}}))}function cn(e,t,i,a,o,n){var r=i.pieChartLinePadding,s=[],l=null,c=e.map((function(i,a){var o=i.formatter?i.formatter(i,a,e,t):to.toFixed(100*i._proportion_.toFixed(4))+"%";o=i.labelText?i.labelText:o;var n=2*Math.PI-(i._start_+2*Math.PI*i._proportion_/2);return i._rose_proportion_&&(n=2*Math.PI-(i._start_+2*Math.PI*i._rose_proportion_/2)),{arc:n,text:o,color:i.color,radius:i._radius_,textColor:i.textColor,textSize:i.textSize,labelShow:i.labelShow}}));for(let h=0;h<c.length;h++){let e=c[h],o=Math.cos(e.arc)*(e.radius+r),n=Math.sin(e.arc)*(e.radius+r),d=Math.cos(e.arc)*e.radius,p=Math.sin(e.arc)*e.radius,u=o>=0?o+i.pieChartTextPadding:o-i.pieChartTextPadding,g=n,f=po(e.text,e.textSize*t.pix||i.fontSize,a),x=g;l&&to.isSameXCoordinateArea(l.start,{x:u})&&(x=u>0?Math.min(g,l.start.y):o<0||g>0?Math.max(g,l.start.y):Math.min(g,l.start.y)),u<0&&(u-=f),l=lo({lineStart:{x:d,y:p},lineEnd:{x:o,y:n},start:{x:u,y:x},width:f,height:i.fontSize,text:e.text,color:e.color,textColor:e.textColor,textSize:e.textSize},l),s.push(l)}for(let h=0;h<s.length;h++){if(!1===c[h].labelShow)continue;let e=s[h],o=so(e.lineStart.x,e.lineStart.y,n),r=so(e.lineEnd.x,e.lineEnd.y,n),l=so(e.start.x,e.start.y,n);a.setLineWidth(1*t.pix),a.setFontSize(e.textSize*t.pix||i.fontSize),a.beginPath(),a.setStrokeStyle(e.color),a.setFillStyle(e.color),a.moveTo(o.x,o.y);let d=e.start.x<0?l.x+e.width:l.x,p=e.start.x<0?l.x-5:l.x+5;a.quadraticCurveTo(r.x,r.y,d,l.y),a.moveTo(o.x,o.y),a.stroke(),a.closePath(),a.beginPath(),a.moveTo(l.x+e.width,l.y),a.arc(d,l.y,2*t.pix,0,2*Math.PI),a.closePath(),a.fill(),a.beginPath(),a.setFontSize(e.textSize*t.pix||i.fontSize),a.setFillStyle(e.textColor||t.fontColor),a.fillText(e.text,p,l.y+3),a.closePath(),a.stroke(),a.closePath()}}function hn(e,t,i){let a=eo({},{type:"solid",dashLength:4,data:[]},e.extra.markLine),o=e.area[3],n=e.width-e.area[1],r=function(e,t){let i,a,o=t.height-t.area[0]-t.area[2];for(let n=0;n<e.length;n++){e[n].yAxisIndex=e[n].yAxisIndex?e[n].yAxisIndex:0;let r=[].concat(t.chartData.yAxisData.ranges[e[n].yAxisIndex]);i=r.pop(),a=r.shift();let s=o*(e[n].value-i)/(a-i);e[n].y=t.height-Math.round(s)-t.area[2]}return e}(a.data,e);for(let s=0;s<r.length;s++){let t=eo({},{lineColor:"#DE4A42",showLabel:!1,labelFontSize:13,labelPadding:6,labelFontColor:"#666666",labelBgColor:"#DFE8FF",labelBgOpacity:.8,labelAlign:"left",labelOffsetX:0,labelOffsetY:0},r[s]);if("dash"==a.type&&i.setLineDash([a.dashLength,a.dashLength]),i.setStrokeStyle(t.lineColor),i.setLineWidth(1*e.pix),i.beginPath(),i.moveTo(o,t.y),i.lineTo(n,t.y),i.stroke(),i.setLineDash([]),t.showLabel){let a=t.labelFontSize*e.pix,o=t.labelText?t.labelText:t.value;i.setFontSize(a);let n=po(o,a,i)+t.labelPadding*e.pix*2,r="left"==t.labelAlign?e.area[3]-n:e.width-e.area[1];r+=t.labelOffsetX;let s=t.y-.5*a-t.labelPadding*e.pix;s+=t.labelOffsetY;let l=r+t.labelPadding*e.pix;t.y,i.setFillStyle(io(t.labelBgColor,t.labelBgOpacity)),i.setStrokeStyle(t.labelBgColor),i.setLineWidth(1*e.pix),i.beginPath(),i.rect(r,s,n,a+2*t.labelPadding*e.pix),i.closePath(),i.stroke(),i.fill(),i.setFontSize(a),i.setTextAlign("left"),i.setFillStyle(t.labelFontColor),i.fillText(String(o),l,s+a+t.labelPadding*e.pix/2),i.stroke(),i.setTextAlign("left")}}}function dn(e,t,i,a,o){var n=eo({},{gridType:"solid",dashLength:4},e.extra.tooltip),r=e.area[3],s=e.width-e.area[1];if("dash"==n.gridType&&i.setLineDash([n.dashLength,n.dashLength]),i.setStrokeStyle(n.gridColor||"#cccccc"),i.setLineWidth(1*e.pix),i.beginPath(),i.moveTo(r,e.tooltip.offset.y),i.lineTo(s,e.tooltip.offset.y),i.stroke(),i.setLineDash([]),n.yAxisLabel){let a=n.boxPadding*e.pix,o=function(e,t,i,a,o){let n=[].concat(i.chartData.yAxisData.ranges),r=i.height-i.area[0]-i.area[2],s=i.area[0],l=[];for(let c=0;c<n.length;c++){let t=Math.max.apply(this,n[c]),a=t-(t-Math.min.apply(this,n[c]))*(e-s)/r;a=i.yAxis.data&&i.yAxis.data[c].formatter?i.yAxis.data[c].formatter(a,c,i):a.toFixed(0),l.push(String(a))}return l}(e.tooltip.offset.y,e.series,e),r=e.chartData.yAxisData.yAxisWidth,s=e.area[3],l=e.width-e.area[1];for(let c=0;c<o.length;c++){i.setFontSize(n.fontSize*e.pix);let h,d,p,u=po(o[c],n.fontSize*e.pix,i);"left"==r[c].position?(h=s-(u+2*a)-2*e.pix,d=Math.max(h,h+u+2*a)):(h=l+2*e.pix,d=Math.max(h+r[c].width,h+u+2*a)),p=d-h;let g=h+(p-u)/2,f=e.tooltip.offset.y;i.beginPath(),i.setFillStyle(io(n.labelBgColor||t.toolTipBackground,n.labelBgOpacity||t.toolTipOpacity)),i.setStrokeStyle(n.labelBgColor||t.toolTipBackground),i.setLineWidth(1*e.pix),i.rect(h,f-.5*t.fontSize-a,p,t.fontSize+2*a),i.closePath(),i.stroke(),i.fill(),i.beginPath(),i.setFontSize(t.fontSize),i.setFillStyle(n.labelFontColor||e.fontColor),i.fillText(o[c],g,f+.5*t.fontSize),i.closePath(),i.stroke(),"left"==r[c].position?s-=r[c].width+e.yAxis.padding*e.pix:l+=r[c].width+e.yAxis.padding*e.pix}}}function pn(e,t,i,a,o){var n=eo({},{activeBgColor:"#000000",activeBgOpacity:.08,activeWidth:o},t.extra.column);n.activeWidth=n.activeWidth>o?o:n.activeWidth;var r=t.area[0],s=t.height-t.area[2];a.beginPath(),a.setFillStyle(io(n.activeBgColor,n.activeBgOpacity)),a.rect(e-n.activeWidth/2,r,n.activeWidth,s-r),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function un(e,t,i,a,o){var n=eo({},{activeBgColor:"#000000",activeBgOpacity:.08},t.extra.bar),r=t.area[3],s=t.width-t.area[1];a.beginPath(),a.setFillStyle(io(n.activeBgColor,n.activeBgOpacity)),a.rect(r,e-o/2,s-r,o),a.closePath(),a.fill(),a.setFillStyle("#FFFFFF")}function gn(e,t,i,a,o,n,r){var s=eo({},{showBox:!0,showArrow:!0,showCategory:!1,bgColor:"#000000",bgOpacity:.7,borderColor:"#000000",borderWidth:0,borderRadius:0,borderOpacity:.7,boxPadding:3,fontColor:"#FFFFFF",fontSize:13,lineHeight:20,legendShow:!0,legendShape:"auto",splitLine:!0},i.extra.tooltip);1==s.showCategory&&i.categories&&e.unshift({text:i.categories[i.tooltip.index],color:null});var l=s.fontSize*i.pix,c=s.lineHeight*i.pix,h=s.boxPadding*i.pix,d=l,p=5*i.pix;0==s.legendShow&&(d=0,p=0);var u=s.showArrow?8*i.pix:0,g=!1;"line"!=i.type&&"mount"!=i.type&&"area"!=i.type&&"candle"!=i.type&&"mix"!=i.type||1==s.splitLine&&function(e,t,i,a){var o=t.extra.tooltip||{};o.gridType=null==o.gridType?"solid":o.gridType,o.dashLength=null==o.dashLength?4:o.dashLength;var n=t.area[0],r=t.height-t.area[2];if("dash"==o.gridType&&a.setLineDash([o.dashLength,o.dashLength]),a.setStrokeStyle(o.gridColor||"#cccccc"),a.setLineWidth(1*t.pix),a.beginPath(),a.moveTo(e,n),a.lineTo(e,r),a.stroke(),a.setLineDash([]),o.xAxisLabel){let n=t.categories[t.tooltip.index];a.setFontSize(i.fontSize);let s=po(n,i.fontSize,a),l=e-.5*s,c=r+2*t.pix;a.beginPath(),a.setFillStyle(io(o.labelBgColor||i.toolTipBackground,o.labelBgOpacity||i.toolTipOpacity)),a.setStrokeStyle(o.labelBgColor||i.toolTipBackground),a.setLineWidth(1*t.pix),a.rect(l-o.boxPadding*t.pix,c,s+2*o.boxPadding*t.pix,i.fontSize+2*o.boxPadding*t.pix),a.closePath(),a.stroke(),a.fill(),a.beginPath(),a.setFontSize(i.fontSize),a.setFillStyle(o.labelFontColor||t.fontColor),a.fillText(String(n),l,c+o.boxPadding*t.pix+i.fontSize),a.closePath(),a.stroke()}}(i.tooltip.offset.x,i,a,o),(t=eo({x:0,y:0},t)).y-=8*i.pix;var f=e.map((function(e){return po(e.text,l,o)})),x=d+p+4*h+Math.max.apply(null,f),m=2*h+e.length*c;if(0!=s.showBox){t.x-Math.abs(i._scrollDistance_||0)+u+x>i.width&&(g=!0),m+t.y>i.height&&(t.y=i.height-m),o.beginPath(),o.setFillStyle(io(s.bgColor,s.bgOpacity)),o.setLineWidth(s.borderWidth*i.pix),o.setStrokeStyle(io(s.borderColor,s.borderOpacity));var y=s.borderRadius;g?(x+u>i.width&&(t.x=i.width+Math.abs(i._scrollDistance_||0)+u+(x-i.width)),x>t.x&&(t.x=i.width+Math.abs(i._scrollDistance_||0)+u+(x-i.width)),s.showArrow&&(o.moveTo(t.x,t.y+10*i.pix),o.lineTo(t.x-u,t.y+10*i.pix+5*i.pix)),o.arc(t.x-u-y,t.y+m-y,y,0,Math.PI/2,!1),o.arc(t.x-u-Math.round(x)+y,t.y+m-y,y,Math.PI/2,Math.PI,!1),o.arc(t.x-u-Math.round(x)+y,t.y+y,y,-Math.PI,-Math.PI/2,!1),o.arc(t.x-u-y,t.y+y,y,-Math.PI/2,0,!1),s.showArrow&&(o.lineTo(t.x-u,t.y+10*i.pix-5*i.pix),o.lineTo(t.x,t.y+10*i.pix))):(s.showArrow&&(o.moveTo(t.x,t.y+10*i.pix),o.lineTo(t.x+u,t.y+10*i.pix-5*i.pix)),o.arc(t.x+u+y,t.y+y,y,-Math.PI,-Math.PI/2,!1),o.arc(t.x+u+Math.round(x)-y,t.y+y,y,-Math.PI/2,0,!1),o.arc(t.x+u+Math.round(x)-y,t.y+m-y,y,0,Math.PI/2,!1),o.arc(t.x+u+y,t.y+m-y,y,Math.PI/2,Math.PI,!1),s.showArrow&&(o.lineTo(t.x+u,t.y+10*i.pix+5*i.pix),o.lineTo(t.x,t.y+10*i.pix))),o.closePath(),o.fill(),s.borderWidth>0&&o.stroke(),s.legendShow&&e.forEach((function(e,a){if(null!==e.color){o.beginPath(),o.setFillStyle(e.color);var n=t.x+u+2*h,r=t.y+(c-l)/2+c*a+h+1;switch(g&&(n=t.x-x-u+2*h),e.legendShape){case"line":o.moveTo(n,r+.5*d-2*i.pix),o.fillRect(n,r+.5*d-2*i.pix,d,4*i.pix);break;case"triangle":o.moveTo(n+7.5*i.pix,r+.5*d-5*i.pix),o.lineTo(n*****i.pix,r+.5*d+5*i.pix),o.lineTo(n+12.5*i.pix,r+.5*d+5*i.pix),o.lineTo(n+7.5*i.pix,r+.5*d-5*i.pix);break;case"diamond":o.moveTo(n+7.5*i.pix,r+.5*d-5*i.pix),o.lineTo(n*****i.pix,r+.5*d),o.lineTo(n+7.5*i.pix,r+.5*d+5*i.pix),o.lineTo(n+12.5*i.pix,r+.5*d),o.lineTo(n+7.5*i.pix,r+.5*d-5*i.pix);break;case"circle":o.moveTo(n+7.5*i.pix,r+.5*d),o.arc(n+7.5*i.pix,r+.5*d,5*i.pix,0,2*Math.PI);break;case"rect":default:o.moveTo(n,r+.5*d-5*i.pix),o.fillRect(n,r+.5*d-5*i.pix,15*i.pix,10*i.pix);break;case"square":o.moveTo(n+2*i.pix,r+.5*d-5*i.pix),o.fillRect(n+2*i.pix,r+.5*d-5*i.pix,10*i.pix,10*i.pix)}o.closePath(),o.fill()}})),e.forEach((function(e,i){var a=t.x+u+2*h+d+p;g&&(a=t.x-x-u+2*h+d+p);var n=t.y+c*i+(c-l)/2-1+h+l;o.beginPath(),o.setFontSize(l),o.setTextBaseline("normal"),o.setFillStyle(s.fontColor),o.fillText(e.text,a,n),o.closePath(),o.stroke()}))}}function fn(e,t,i,a,o,n){(e.extra.tooltip||{}).horizentalLine&&e.tooltip&&1===a&&("line"==e.type||"area"==e.type||"column"==e.type||"mount"==e.type||"candle"==e.type||"mix"==e.type)&&dn(e,t,i),i.save(),e._scrollDistance_&&0!==e._scrollDistance_&&!0===e.enableScroll&&i.translate(e._scrollDistance_,0),e.tooltip&&e.tooltip.textList&&e.tooltip.textList.length&&1===a&&gn(e.tooltip.textList,e.tooltip.offset,e,t,i),i.restore()}function xn(e,t,i,a){let o=t.chartData.xAxisData,n=o.xAxisPoints,r=o.startX,s=o.endX,l=o.eachSpacing;var c="center";"bar"!=t.type&&"line"!=t.type&&"area"!=t.type&&"scatter"!=t.type&&"bubble"!=t.type||(c=t.xAxis.boundaryGap);var h=t.height-t.area[2],d=t.area[0];if(t.enableScroll&&t.xAxis.scrollShow){var p=t.height-t.area[2]+i.xAxisHeight,u=s-r,g=l*(n.length-1);"mount"==t.type&&t.extra&&t.extra.mount&&t.extra.mount.widthRatio&&t.extra.mount.widthRatio>1&&(t.extra.mount.widthRatio>2&&(t.extra.mount.widthRatio=2),g+=(t.extra.mount.widthRatio-1)*l);var f=u*u/g,x=0;t._scrollDistance_&&(x=-t._scrollDistance_*u/g),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*t.pix),a.setStrokeStyle(t.xAxis.scrollBackgroundColor||"#EFEBEF"),a.moveTo(r,p),a.lineTo(s,p),a.stroke(),a.closePath(),a.beginPath(),a.setLineCap("round"),a.setLineWidth(6*t.pix),a.setStrokeStyle(t.xAxis.scrollColor||"#A6A6A6"),a.moveTo(r+x,p),a.lineTo(r+x+f,p),a.stroke(),a.closePath(),a.setLineCap("butt")}if(a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&a.translate(t._scrollDistance_,0),!0===t.xAxis.calibration&&(a.setStrokeStyle(t.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*t.pix),n.forEach((function(e,i){i>0&&(a.beginPath(),a.moveTo(e-l/2,h),a.lineTo(e-l/2,h+3*t.pix),a.closePath(),a.stroke())}))),!0!==t.xAxis.disableGrid&&(a.setStrokeStyle(t.xAxis.gridColor||"#cccccc"),a.setLineCap("butt"),a.setLineWidth(1*t.pix),"dash"==t.xAxis.gridType&&a.setLineDash([t.xAxis.dashLength*t.pix,t.xAxis.dashLength*t.pix]),t.xAxis.gridEval=t.xAxis.gridEval||1,n.forEach((function(e,i){i%t.xAxis.gridEval==0&&(a.beginPath(),a.moveTo(e,h),a.lineTo(e,d),a.stroke())})),a.setLineDash([])),!0!==t.xAxis.disabled){let o=e.length;t.xAxis.labelCount&&(o=t.xAxis.itemCount?Math.ceil(e.length/t.xAxis.itemCount*t.xAxis.labelCount):t.xAxis.labelCount,o-=1);let r=Math.ceil(e.length/o),s=[],d=e.length;for(let t=0;t<d;t++)t%r!=0?s.push(""):s.push(e[t]);s[d-1]=e[d-1];var m=t.xAxis.fontSize*t.pix||i.fontSize;0===i._xAxisTextAngle_?s.forEach((function(e,i){var o=t.xAxis.formatter?t.xAxis.formatter(e,i,t):e,r=-po(String(o),m,a)/2;"center"==c&&(r+=l/2),t.xAxis.scrollShow&&t.pix;var s=t._scrollDistance_||0,d="center"==c?n[i]+l/2:n[i];d-Math.abs(s)>=t.area[3]-1&&d-Math.abs(s)<=t.width-t.area[1]+1&&(a.beginPath(),a.setFontSize(m),a.setFillStyle(t.xAxis.fontColor||t.fontColor),a.fillText(String(o),n[i]+r,h+t.xAxis.marginTop*t.pix+(t.xAxis.lineHeight-t.xAxis.fontSize)*t.pix/2+t.xAxis.fontSize*t.pix),a.closePath(),a.stroke())})):s.forEach((function(e,o){var r=t.xAxis.formatter?t.xAxis.formatter(e):e,s=t._scrollDistance_||0,d="center"==c?n[o]+l/2:n[o];if(d-Math.abs(s)>=t.area[3]-1&&d-Math.abs(s)<=t.width-t.area[1]+1){a.save(),a.beginPath(),a.setFontSize(m),a.setFillStyle(t.xAxis.fontColor||t.fontColor);var p=po(String(r),m,a),u=n[o];"center"==c&&(u=n[o]+l/2),t.xAxis.scrollShow&&t.pix;var g=h+t.xAxis.marginTop*t.pix+m-m*Math.abs(Math.sin(i._xAxisTextAngle_));t.xAxis.rotateAngle<0?(u-=m/2,p=0):(u+=m/2,p=-p),a.translate(u,g),a.rotate(-1*i._xAxisTextAngle_),a.fillText(String(r),p,0),a.closePath(),a.stroke(),a.restore()}}))}a.restore(),t.xAxis.title&&(a.beginPath(),a.setFontSize(t.xAxis.titleFontSize*t.pix),a.setFillStyle(t.xAxis.titleFontColor),a.fillText(String(t.xAxis.title),t.width-t.area[1]+t.xAxis.titleOffsetX*t.pix,t.height-t.area[2]+t.xAxis.marginTop*t.pix+(t.xAxis.lineHeight-t.xAxis.titleFontSize)*t.pix/2+(t.xAxis.titleFontSize+t.xAxis.titleOffsetY)*t.pix),a.closePath(),a.stroke()),t.xAxis.axisLine&&(a.beginPath(),a.setStrokeStyle(t.xAxis.axisLineColor),a.setLineWidth(1*t.pix),a.moveTo(r,t.height-t.area[2]),a.lineTo(s,t.height-t.area[2]),a.stroke())}function mn(e,t,i,a){if(!0===t.yAxis.disableGrid)return;let o=(t.height-t.area[0]-t.area[2])/t.yAxis.splitNumber,n=t.area[3],r=t.chartData.xAxisData.xAxisPoints,s=t.chartData.xAxisData.eachSpacing,l=s*(r.length-1);"mount"==t.type&&t.extra&&t.extra.mount&&t.extra.mount.widthRatio&&t.extra.mount.widthRatio>1&&(t.extra.mount.widthRatio>2&&(t.extra.mount.widthRatio=2),l+=(t.extra.mount.widthRatio-1)*s);let c=n+l,h=[],d=1;!1===t.xAxis.axisLine&&(d=0);for(let p=d;p<t.yAxis.splitNumber+1;p++)h.push(t.height-t.area[2]-o*p);a.save(),t._scrollDistance_&&0!==t._scrollDistance_&&a.translate(t._scrollDistance_,0),"dash"==t.yAxis.gridType&&a.setLineDash([t.yAxis.dashLength*t.pix,t.yAxis.dashLength*t.pix]),a.setStrokeStyle(t.yAxis.gridColor),a.setLineWidth(1*t.pix),h.forEach((function(e,t){a.beginPath(),a.moveTo(n,e),a.lineTo(c,e),a.stroke()})),a.setLineDash([]),a.restore()}function yn(e,t,i,a){if(!0===t.yAxis.disabled)return;var o=t.height-t.area[0]-t.area[2],n=o/t.yAxis.splitNumber,r=t.area[3],s=t.width-t.area[1],l=t.height-t.area[2];a.beginPath(),a.setFillStyle(t.background),1==t.enableScroll&&t.xAxis.scrollPosition&&"left"!==t.xAxis.scrollPosition&&a.fillRect(0,0,r,l+2*t.pix),1==t.enableScroll&&t.xAxis.scrollPosition&&"right"!==t.xAxis.scrollPosition&&a.fillRect(s,0,t.width,l+2*t.pix),a.closePath(),a.stroke();let c=t.area[3],h=t.width-t.area[1],d=t.area[3]+(t.width-t.area[1]-t.area[3])/2;if(t.yAxis.data)for(let u=0;u<t.yAxis.data.length;u++){let e=t.yAxis.data[u];var p=[];if("categories"===e.type)for(let i=0;i<=e.categories.length;i++)p.push(t.area[0]+o/e.categories.length/2+o/e.categories.length*i);else for(let i=0;i<=t.yAxis.splitNumber;i++)p.push(t.area[0]+n*i);if(!0!==e.disabled){let o=t.chartData.yAxisData.rangesFormat[u],n=e.fontSize?e.fontSize*t.pix:i.fontSize,r=t.chartData.yAxisData.yAxisWidth[u],s=e.textAlign||"right";if(o.forEach((function(i,o){var l=p[o];a.beginPath(),a.setFontSize(n),a.setLineWidth(1*t.pix),a.setStrokeStyle(e.axisLineColor||"#cccccc"),a.setFillStyle(e.fontColor||t.fontColor);let u=0,g=4*t.pix;if("left"==r.position){switch(1==e.calibration&&(a.moveTo(c,l),a.lineTo(c-3*t.pix,l),g+=3*t.pix),s){case"left":a.setTextAlign("left"),u=c-r.width;break;case"right":a.setTextAlign("right"),u=c-g;break;default:a.setTextAlign("center"),u=c-r.width/2}a.fillText(String(i),u,l+n/2-3*t.pix)}else if("right"==r.position){switch(1==e.calibration&&(a.moveTo(h,l),a.lineTo(h+3*t.pix,l),g+=3*t.pix),s){case"left":a.setTextAlign("left"),u=h+g;break;case"right":a.setTextAlign("right"),u=h+r.width;break;default:a.setTextAlign("center"),u=h+r.width/2}a.fillText(String(i),u,l+n/2-3*t.pix)}else if("center"==r.position){switch(1==e.calibration&&(a.moveTo(d,l),a.lineTo(d-3*t.pix,l),g+=3*t.pix),s){case"left":a.setTextAlign("left"),u=d-r.width;break;case"right":a.setTextAlign("right"),u=d-g;break;default:a.setTextAlign("center"),u=d-r.width/2}a.fillText(String(i),u,l+n/2-3*t.pix)}a.closePath(),a.stroke(),a.setTextAlign("left")})),!1!==e.axisLine&&(a.beginPath(),a.setStrokeStyle(e.axisLineColor||"#cccccc"),a.setLineWidth(1*t.pix),"left"==r.position?(a.moveTo(c,t.height-t.area[2]),a.lineTo(c,t.area[0])):"right"==r.position?(a.moveTo(h,t.height-t.area[2]),a.lineTo(h,t.area[0])):"center"==r.position&&(a.moveTo(d,t.height-t.area[2]),a.lineTo(d,t.area[0])),a.stroke()),t.yAxis.showTitle){let o=e.titleFontSize*t.pix||i.fontSize,n=e.title;a.beginPath(),a.setFontSize(o),a.setFillStyle(e.titleFontColor||t.fontColor),"left"==r.position?a.fillText(n,c-po(n,o,a)/2+(e.titleOffsetX||0),t.area[0]-(10-(e.titleOffsetY||0))*t.pix):"right"==r.position?a.fillText(n,h-po(n,o,a)/2+(e.titleOffsetX||0),t.area[0]-(10-(e.titleOffsetY||0))*t.pix):"center"==r.position&&a.fillText(n,d-po(n,o,a)/2+(e.titleOffsetX||0),t.area[0]-(10-(e.titleOffsetY||0))*t.pix),a.closePath(),a.stroke()}"left"==r.position?c-=r.width+t.yAxis.padding*t.pix:h+=r.width+t.yAxis.padding*t.pix}}}function vn(e,t,i,a,o){if(!1===t.legend.show)return;let n=o.legendData,r=n.points,s=n.area,l=t.legend.padding*t.pix,c=t.legend.fontSize*t.pix,h=15*t.pix,d=5*t.pix,p=t.legend.itemGap*t.pix,u=Math.max(t.legend.lineHeight*t.pix,c);a.beginPath(),a.setLineWidth(t.legend.borderWidth*t.pix),a.setStrokeStyle(t.legend.borderColor),a.setFillStyle(t.legend.backgroundColor),a.moveTo(s.start.x,s.start.y),a.rect(s.start.x,s.start.y,s.width,s.height),a.closePath(),a.fill(),a.stroke(),r.forEach((function(e,o){let r=0,g=0;r=n.widthArr[o],g=n.heightArr[o];let f=0,x=0;if("top"==t.legend.position||"bottom"==t.legend.position){switch(t.legend.float){case"left":f=s.start.x+l;break;case"right":f=s.start.x+s.width-r;break;default:f=s.start.x+(s.width-r)/2}x=s.start.y+l+o*u}else r=0==o?0:n.widthArr[o-1],f=s.start.x+l+r,x=s.start.y+l+(s.height-g)/2;a.setFontSize(i.fontSize);for(let i=0;i<e.length;i++){let o=e[i];switch(o.area=[0,0,0,0],o.area[0]=f,o.area[1]=x,o.area[3]=x+u,a.beginPath(),a.setLineWidth(1*t.pix),a.setStrokeStyle(o.show?o.color:t.legend.hiddenColor),a.setFillStyle(o.show?o.color:t.legend.hiddenColor),o.legendShape){case"line":a.moveTo(f,x+.5*u-2*t.pix),a.fillRect(f,x+.5*u-2*t.pix,15*t.pix,4*t.pix);break;case"triangle":a.moveTo(f+7.5*t.pix,x+.5*u-5*t.pix),a.lineTo(f*****t.pix,x+.5*u+5*t.pix),a.lineTo(f+12.5*t.pix,x+.5*u+5*t.pix),a.lineTo(f+7.5*t.pix,x+.5*u-5*t.pix);break;case"diamond":a.moveTo(f+7.5*t.pix,x+.5*u-5*t.pix),a.lineTo(f*****t.pix,x+.5*u),a.lineTo(f+7.5*t.pix,x+.5*u+5*t.pix),a.lineTo(f+12.5*t.pix,x+.5*u),a.lineTo(f+7.5*t.pix,x+.5*u-5*t.pix);break;case"circle":a.moveTo(f+7.5*t.pix,x+.5*u),a.arc(f+7.5*t.pix,x+.5*u,5*t.pix,0,2*Math.PI);break;case"rect":default:a.moveTo(f,x+.5*u-5*t.pix),a.fillRect(f,x+.5*u-5*t.pix,15*t.pix,10*t.pix);break;case"square":a.moveTo(f+5*t.pix,x+.5*u-5*t.pix),a.fillRect(f+5*t.pix,x+.5*u-5*t.pix,10*t.pix,10*t.pix);case"none":}a.closePath(),a.fill(),a.stroke(),f+=h+d;let n=.5*u+.5*c-2;const r=o.legendText?o.legendText:o.name;a.beginPath(),a.setFontSize(c),a.setFillStyle(o.show?t.legend.fontColor:t.legend.hiddenColor),a.fillText(r,f,x+n),a.closePath(),a.stroke(),"top"==t.legend.position||"bottom"==t.legend.position?(f+=po(r,c,a)+p,o.area[2]=f):(o.area[2]=f+po(r,c,a)+p,f-=h+d,x+=u)}}))}function bn(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,ringWidth:30,customRadius:0,border:!1,borderWidth:2,borderColor:"#FFFFFF",centerColor:"#FFFFFF",linearType:"none",customColor:[]},"pie"==t.type?t.extra.pie:t.extra.ring),r={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2};0==i.pieChartLinePadding&&(i.pieChartLinePadding=n.activeRadius*t.pix);var s=Math.min((t.width-t.area[1]-t.area[3])/2-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,(t.height-t.area[0]-t.area[2])/2-i.pieChartLinePadding-i.pieChartTextPadding);s=s<10?10:s,n.customRadius>0&&(s=n.customRadius*t.pix),e=Do(e,s,o);var l=n.activeRadius*t.pix;if(n.customColor=ho(n.linearType,n.customColor,e,i),(e=e.map((function(e){return e._start_+=n.offsetAngle*Math.PI/180,e}))).forEach((function(e,i){t.tooltip&&t.tooltip.index==i&&(a.beginPath(),a.setFillStyle(io(e.color,n.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,e._radius_+l,e._start_,e._start_+2*e._proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(n.borderWidth*t.pix),a.lineJoin="round",a.setStrokeStyle(n.borderColor);var o,s=e.color;"custom"==n.linearType&&((o=a.createCircularGradient?a.createCircularGradient(r.x,r.y,e._radius_):a.createRadialGradient(r.x,r.y,0,r.x,r.y,e._radius_)).addColorStop(0,io(n.customColor[e.linearIndex],1)),o.addColorStop(1,io(e.color,1)),s=o);a.setFillStyle(s),a.moveTo(r.x,r.y),a.arc(r.x,r.y,e._radius_,e._start_,e._start_+2*e._proportion_*Math.PI),a.closePath(),a.fill(),1==n.border&&a.stroke()})),"ring"===t.type){var c=.6*s;"number"==typeof n.ringWidth&&n.ringWidth>0&&(c=Math.max(0,s-n.ringWidth*t.pix)),a.beginPath(),a.setFillStyle(n.centerColor),a.moveTo(r.x,r.y),a.arc(r.x,r.y,c,0,2*Math.PI),a.closePath(),a.fill()}return!1!==t.dataLabel&&1===o&&cn(e,t,i,a,0,r),1===o&&"ring"===t.type&&tn(t,i,a,r),{center:r,radius:s,series:e}}function Sn(e,t){var i=Array(2),a=20037508.34*e/180,o=Math.log(Math.tan((90+t)*Math.PI/360))/(Math.PI/180);return o=20037508.34*o/180,i[0]=a,i[1]=o,i}function wn(e,t,i,a,o,n){return{x:(t-i.xMin)*a+o,y:(i.yMax-e)*a+n}}function Tn(e,t,i){if(t[1]==i[1])return!1;if(t[1]>e[1]&&i[1]>e[1])return!1;if(t[1]<e[1]&&i[1]<e[1])return!1;if(t[1]==e[1]&&i[1]>e[1])return!1;if(i[1]==e[1]&&t[1]>e[1])return!1;if(t[0]<e[0]&&i[1]<e[1])return!1;return!(i[0]-(i[0]-t[0])*(i[1]-e[1])/(i[1]-t[1])<e[0])}function _n(e,t,i){let a=0;for(let o=0;o<t.length;o++){let n=t[o][0];1==t.length&&(n=t[o][0]);for(let t=0;t<n.length-1;t++){let o=n[t],r=n[t+1];i&&(o=Sn(n[t][0],n[t][1]),r=Sn(n[t+1][0],n[t+1][1])),Tn(e,o,r)&&(a+=1)}}return a%2==1}function An(e,t,i){i=0==i?1:i;for(var a=[],o=0;o<i;o++)a[o]=Math.random();return Math.floor(a.reduce((function(e,t){return e+t}))/i*(t-e))+e}function Pn(e,t,i,a){var o=!1;for(let n=0;n<t.length;n++)if(t[n].area){if(!(e[3]<t[n].area[1]||e[0]>t[n].area[2]||e[1]>t[n].area[3]||e[2]<t[n].area[0])){o=!0;break}if(e[0]<0||e[1]<0||e[2]>i||e[3]>a){o=!0;break}o=!1}return o}function Cn(e,t,i){let a=e.series;switch(t){case"normal":for(let o=0;o<a.length;o++){let t,n,r,s=a[o].name,l=a[o].textSize*e.pix,c=po(s,l,i),h=0;for(;;){if(h++,t=An(-e.width/2,e.width/2,5)-c/2,n=An(-e.height/2,e.height/2,5)+l/2,r=[t-5+e.width/2,n-5-l+e.height/2,t+c+5+e.width/2,n+5+e.height/2],!Pn(r,a,e.width,e.height))break;if(1e3==h){r=[-100,-100,-100,-100];break}}a[o].area=r}break;case"vertical":let t=function(){return Math.random()>.7};for(let o=0;o<a.length;o++){let n,r,s,l,c=a[o].name,h=a[o].textSize*e.pix,d=po(c,h,i),p=t(),u=0;for(;;){let t;if(u++,p?(n=An(-e.width/2,e.width/2,5)-d/2,r=An(-e.height/2,e.height/2,5)+h/2,s=[r-5-d+e.width/2,-n-5+e.height/2,r+5+e.width/2,-n+h+5+e.height/2],l=[e.width-(e.width/2-e.height/2)-(-n+h+5+e.height/2)-5,e.height/2-e.width/2+(r-5-d+e.width/2)-5,e.width-(e.width/2-e.height/2)-(-n+h+5+e.height/2)+h,e.height/2-e.width/2+(r-5-d+e.width/2)+d+5],t=Pn(l,a,e.height,e.width)):(n=An(-e.width/2,e.width/2,5)-d/2,r=An(-e.height/2,e.height/2,5)+h/2,s=[n-5+e.width/2,r-5-h+e.height/2,n+d+5+e.width/2,r+5+e.height/2],t=Pn(s,a,e.width,e.height)),!t)break;if(1e3==u){s=[-1e3,-1e3,-1e3,-1e3];break}}p?(a[o].area=l,a[o].areav=s):a[o].area=s,a[o].rotate=p}}return a}function kn(e,t,i,a,o,n,r){for(let s=0;s<e.length;s++){let l,c,h,d,p=e[s];if(!1===p.labelShow)continue;let u=p.formatter?p.formatter(p,s,e,t):to.toFixed(100*p._proportion_)+"%";u=p.labelText?p.labelText:u,"right"==o&&(l=s==e.length-1?(p.funnelArea[2]+r.x)/2:(p.funnelArea[2]+e[s+1].funnelArea[2])/2,c=l+2*n,h=p.funnelArea[1]+a/2,d=p.textSize*t.pix||t.fontSize*t.pix,i.setLineWidth(1*t.pix),i.setStrokeStyle(p.color),i.setFillStyle(p.color),i.beginPath(),i.moveTo(l,h),i.lineTo(c,h),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(c,h),i.arc(c,h,2*t.pix,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(d),i.setFillStyle(p.textColor||t.fontColor),i.fillText(u,c+5,h+d/2-2),i.closePath(),i.stroke(),i.closePath()),"left"==o&&(l=s==e.length-1?(p.funnelArea[0]+r.x)/2:(p.funnelArea[0]+e[s+1].funnelArea[0])/2,c=l-2*n,h=p.funnelArea[1]+a/2,d=p.textSize*t.pix||t.fontSize*t.pix,i.setLineWidth(1*t.pix),i.setStrokeStyle(p.color),i.setFillStyle(p.color),i.beginPath(),i.moveTo(l,h),i.lineTo(c,h),i.stroke(),i.closePath(),i.beginPath(),i.moveTo(c,h),i.arc(c,h,2,0,2*Math.PI),i.closePath(),i.fill(),i.beginPath(),i.setFontSize(d),i.setFillStyle(p.textColor||t.fontColor),i.fillText(u,c-5-po(u,d,i),h+d/2-2),i.closePath(),i.stroke(),i.closePath())}}function Dn(e,t,i,a,o,n,r){for(let s=0;s<e.length;s++){let o,n,l=e[s];l.centerText&&(o=l.funnelArea[1]+a/2,n=l.centerTextSize*t.pix||t.fontSize*t.pix,i.beginPath(),i.setFontSize(n),i.setFillStyle(l.centerTextColor||"#FFFFFF"),i.fillText(l.centerText,r.x-po(l.centerText,n,i)/2,o+n/2-2),i.closePath(),i.stroke(),i.closePath())}}function Ln(e,t){t.save(),t.translate(0,.5),t.restore(),t.draw()}var In={easeIn:function(e){return Math.pow(e,3)},easeOut:function(e){return Math.pow(e-1,3)+1},easeInOut:function(e){return(e/=.5)<1?.5*Math.pow(e,3):.5*(Math.pow(e-2,3)+2)},linear:function(e){return e}};function Mn(e){this.isStop=!1,e.duration=void 0===e.duration?1e3:e.duration,e.timing=e.timing||"easeInOut";var t="undefined"!=typeof setTimeout?function(e,t){setTimeout((function(){e(+new Date)}),t)}:"undefined"!=typeof requestAnimationFrame?requestAnimationFrame:function(e){e(null)},i=null,a=function(o){if(null===o||!0===this.isStop)return e.onProcess&&e.onProcess(1),void(e.onAnimationFinish&&e.onAnimationFinish());if(null===i&&(i=o),o-i<e.duration){var n=(o-i)/e.duration;n=(0,In[e.timing])(n),e.onProcess&&e.onProcess(n),t(a,17)}else e.onProcess&&e.onProcess(1),e.onAnimationFinish&&e.onAnimationFinish()};a=a.bind(this),t(a,17)}function Fn(e,t,i,a){var o=this,n=t.series;"pie"!==e&&"ring"!==e&&"mount"!==e&&"rose"!==e&&"funnel"!==e||(n=function(e,t,i){let a=[];if(e.length>0&&e[0].data.constructor.toString().indexOf("Array")>-1){t._pieSeries_=e;let i=e[0].data;for(var o=0;o<i.length;o++)i[o].formatter=e[0].formatter,i[o].data=i[o].value,a.push(i[o]);t.series=a}else a=e;return a}(n,t));var r=t.categories;if("mount"===e){r=[];for(let e=0;e<n.length;e++)!1!==n[e].show&&r.push(n[e].name);t.categories=r}n=co(n,t,i);var s=t.animation?t.duration:0;o.animationInstance&&o.animationInstance.stop();var l=null;if("candle"==e){let e=eo({},t.extra.candle.average);e.show?(l=co(l=function(e,t,i,a){let o=[];for(let n=0;n<e.length;n++){let r={data:[],name:t[n],color:i[n]};for(let t=0,i=a.length;t<i;t++){if(t<e[n]){r.data.push(null);continue}let i=0;for(let o=0;o<e[n];o++)i+=a[t-o][1];r.data.push(+(i/e[n]).toFixed(3))}o.push(r)}return o}(e.day,e.name,e.color,n[0].data),t,i),t.seriesMA=l):l=t.seriesMA?t.seriesMA=co(t.seriesMA,t,i):n}else l=n;t._series_=n=wo(n),t.area=new Array(4);for(let g=0;g<4;g++)t.area[g]=t.padding[g]*t.pix;var c=function(e,t,i,a,o){let n={area:{start:{x:0,y:0},end:{x:0,y:0},width:0,height:0,wholeWidth:0,wholeHeight:0},points:[],widthArr:[],heightArr:[]};if(!1===t.legend.show)return a.legendData=n,n;let r=t.legend.padding*t.pix,s=t.legend.margin*t.pix,l=t.legend.fontSize?t.legend.fontSize*t.pix:i.fontSize,c=15*t.pix,h=5*t.pix,d=Math.max(t.legend.lineHeight*t.pix,l);if("top"==t.legend.position||"bottom"==t.legend.position){let i=[],a=0,p=[],u=[];for(let n=0;n<e.length;n++){let r=e[n],s=c+h+po((r.legendText?r.legendText:r.name)||"undefined",l,o)+t.legend.itemGap*t.pix;a+s>t.width-t.area[1]-t.area[3]?(i.push(u),p.push(a-t.legend.itemGap*t.pix),a=s,u=[r]):(a+=s,u.push(r))}if(u.length){i.push(u),p.push(a-t.legend.itemGap*t.pix),n.widthArr=p;let e=Math.max.apply(null,p);switch(t.legend.float){case"left":n.area.start.x=t.area[3],n.area.end.x=t.area[3]+e+2*r;break;case"right":n.area.start.x=t.width-t.area[1]-e-2*r,n.area.end.x=t.width-t.area[1];break;default:n.area.start.x=(t.width-e)/2-r,n.area.end.x=(t.width+e)/2+r}n.area.width=e+2*r,n.area.wholeWidth=e+2*r,n.area.height=i.length*d+2*r,n.area.wholeHeight=i.length*d+2*r+2*s,n.points=i}}else{let i=e.length,a=t.height-t.area[0]-t.area[2]-2*s-2*r,p=Math.min(Math.floor(a/d),i);switch(n.area.height=p*d+2*r,n.area.wholeHeight=p*d+2*r,t.legend.float){case"top":n.area.start.y=t.area[0]+s,n.area.end.y=t.area[0]+s+n.area.height;break;case"bottom":n.area.start.y=t.height-t.area[2]-s-n.area.height,n.area.end.y=t.height-t.area[2]-s;break;default:n.area.start.y=(t.height-n.area.height)/2,n.area.end.y=(t.height+n.area.height)/2}let u=i%p==0?i/p:Math.floor(i/p+1),g=[];for(let t=0;t<u;t++){let i=e.slice(t*p,t*p+p);g.push(i)}if(n.points=g,g.length){for(let i=0;i<g.length;i++){let e=g[i],a=0;for(let i=0;i<e.length;i++){let n=c+h+po(e[i].name||"undefined",l,o)+t.legend.itemGap*t.pix;n>a&&(a=n)}n.widthArr.push(a),n.heightArr.push(e.length*d+2*r)}let e=0;for(let t=0;t<n.widthArr.length;t++)e+=n.widthArr[t];n.area.width=e-t.legend.itemGap*t.pix+2*r,n.area.wholeWidth=n.area.width+r}}switch(t.legend.position){case"top":n.area.start.y=t.area[0]+s,n.area.end.y=t.area[0]+s+n.area.height;break;case"bottom":n.area.start.y=t.height-t.area[2]-n.area.height-s,n.area.end.y=t.height-t.area[2]-s;break;case"left":n.area.start.x=t.area[3],n.area.end.x=t.area[3]+n.area.width;break;case"right":n.area.start.x=t.width-t.area[1]-n.area.width,n.area.end.x=t.width-t.area[1]}return a.legendData=n,n}(l,t,i,t.chartData,a),h=c.area.wholeHeight,d=c.area.wholeWidth;switch(t.legend.position){case"top":t.area[0]+=h;break;case"bottom":t.area[2]+=h;break;case"left":t.area[3]+=d;break;case"right":t.area[1]+=d}let p={},u=0;if("line"===t.type||"column"===t.type||"mount"===t.type||"area"===t.type||"mix"===t.type||"candle"===t.type||"scatter"===t.type||"bubble"===t.type||"bar"===t.type){if(p=Yo(n,t,i,a),u=p.yAxisWidth,t.yAxis.showTitle){let e=0;for(let a=0;a<t.yAxis.data.length;a++)e=Math.max(e,t.yAxis.data[a].titleFontSize?t.yAxis.data[a].titleFontSize*t.pix:i.fontSize);t.area[0]+=e}let e=0,o=0;for(let i=0;i<u.length;i++)"left"==u[i].position?(t.area[3]+=o>0?u[i].width+t.yAxis.padding*t.pix:u[i].width,o+=1):"right"==u[i].position&&(t.area[1]+=e>0?u[i].width+t.yAxis.padding*t.pix:u[i].width,e+=1)}else i.yAxisWidth=u;if(t.chartData.yAxisData=p,t.categories&&t.categories.length&&"radar"!==t.type&&"gauge"!==t.type&&"bar"!==t.type){t.chartData.xAxisData=Wo(t.categories,t);let e=Po(t.categories,t,0,t.chartData.xAxisData.eachSpacing,a),o=e.xAxisHeight,n=e.angle;i.xAxisHeight=o,i._xAxisTextAngle_=n,t.area[2]+=o,t.chartData.categoriesData=e}else if("line"===t.type||"area"===t.type||"scatter"===t.type||"bubble"===t.type||"bar"===t.type){t.chartData.xAxisData=Co(n,t,i,a);let e=Po(r=t.chartData.xAxisData.rangesFormat,t,0,t.chartData.xAxisData.eachSpacing,a),o=e.xAxisHeight,s=e.angle;i.xAxisHeight=o,i._xAxisTextAngle_=s,t.area[2]+=o,t.chartData.categoriesData=e}else t.chartData.xAxisData={xAxisPoints:[]};if(t.enableScroll&&"right"==t.xAxis.scrollAlign&&void 0===t._scrollDistance_){let e=0,i=t.chartData.xAxisData.xAxisPoints,a=t.chartData.xAxisData.startX;e=t.chartData.xAxisData.endX-a-t.chartData.xAxisData.eachSpacing*(i.length-1),o.scrollOption.currentOffset=e,o.scrollOption.startTouchX=e,o.scrollOption.distance=0,o.scrollOption.lastMoveTime=0,t._scrollDistance_=e}switch("pie"!==e&&"ring"!==e&&"rose"!==e||(i._pieTextMaxLength_=!1===t.dataLabel?0:function(e,t,i,a){e=Do(e);let o=0;for(let n=0;n<e.length;n++){let r=e[n],s=r.formatter?r.formatter(+r._proportion_.toFixed(2)):to.toFixed(100*r._proportion_)+"%";o=Math.max(o,po(s,r.textSize*a.pix||t.fontSize,i))}return o}(l,i,a,t)),e){case"word":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),function(e,t,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{type:"normal",autoColors:!0},t.extra.word);t.chartData.wordCloudData||(t.chartData.wordCloudData=Cn(t,n.type,a)),a.beginPath(),a.setFillStyle(t.background),a.rect(0,0,t.width,t.height),a.fill(),a.save();let r=t.chartData.wordCloudData;a.translate(t.width/2,t.height/2);for(let s=0;s<r.length;s++){a.save(),r[s].rotate&&a.rotate(90*Math.PI/180);let e=r[s].name,i=r[s].textSize*t.pix,n=po(e,i,a);a.beginPath(),a.setStrokeStyle(r[s].color),a.setFillStyle(r[s].color),a.setFontSize(i),r[s].rotate?r[s].areav[0]>0&&(t.tooltip&&t.tooltip.index==s?a.strokeText(e,(r[s].areav[0]+5-t.width/2)*o-n*(1-o)/2,(r[s].areav[1]+5+i-t.height/2)*o):a.fillText(e,(r[s].areav[0]+5-t.width/2)*o-n*(1-o)/2,(r[s].areav[1]+5+i-t.height/2)*o)):r[s].area[0]>0&&(t.tooltip&&t.tooltip.index==s?a.strokeText(e,(r[s].area[0]+5-t.width/2)*o-n*(1-o)/2,(r[s].area[1]+5+i-t.height/2)*o):a.fillText(e,(r[s].area[0]+5-t.width/2)*o-n*(1-o)/2,(r[s].area[1]+5+i-t.height/2)*o)),a.stroke(),a.restore()}a.restore()}(n,t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"map":a.clearRect(0,0,t.width,t.height),function(e,t,i,a){var o,n,r=eo({},{border:!0,mercator:!1,borderWidth:1,active:!0,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#f04864",activeFillColor:"#facc14",activeFillOpacity:1},t.extra.map),s=e,l=function(e){for(var t,i={xMin:180,xMax:0,yMin:90,yMax:0},a=0;a<e.length;a++)for(var o=e[a].geometry.coordinates,n=0;n<o.length;n++){1==(t=o[n]).length&&(t=t[0]);for(var r=0;r<t.length;r++){var s={x:t[r][0],y:t[r][1]};i.xMin=i.xMin<s.x?i.xMin:s.x,i.xMax=i.xMax>s.x?i.xMax:s.x,i.yMin=i.yMin<s.y?i.yMin:s.y,i.yMax=i.yMax>s.y?i.yMax:s.y}}return i}(s);if(r.mercator){var c=Sn(l.xMax,l.yMax),h=Sn(l.xMin,l.yMin);l.xMax=c[0],l.yMax=c[1],l.xMin=h[0],l.yMin=h[1]}for(var d=t.width/Math.abs(l.xMax-l.xMin),p=t.height/Math.abs(l.yMax-l.yMin),u=d<p?d:p,g=t.width/2-Math.abs(l.xMax-l.xMin)/2*u,f=t.height/2-Math.abs(l.yMax-l.yMin)/2*u,x=0;x<s.length;x++){a.beginPath(),a.setLineWidth(r.borderWidth*t.pix),a.setStrokeStyle(r.borderColor),a.setFillStyle(io(e[x].color,e[x].fillOpacity||r.fillOpacity)),1==r.active&&t.tooltip&&t.tooltip.index==x&&(a.setStrokeStyle(r.activeBorderColor),a.setFillStyle(io(r.activeFillColor,r.activeFillOpacity)));for(var m=s[x].geometry.coordinates,y=0;y<m.length;y++){1==(o=m[y]).length&&(o=o[0]);for(var v=0;v<o.length;v++){var b=Array(2);n=wn((b=r.mercator?Sn(o[v][0],o[v][1]):o[v])[1],b[0],l,u,g,f),0===v?(a.beginPath(),a.moveTo(n.x,n.y)):a.lineTo(n.x,n.y)}a.fill(),1==r.border&&a.stroke()}}if(1==t.dataLabel)for(x=0;x<s.length;x++){var S=s[x].properties.centroid;if(S){r.mercator&&(S=Sn(s[x].properties.centroid[0],s[x].properties.centroid[1])),n=wn(S[1],S[0],l,u,g,f);let e=s[x].textSize*t.pix||i.fontSize,o=s[x].textColor||t.fontColor;r.active&&r.activeTextColor&&t.tooltip&&t.tooltip.index==x&&(o=r.activeTextColor);let c=s[x].properties.name;a.beginPath(),a.setFontSize(e),a.setFillStyle(o),a.fillText(c,n.x-po(c,e,a)/2,n.y+e/2),a.closePath(),a.stroke()}}t.chartData.mapData={bounds:l,scale:u,xoffset:g,yoffset:f,mercator:r.mercator},fn(t,i,a,1),a.draw()}(n,t,i,a),setTimeout((()=>{this.uevent.trigger("renderComplete")}),50);break;case"funnel":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),t.chartData.funnelData=function(e,t,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{type:"funnel",activeWidth:10,activeOpacity:.3,border:!1,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,minSize:0,labelAlign:"right",linearType:"none",customColor:[]},t.extra.funnel),r=(t.height-t.area[0]-t.area[2])/e.length,s={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.height-t.area[2]},l=n.activeWidth*t.pix,c=Math.min((t.width-t.area[1]-t.area[3])/2-l,(t.height-t.area[0]-t.area[2])/2-l),h=Lo(e,c,n,r,o);if(a.save(),a.translate(s.x,s.y),n.customColor=ho(n.linearType,n.customColor,e,i),"pyramid"==n.type)for(let u=0;u<h.length;u++){if(u==h.length-1){t.tooltip&&t.tooltip.index==u&&(a.beginPath(),a.setFillStyle(io(h[u].color,n.activeOpacity)),a.moveTo(-l,-r),a.lineTo(-h[u].radius-l,0),a.lineTo(h[u].radius+l,0),a.lineTo(l,-r),a.lineTo(-l,-r),a.closePath(),a.fill()),h[u].funnelArea=[s.x-h[u].radius,s.y-r*(u+1),s.x+h[u].radius,s.y-r*u],a.beginPath(),a.setLineWidth(n.borderWidth*t.pix),a.setStrokeStyle(n.borderColor);var d=io(h[u].color,n.fillOpacity);"custom"==n.linearType&&((p=a.createLinearGradient(h[u].radius,-r,-h[u].radius,-r)).addColorStop(0,io(h[u].color,n.fillOpacity)),p.addColorStop(.5,io(n.customColor[h[u].linearIndex],n.fillOpacity)),p.addColorStop(1,io(h[u].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,-r),a.lineTo(-h[u].radius,0),a.lineTo(h[u].radius,0),a.lineTo(0,-r),a.closePath(),a.fill(),1==n.border&&a.stroke()}else t.tooltip&&t.tooltip.index==u&&(a.beginPath(),a.setFillStyle(io(h[u].color,n.activeOpacity)),a.moveTo(0,0),a.lineTo(-h[u].radius-l,0),a.lineTo(-h[u+1].radius-l,-r),a.lineTo(h[u+1].radius+l,-r),a.lineTo(h[u].radius+l,0),a.lineTo(0,0),a.closePath(),a.fill()),h[u].funnelArea=[s.x-h[u].radius,s.y-r*(u+1),s.x+h[u].radius,s.y-r*u],a.beginPath(),a.setLineWidth(n.borderWidth*t.pix),a.setStrokeStyle(n.borderColor),d=io(h[u].color,n.fillOpacity),"custom"==n.linearType&&((p=a.createLinearGradient(h[u].radius,-r,-h[u].radius,-r)).addColorStop(0,io(h[u].color,n.fillOpacity)),p.addColorStop(.5,io(n.customColor[h[u].linearIndex],n.fillOpacity)),p.addColorStop(1,io(h[u].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-h[u].radius,0),a.lineTo(-h[u+1].radius,-r),a.lineTo(h[u+1].radius,-r),a.lineTo(h[u].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),1==n.border&&a.stroke();a.translate(0,-r)}else{a.translate(0,-(h.length-1)*r);for(let e=0;e<h.length;e++){var p;if(e==h.length-1)t.tooltip&&t.tooltip.index==e&&(a.beginPath(),a.setFillStyle(io(h[e].color,n.activeOpacity)),a.moveTo(-l-n.minSize/2,0),a.lineTo(-h[e].radius-l,-r),a.lineTo(h[e].radius+l,-r),a.lineTo(l+n.minSize/2,0),a.lineTo(-l-n.minSize/2,0),a.closePath(),a.fill()),h[e].funnelArea=[s.x-h[e].radius,s.y-r,s.x+h[e].radius,s.y],a.beginPath(),a.setLineWidth(n.borderWidth*t.pix),a.setStrokeStyle(n.borderColor),d=io(h[e].color,n.fillOpacity),"custom"==n.linearType&&((p=a.createLinearGradient(h[e].radius,-r,-h[e].radius,-r)).addColorStop(0,io(h[e].color,n.fillOpacity)),p.addColorStop(.5,io(n.customColor[h[e].linearIndex],n.fillOpacity)),p.addColorStop(1,io(h[e].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-n.minSize/2,0),a.lineTo(-h[e].radius,-r),a.lineTo(h[e].radius,-r),a.lineTo(n.minSize/2,0),a.lineTo(0,0),a.closePath(),a.fill(),1==n.border&&a.stroke();else t.tooltip&&t.tooltip.index==e&&(a.beginPath(),a.setFillStyle(io(h[e].color,n.activeOpacity)),a.moveTo(0,0),a.lineTo(-h[e+1].radius-l,0),a.lineTo(-h[e].radius-l,-r),a.lineTo(h[e].radius+l,-r),a.lineTo(h[e+1].radius+l,0),a.lineTo(0,0),a.closePath(),a.fill()),h[e].funnelArea=[s.x-h[e].radius,s.y-r*(h.length-e),s.x+h[e].radius,s.y-r*(h.length-e-1)],a.beginPath(),a.setLineWidth(n.borderWidth*t.pix),a.setStrokeStyle(n.borderColor),d=io(h[e].color,n.fillOpacity),"custom"==n.linearType&&((p=a.createLinearGradient(h[e].radius,-r,-h[e].radius,-r)).addColorStop(0,io(h[e].color,n.fillOpacity)),p.addColorStop(.5,io(n.customColor[h[e].linearIndex],n.fillOpacity)),p.addColorStop(1,io(h[e].color,n.fillOpacity)),d=p),a.setFillStyle(d),a.moveTo(0,0),a.lineTo(-h[e+1].radius,0),a.lineTo(-h[e].radius,-r),a.lineTo(h[e].radius,-r),a.lineTo(h[e+1].radius,0),a.lineTo(0,0),a.closePath(),a.fill(),1==n.border&&a.stroke();a.translate(0,r)}}return a.restore(),!1!==t.dataLabel&&1===o&&kn(h,t,a,r,n.labelAlign,l,s),1===o&&Dn(h,t,a,r,n.labelAlign,0,s),{center:s,radius:c,series:h}}(n,t,i,a,e),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"line":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{type:"straight",width:2,activeType:"none",linearType:"none",onShadow:!1,animation:"vertical"},t.extra.line);n.width*=t.pix;let r=t.chartData.xAxisData,s=r.xAxisPoints,l=r.eachSpacing;var c=[];a.save();let h=0,d=t.width+l;return t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(a.translate(t._scrollDistance_,0),h=-t._scrollDistance_-2*l+t.area[3],d=h+(t.xAxis.itemCount+4)*l),e.forEach((function(e,r){let p,u,g;a.beginPath(),a.setStrokeStyle(e.color),a.moveTo(-1e4,-1e4),a.lineTo(-10001,-10001),a.stroke(),p=[].concat(t.chartData.yAxisData.ranges[e.index]),u=p.pop(),g=p.shift();var f=$o(e.data,u,g,s,l,t,i,n,o);c.push(f);var x=Ao(f,e);if("dash"==e.lineType){let i=e.dashLength?e.dashLength:8;i*=t.pix,a.setLineDash([i,i])}a.beginPath();var m=e.color;if("none"!==n.linearType&&e.linearColor&&e.linearColor.length>0){for(var y=a.createLinearGradient(t.chartData.xAxisData.startX,t.height/2,t.chartData.xAxisData.endX,t.height/2),v=0;v<e.linearColor.length;v++)y.addColorStop(e.linearColor[v][0],io(e.linearColor[v][1],1));m=y}a.setStrokeStyle(m),1==n.onShadow&&e.setShadow&&e.setShadow.length>0?a.setShadow(e.setShadow[0],e.setShadow[1],e.setShadow[2],e.setShadow[3]):a.setShadow(0,0,0,"rgba(0,0,0,0)"),a.setLineWidth(n.width),x.forEach((function(e,t){if(1===e.length)a.moveTo(e[0].x,e[0].y);else{a.moveTo(e[0].x,e[0].y);let t=0;if("curve"===n.type)for(let o=0;o<e.length;o++){let n=e[o];if(0==t&&n.x>h&&(a.moveTo(n.x,n.y),t=1),o>0&&n.x>h&&n.x<d){var i=ro(e,o-1);a.bezierCurveTo(i.ctrA.x,i.ctrA.y,i.ctrB.x,i.ctrB.y,n.x,n.y)}}if("straight"===n.type)for(let i=0;i<e.length;i++){let o=e[i];0==t&&o.x>h&&(a.moveTo(o.x,o.y),t=1),i>0&&o.x>h&&o.x<d&&a.lineTo(o.x,o.y)}if("step"===n.type)for(let i=0;i<e.length;i++){let o=e[i];0==t&&o.x>h&&(a.moveTo(o.x,o.y),t=1),i>0&&o.x>h&&o.x<d&&(a.lineTo(o.x,e[i-1].y),a.lineTo(o.x,o.y))}a.moveTo(e[0].x,e[0].y)}})),a.stroke(),a.setLineDash([]),!1!==t.dataPointShape&&Zo(f,e.color,e.pointShape,a,t),en(f,e.color,e.pointShape,a,t,n)})),!1!==t.dataLabel&&1===o&&e.forEach((function(e,n){let r,c,h;r=[].concat(t.chartData.yAxisData.ranges[e.index]),c=r.pop(),h=r.shift(),an(qo(e.data,c,h,s,l,t,i,o),e,i,a,t)})),a.restore(),{xAxisPoints:s,calPoints:c,eachSpacing:l}}(n,t,i,a,e),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"scatter":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;eo({},{type:"circle"},t.extra.scatter);let n=t.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing;var l=[];a.save();let c=0;return t.width,t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(a.translate(t._scrollDistance_,0),c=-t._scrollDistance_-2*s+t.area[3],t.xAxis.itemCount),e.forEach((function(e,n){let l,c,h;l=[].concat(t.chartData.yAxisData.ranges[e.index]),c=l.pop(),h=l.shift();var d=qo(e.data,c,h,r,s,t,i,o);a.beginPath(),a.setStrokeStyle(e.color),a.setFillStyle(e.color),a.setLineWidth(1*t.pix);var p=e.pointShape;if("diamond"===p)d.forEach((function(e,t){null!==e&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y),a.lineTo(e.x,e.y****),a.lineTo(e.x****,e.y),a.lineTo(e.x,e.y-4.5))}));else if("circle"===p)d.forEach((function(e,i){null!==e&&(a.moveTo(e.x*****t.pix,e.y),a.arc(e.x,e.y,3*t.pix,0,2*Math.PI,!1))}));else if("square"===p)d.forEach((function(e,t){null!==e&&(a.moveTo(e.x-3.5,e.y-3.5),a.rect(e.x-3.5,e.y-3.5,7,7))}));else if("triangle"===p)d.forEach((function(e,t){null!==e&&(a.moveTo(e.x,e.y-4.5),a.lineTo(e.x-4.5,e.y****),a.lineTo(e.x****,e.y****),a.lineTo(e.x,e.y-4.5))}));else if("triangle"===p)return;a.closePath(),a.fill(),a.stroke()})),!1!==t.dataLabel&&1===o&&e.forEach((function(e,n){let l,c,h;l=[].concat(t.chartData.yAxisData.ranges[e.index]),c=l.pop(),h=l.shift(),an(qo(e.data,c,h,r,s,t,i,o),e,i,a,t)})),a.restore(),{xAxisPoints:r,calPoints:l,eachSpacing:s}}(n,t,i,a,e),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bubble":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{opacity:1,border:2},t.extra.bubble);let r=t.chartData.xAxisData,s=r.xAxisPoints,l=r.eachSpacing;var c=[];a.save();let h=0;return t.width,t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(a.translate(t._scrollDistance_,0),h=-t._scrollDistance_-2*l+t.area[3],t.xAxis.itemCount),e.forEach((function(e,r){let c,h,d;c=[].concat(t.chartData.yAxisData.ranges[e.index]),h=c.pop(),d=c.shift();var p=qo(e.data,h,d,s,l,t,i,o);a.beginPath(),a.setStrokeStyle(e.color),a.setLineWidth(n.border*t.pix),a.setFillStyle(io(e.color,n.opacity)),p.forEach((function(e,i){a.moveTo(e.x+e.r,e.y),a.arc(e.x,e.y,e.r*t.pix,0,2*Math.PI,!1)})),a.closePath(),a.fill(),a.stroke(),!1!==t.dataLabel&&1===o&&p.forEach((function(o,n){a.beginPath();var r=e.textSize*t.pix||i.fontSize;a.setFontSize(r),a.setFillStyle(e.textColor||"#FFFFFF"),a.setTextAlign("center"),a.fillText(String(o.t),o.x,o.y+r/2),a.closePath(),a.stroke(),a.setTextAlign("left")}))})),a.restore(),{xAxisPoints:s,calPoints:c,eachSpacing:l}}(n,t,i,a,e),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mix":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=t.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing,l=eo({},{width:s/2,barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},t.extra.mix.column),c=eo({},{opacity:.2,gradient:!1},t.extra.mix.area),h=eo({},{width:2},t.extra.mix.line),d=t.height-t.area[2],p=[];var u=0,g=0;e.forEach((function(e,t){"column"==e.type&&(g+=1)})),a.save();let f=-2,x=r.length+2,m=0,y=t.width+s;t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(a.translate(t._scrollDistance_,0),f=Math.floor(-t._scrollDistance_/s)-2,x=f+t.xAxis.itemCount+4,m=-t._scrollDistance_-2*s+t.area[3],y=m+(t.xAxis.itemCount+4)*s),l.customColor=ho(l.linearType,l.customColor,e,i),e.forEach((function(e,n){let v,b,S;v=[].concat(t.chartData.yAxisData.ranges[e.index]),b=v.pop(),S=v.shift();var w=qo(e.data,b,S,r,s,t,i,o);if(p.push(w),"column"==e.type){w=Ro(w,s,g,u,0,t);for(let i=0;i<w.length;i++){let o=w[i];if(null!==o&&i>f&&i<x){var T=o.x-o.width/2;t.height,o.y,t.area[2],a.beginPath();var _=o.color||e.color,A=o.color||e.color;if("none"!==l.linearType){var P=a.createLinearGradient(T,o.y,T,t.height-t.area[2]);"opacity"==l.linearType?(P.addColorStop(0,io(_,l.linearOpacity)),P.addColorStop(1,io(_,1))):(P.addColorStop(0,io(l.customColor[e.linearIndex],l.linearOpacity)),P.addColorStop(l.colorStop,io(l.customColor[e.linearIndex],l.linearOpacity)),P.addColorStop(1,io(_,1))),_=P}if(l.barBorderRadius&&4===l.barBorderRadius.length||l.barBorderCircle){const e=T,i=o.y,n=o.width,r=t.height-t.area[2]-o.y;l.barBorderCircle&&(l.barBorderRadius=[n/2,n/2,0,0]);let[s,c,h,d]=l.barBorderRadius,p=Math.min(n/2,r/2);s=s>p?p:s,c=c>p?p:c,h=h>p?p:h,d=d>p?p:d,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,d=d<0?0:d,a.arc(e+s,i+s,s,-Math.PI,-Math.PI/2),a.arc(e+n-c,i+c,c,-Math.PI/2,0),a.arc(e+n-h,i+r-h,h,0,Math.PI/2),a.arc(e+d,i+r-d,d,Math.PI/2,Math.PI)}else a.moveTo(T,o.y),a.lineTo(T+o.width,o.y),a.lineTo(T+o.width,t.height-t.area[2]),a.lineTo(T,t.height-t.area[2]),a.lineTo(T,o.y),a.setLineWidth(1),a.setStrokeStyle(A);a.setFillStyle(_),a.closePath(),a.fill()}}u+=1}if("area"==e.type){let i=Ao(w,e);for(let o=0;o<i.length;o++){let n=i[o];if(a.beginPath(),a.setStrokeStyle(e.color),a.setStrokeStyle(io(e.color,c.opacity)),c.gradient){let i=a.createLinearGradient(0,t.area[0],0,t.height-t.area[2]);i.addColorStop("0",io(e.color,c.opacity)),i.addColorStop("1.0",io("#FFFFFF",.1)),a.setFillStyle(i)}else a.setFillStyle(io(e.color,c.opacity));if(a.setLineWidth(2*t.pix),n.length>1){var C=n[0];let t=n[n.length-1];a.moveTo(C.x,C.y);let i=0;if("curve"===e.style)for(let e=0;e<n.length;e++){let t=n[e];if(0==i&&t.x>m&&(a.moveTo(t.x,t.y),i=1),e>0&&t.x>m&&t.x<y){var k=ro(n,e-1);a.bezierCurveTo(k.ctrA.x,k.ctrA.y,k.ctrB.x,k.ctrB.y,t.x,t.y)}}else for(let e=0;e<n.length;e++){let t=n[e];0==i&&t.x>m&&(a.moveTo(t.x,t.y),i=1),e>0&&t.x>m&&t.x<y&&a.lineTo(t.x,t.y)}a.lineTo(t.x,d),a.lineTo(C.x,d),a.lineTo(C.x,C.y)}else{let e=n[0];a.moveTo(e.x-s/2,e.y)}a.closePath(),a.fill()}}"line"==e.type&&Ao(w,e).forEach((function(i,o){if("dash"==e.lineType){let i=e.dashLength?e.dashLength:8;i*=t.pix,a.setLineDash([i,i])}if(a.beginPath(),a.setStrokeStyle(e.color),a.setLineWidth(h.width*t.pix),1===i.length)a.moveTo(i[0].x,i[0].y);else{a.moveTo(i[0].x,i[0].y);let t=0;if("curve"==e.style)for(let e=0;e<i.length;e++){let o=i[e];if(0==t&&o.x>m&&(a.moveTo(o.x,o.y),t=1),e>0&&o.x>m&&o.x<y){var n=ro(i,e-1);a.bezierCurveTo(n.ctrA.x,n.ctrA.y,n.ctrB.x,n.ctrB.y,o.x,o.y)}}else for(let e=0;e<i.length;e++){let o=i[e];0==t&&o.x>m&&(a.moveTo(o.x,o.y),t=1),e>0&&o.x>m&&o.x<y&&a.lineTo(o.x,o.y)}a.moveTo(i[0].x,i[0].y)}a.stroke(),a.setLineDash([])})),"point"==e.type&&(e.addPoint=!0),1==e.addPoint&&"column"!==e.type&&Zo(w,e.color,e.pointShape,a,t)})),!1!==t.dataLabel&&1===o&&(u=0,e.forEach((function(e,n){let l,c,h;l=[].concat(t.chartData.yAxisData.ranges[e.index]),c=l.pop(),h=l.shift();var d=qo(e.data,c,h,r,s,t,i,o);"column"!==e.type?an(d,e,i,a,t):(an(d=Ro(d,s,g,u,0,t),e,i,a,t),u+=1)})));return a.restore(),{xAxisPoints:r,calPoints:p,eachSpacing:s}}(n,t,i,a,e),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"column":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=t.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing,l=eo({},{type:"group",width:s/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0,labelPosition:"outside"},t.extra.column),c=[];a.save();let h=-2,d=r.length+2;return t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(a.translate(t._scrollDistance_,0),h=Math.floor(-t._scrollDistance_/s)-2,d=h+t.xAxis.itemCount+4),t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===o&&pn(t.tooltip.offset.x,t,0,a,s),l.customColor=ho(l.linearType,l.customColor,e,i),e.forEach((function(n,p){let u,g,f;u=[].concat(t.chartData.yAxisData.ranges[n.index]),g=u.pop(),f=u.shift();let x=(t.height-t.area[0]-t.area[2])*(0-g)/(f-g),m=t.height-Math.round(x)-t.area[2];n.zeroPoints=m;var y=n.data;switch(l.type){case"group":var v=Ho(y,g,f,r,s,t,i,m,o),b=Jo(y,g,f,r,s,t,i,p,e,o);c.push(b),v=Ro(v,s,e.length,p,0,t);for(let e=0;e<v.length;e++){let i=v[e];if(null!==i&&e>h&&e<d){var S=i.x-i.width/2,w=t.height-i.y-t.area[2];a.beginPath();var T=i.color||n.color,_=i.color||n.color;if("none"!==l.linearType){var A=a.createLinearGradient(S,i.y,S,m);"opacity"==l.linearType?(A.addColorStop(0,io(T,l.linearOpacity)),A.addColorStop(1,io(T,1))):(A.addColorStop(0,io(l.customColor[n.linearIndex],l.linearOpacity)),A.addColorStop(l.colorStop,io(l.customColor[n.linearIndex],l.linearOpacity)),A.addColorStop(1,io(T,1))),T=A}if(l.barBorderRadius&&4===l.barBorderRadius.length||!0===l.barBorderCircle){const e=S,t=i.y>m?m:i.y,o=i.width,n=Math.abs(m-i.y);l.barBorderCircle&&(l.barBorderRadius=[o/2,o/2,0,0]),i.y>m&&(l.barBorderRadius=[0,0,o/2,o/2]);let[r,s,c,h]=l.barBorderRadius,d=Math.min(o/2,n/2);r=r>d?d:r,s=s>d?d:s,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,a.arc(e+r,t+r,r,-Math.PI,-Math.PI/2),a.arc(e+o-s,t+s,s,-Math.PI/2,0),a.arc(e+o-c,t+n-c,c,0,Math.PI/2),a.arc(e+h,t+n-h,h,Math.PI/2,Math.PI)}else a.moveTo(S,i.y),a.lineTo(S+i.width,i.y),a.lineTo(S+i.width,m),a.lineTo(S,m),a.lineTo(S,i.y),a.setLineWidth(1),a.setStrokeStyle(_);a.setFillStyle(T),a.closePath(),a.fill()}}break;case"stack":v=Jo(y,g,f,r,s,t,i,p,e,o),c.push(v),v=No(v,s,e.length,0,0,t);for(let e=0;e<v.length;e++){let i=v[e];if(null!==i&&e>h&&e<d){a.beginPath(),T=i.color||n.color,S=i.x-i.width/2+1,w=t.height-i.y-t.area[2];var P=t.height-i.y0-t.area[2];p>0&&(w-=P),a.setFillStyle(T),a.moveTo(S,i.y),a.fillRect(S,i.y,i.width,w),a.closePath(),a.fill()}}break;case"meter":v=qo(y,g,f,r,s,t,i,o),c.push(v),v=Bo(v,s,e.length,p,0,t,l.meterBorder);for(let e=0;e<v.length;e++){let i=v[e];if(null!==i&&e>h&&e<d){if(a.beginPath(),0==p&&l.meterBorder>0&&(a.setStrokeStyle(n.color),a.setLineWidth(l.meterBorder*t.pix)),0==p?a.setFillStyle(l.meterFillColor):a.setFillStyle(i.color||n.color),S=i.x-i.width/2,w=t.height-i.y-t.area[2],l.barBorderRadius&&4===l.barBorderRadius.length||!0===l.barBorderCircle){const e=S,t=i.y,o=i.width,n=m-i.y;l.barBorderCircle&&(l.barBorderRadius=[o/2,o/2,0,0]);let[r,s,c,h]=l.barBorderRadius,d=Math.min(o/2,n/2);r=r>d?d:r,s=s>d?d:s,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,a.arc(e+r,t+r,r,-Math.PI,-Math.PI/2),a.arc(e+o-s,t+s,s,-Math.PI/2,0),a.arc(e+o-c,t+n-c,c,0,Math.PI/2),a.arc(e+h,t+n-h,h,Math.PI/2,Math.PI),a.fill()}else a.moveTo(S,i.y),a.lineTo(S+i.width,i.y),a.lineTo(S+i.width,m),a.lineTo(S,m),a.lineTo(S,i.y),a.fill();0==p&&l.meterBorder>0&&(a.closePath(),a.stroke())}}}})),!1!==t.dataLabel&&1===o&&e.forEach((function(n,c){let h,d,p;h=[].concat(t.chartData.yAxisData.ranges[n.index]),d=h.pop(),p=h.shift();var u=n.data;switch(l.type){case"group":on(Ro(Ho(u,d,p,r,s,t,i,o),s,e.length,c,0,t),n,i,a,t);break;case"stack":on(Jo(u,d,p,r,s,t,i,c,e,o),n,i,a,t);break;case"meter":on(qo(u,d,p,r,s,t,i,o),n,i,a,t)}})),a.restore(),{xAxisPoints:r,calPoints:c,eachSpacing:s}}(n,t,i,a,e),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"mount":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=t.chartData.xAxisData,r=n.xAxisPoints,s=n.eachSpacing,l=eo({},{type:"mount",widthRatio:1,borderWidth:1,barBorderCircle:!1,barBorderRadius:[],linearType:"none",linearOpacity:1,customColor:[],colorStop:0},t.extra.mount);l.widthRatio=l.widthRatio<=0?0:l.widthRatio,l.widthRatio=l.widthRatio>=2?2:l.widthRatio,a.save();let c,h,d,p=-2,u=r.length+2;t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(a.translate(t._scrollDistance_,0),p=Math.floor(-t._scrollDistance_/s)-2,u=p+t.xAxis.itemCount+4),l.customColor=ho(l.linearType,l.customColor,e,i),c=[].concat(t.chartData.yAxisData.ranges[0]),h=c.pop(),d=c.shift();let g=(t.height-t.area[0]-t.area[2])*(0-h)/(d-h),f=t.height-Math.round(g)-t.area[2];var x=Go(e,h,d,r,s,t,l,f,o);switch(l.type){case"bar":for(let i=0;i<x.length;i++){let o=x[i];if(null!==o&&i>p&&i<u){var m=o.x-s*l.widthRatio/2,y=t.height-o.y-t.area[2];a.beginPath();var v=o.color||e[i].color,b=o.color||e[i].color;if("none"!==l.linearType){var S=a.createLinearGradient(m,o.y,m,f);"opacity"==l.linearType?(S.addColorStop(0,io(v,l.linearOpacity)),S.addColorStop(1,io(v,1))):(S.addColorStop(0,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(1,io(v,1))),v=S}if(l.barBorderRadius&&4===l.barBorderRadius.length||!0===l.barBorderCircle){const e=m,t=o.y>f?f:o.y,i=o.width,n=Math.abs(f-o.y);l.barBorderCircle&&(l.barBorderRadius=[i/2,i/2,0,0]),o.y>f&&(l.barBorderRadius=[0,0,i/2,i/2]);let[r,s,c,h]=l.barBorderRadius,d=Math.min(i/2,n/2);r=r>d?d:r,s=s>d?d:s,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,s=s<0?0:s,c=c<0?0:c,h=h<0?0:h,a.arc(e+r,t+r,r,-Math.PI,-Math.PI/2),a.arc(e+i-s,t+s,s,-Math.PI/2,0),a.arc(e+i-c,t+n-c,c,0,Math.PI/2),a.arc(e+h,t+n-h,h,Math.PI/2,Math.PI)}else a.moveTo(m,o.y),a.lineTo(m+o.width,o.y),a.lineTo(m+o.width,f),a.lineTo(m,f),a.lineTo(m,o.y);a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*t.pix),a.closePath(),a.stroke()),a.fill()}}break;case"triangle":for(let i=0;i<x.length;i++){let o=x[i];null!==o&&i>p&&i<u&&(m=o.x-s*l.widthRatio/2,y=t.height-o.y-t.area[2],a.beginPath(),v=o.color||e[i].color,b=o.color||e[i].color,"none"!==l.linearType&&(S=a.createLinearGradient(m,o.y,m,f),"opacity"==l.linearType?(S.addColorStop(0,io(v,l.linearOpacity)),S.addColorStop(1,io(v,1))):(S.addColorStop(0,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(1,io(v,1))),v=S),a.moveTo(m,f),a.lineTo(o.x,o.y),a.lineTo(m+o.width,f),a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*t.pix),a.stroke()),a.fill())}break;case"mount":for(let i=0;i<x.length;i++){let o=x[i];null!==o&&i>p&&i<u&&(m=o.x-s*l.widthRatio/2,y=t.height-o.y-t.area[2],a.beginPath(),v=o.color||e[i].color,b=o.color||e[i].color,"none"!==l.linearType&&(S=a.createLinearGradient(m,o.y,m,f),"opacity"==l.linearType?(S.addColorStop(0,io(v,l.linearOpacity)),S.addColorStop(1,io(v,1))):(S.addColorStop(0,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(1,io(v,1))),v=S),a.moveTo(m,f),a.bezierCurveTo(o.x-o.width/4,f,o.x-o.width/4,o.y,o.x,o.y),a.bezierCurveTo(o.x+o.width/4,o.y,o.x+o.width/4,f,m+o.width,f),a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*t.pix),a.stroke()),a.fill())}break;case"sharp":for(let i=0;i<x.length;i++){let o=x[i];null!==o&&i>p&&i<u&&(m=o.x-s*l.widthRatio/2,y=t.height-o.y-t.area[2],a.beginPath(),v=o.color||e[i].color,b=o.color||e[i].color,"none"!==l.linearType&&(S=a.createLinearGradient(m,o.y,m,f),"opacity"==l.linearType?(S.addColorStop(0,io(v,l.linearOpacity)),S.addColorStop(1,io(v,1))):(S.addColorStop(0,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(l.colorStop,io(l.customColor[e[i].linearIndex],l.linearOpacity)),S.addColorStop(1,io(v,1))),v=S),a.moveTo(m,f),a.quadraticCurveTo(o.x-0,f-y/4,o.x,o.y),a.quadraticCurveTo(o.x+0,f-y/4,m+o.width,f),a.setStrokeStyle(b),a.setFillStyle(v),l.borderWidth>0&&(a.setLineWidth(l.borderWidth*t.pix),a.stroke()),a.fill())}}if(!1!==t.dataLabel&&1===o){let n,c,h;n=[].concat(t.chartData.yAxisData.ranges[0]),c=n.pop(),h=n.shift(),nn(x=Go(e,c,h,r,s,t,l,f,o),e,i,a,t,f)}return a.restore(),{xAxisPoints:r,calPoints:x,eachSpacing:s}}(n,t,i,a,e),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"bar":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),xn(r,t,i,a);var o=function(e,t,i,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=[],r=(t.height-t.area[0]-t.area[2])/t.categories.length;for(let d=0;d<t.categories.length;d++)n.push(t.area[0]+r/2+r*d);let s=eo({},{type:"group",width:r/2,meterBorder:4,meterFillColor:"#FFFFFF",barBorderCircle:!1,barBorderRadius:[],seriesGap:2,linearType:"none",linearOpacity:1,customColor:[],colorStop:0},t.extra.bar),l=[];a.save();let c=-2,h=n.length+2;return t.tooltip&&t.tooltip.textList&&t.tooltip.textList.length&&1===o&&un(t.tooltip.offset.y,t,0,a,r),s.customColor=ho(s.linearType,s.customColor,e,i),e.forEach((function(d,p){let u,g,f;u=[].concat(t.chartData.xAxisData.ranges),f=u.pop(),g=u.shift();var x=d.data;switch(s.type){case"group":var m=Ko(x,g,f,n,r,t,i,o),y=Xo(x,g,f,n,r,t,i,p,e,o);l.push(y),m=zo(m,r,e.length,p,0,t);for(let e=0;e<m.length;e++){let i=m[e];if(null!==i&&e>c&&e<h){var v=t.area[3],b=i.y-i.width/2;i.height,a.beginPath();var S=i.color||d.color,w=i.color||d.color;if("none"!==s.linearType){var T=a.createLinearGradient(v,i.y,i.x,i.y);"opacity"==s.linearType?(T.addColorStop(0,io(S,s.linearOpacity)),T.addColorStop(1,io(S,1))):(T.addColorStop(0,io(s.customColor[d.linearIndex],s.linearOpacity)),T.addColorStop(s.colorStop,io(s.customColor[d.linearIndex],s.linearOpacity)),T.addColorStop(1,io(S,1))),S=T}if(s.barBorderRadius&&4===s.barBorderRadius.length||!0===s.barBorderCircle){const e=v,t=i.width,o=i.y-i.width/2,n=i.height;s.barBorderCircle&&(s.barBorderRadius=[t/2,t/2,0,0]);let[r,l,c,h]=s.barBorderRadius,d=Math.min(t/2,n/2);r=r>d?d:r,l=l>d?d:l,c=c>d?d:c,h=h>d?d:h,r=r<0?0:r,l=l<0?0:l,c=c<0?0:c,h=h<0?0:h,a.arc(e+h,o+h,h,-Math.PI,-Math.PI/2),a.arc(i.x-r,o+r,r,-Math.PI/2,0),a.arc(i.x-l,o+t-l,l,0,Math.PI/2),a.arc(e+c,o+t-c,c,Math.PI/2,Math.PI)}else a.moveTo(v,b),a.lineTo(i.x,b),a.lineTo(i.x,b+i.width),a.lineTo(v,b+i.width),a.lineTo(v,b),a.setLineWidth(1),a.setStrokeStyle(w);a.setFillStyle(S),a.closePath(),a.fill()}}break;case"stack":m=Xo(x,g,f,n,r,t,i,p,e,o),l.push(m),m=Uo(m,r,e.length,0,0,t);for(let e=0;e<m.length;e++){let t=m[e];null!==t&&e>c&&e<h&&(a.beginPath(),S=t.color||d.color,v=t.x0,a.setFillStyle(S),a.moveTo(v,t.y-t.width/2),a.fillRect(v,t.y-t.width/2,t.height,t.width),a.closePath(),a.fill())}}})),!1!==t.dataLabel&&1===o&&e.forEach((function(l,c){let h,d,p;h=[].concat(t.chartData.xAxisData.ranges),p=h.pop(),d=h.shift();var u=l.data;switch(s.type){case"group":rn(zo(Ko(u,d,p,n,r,t,i,o),r,e.length,c,0,t),l,i,a,t);break;case"stack":rn(Xo(u,d,p,n,r,t,i,c,e,o),l,i,a,t)}})),{yAxisPoints:n,calPoints:l,eachSpacing:r}}(n,t,i,a,e),s=o.yAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.yAxisPoints=s,t.chartData.xAxisPoints=t.chartData.xAxisData.xAxisPoints,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"area":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{type:"straight",opacity:.2,addLine:!1,width:2,gradient:!1,activeType:"none"},t.extra.area);let r=t.chartData.xAxisData,s=r.xAxisPoints,l=r.eachSpacing,c=t.height-t.area[2],h=[];a.save();let d=0,p=t.width+l;return t._scrollDistance_&&0!==t._scrollDistance_&&!0===t.enableScroll&&(a.translate(t._scrollDistance_,0),d=-t._scrollDistance_-2*l+t.area[3],p=d+(t.xAxis.itemCount+4)*l),e.forEach((function(e,r){let u,g,f;u=[].concat(t.chartData.yAxisData.ranges[e.index]),g=u.pop(),f=u.shift();let x=qo(e.data,g,f,s,l,t,i,o);h.push(x);let m=Ao(x,e);for(let i=0;i<m.length;i++){let o=m[i];if(a.beginPath(),a.setStrokeStyle(io(e.color,n.opacity)),n.gradient){let i=a.createLinearGradient(0,t.area[0],0,t.height-t.area[2]);i.addColorStop("0",io(e.color,n.opacity)),i.addColorStop("1.0",io("#FFFFFF",.1)),a.setFillStyle(i)}else a.setFillStyle(io(e.color,n.opacity));if(a.setLineWidth(n.width*t.pix),o.length>1){let e=o[0],t=o[o.length-1];a.moveTo(e.x,e.y);let i=0;if("curve"===n.type)for(let n=0;n<o.length;n++){let e=o[n];if(0==i&&e.x>d&&(a.moveTo(e.x,e.y),i=1),n>0&&e.x>d&&e.x<p){let t=ro(o,n-1);a.bezierCurveTo(t.ctrA.x,t.ctrA.y,t.ctrB.x,t.ctrB.y,e.x,e.y)}}if("straight"===n.type)for(let n=0;n<o.length;n++){let e=o[n];0==i&&e.x>d&&(a.moveTo(e.x,e.y),i=1),n>0&&e.x>d&&e.x<p&&a.lineTo(e.x,e.y)}if("step"===n.type)for(let n=0;n<o.length;n++){let e=o[n];0==i&&e.x>d&&(a.moveTo(e.x,e.y),i=1),n>0&&e.x>d&&e.x<p&&(a.lineTo(e.x,o[n-1].y),a.lineTo(e.x,e.y))}a.lineTo(t.x,c),a.lineTo(e.x,c),a.lineTo(e.x,e.y)}else{let e=o[0];a.moveTo(e.x-l/2,e.y)}if(a.closePath(),a.fill(),n.addLine){if("dash"==e.lineType){let i=e.dashLength?e.dashLength:8;i*=t.pix,a.setLineDash([i,i])}if(a.beginPath(),a.setStrokeStyle(e.color),a.setLineWidth(n.width*t.pix),1===o.length)a.moveTo(o[0].x,o[0].y);else{a.moveTo(o[0].x,o[0].y);let e=0;if("curve"===n.type)for(let t=0;t<o.length;t++){let i=o[t];if(0==e&&i.x>d&&(a.moveTo(i.x,i.y),e=1),t>0&&i.x>d&&i.x<p){let e=ro(o,t-1);a.bezierCurveTo(e.ctrA.x,e.ctrA.y,e.ctrB.x,e.ctrB.y,i.x,i.y)}}if("straight"===n.type)for(let t=0;t<o.length;t++){let i=o[t];0==e&&i.x>d&&(a.moveTo(i.x,i.y),e=1),t>0&&i.x>d&&i.x<p&&a.lineTo(i.x,i.y)}if("step"===n.type)for(let t=0;t<o.length;t++){let i=o[t];0==e&&i.x>d&&(a.moveTo(i.x,i.y),e=1),t>0&&i.x>d&&i.x<p&&(a.lineTo(i.x,o[t-1].y),a.lineTo(i.x,i.y))}a.moveTo(o[0].x,o[0].y)}a.stroke(),a.setLineDash([])}}!1!==t.dataPointShape&&Zo(x,e.color,e.pointShape,a,t),en(x,e.color,e.pointShape,a,t,n,r)})),!1!==t.dataLabel&&1===o&&e.forEach((function(e,n){let r,c,h;r=[].concat(t.chartData.yAxisData.ranges[e.index]),c=r.pop(),h=r.shift(),an(qo(e.data,c,h,s,l,t,i,o),e,i,a,t)})),a.restore(),{xAxisPoints:s,calPoints:h,eachSpacing:l}}(n,t,i,a,e),s=o.xAxisPoints,l=o.calPoints,c=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=l,t.chartData.eachSpacing=c,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"ring":case"pie":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),t.chartData.pieData=bn(n,t,i,a,e),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"rose":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),t.chartData.pieData=function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{type:"area",activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF",linearType:"none",customColor:[]},t.extra.rose);0==i.pieChartLinePadding&&(i.pieChartLinePadding=n.activeRadius*t.pix);var r={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2},s=Math.min((t.width-t.area[1]-t.area[3])/2-i.pieChartLinePadding-i.pieChartTextPadding-i._pieTextMaxLength_,(t.height-t.area[0]-t.area[2])/2-i.pieChartLinePadding-i.pieChartTextPadding);s=s<10?10:s;var l=n.minRadius||.5*s;s<l&&(s=l+10),e=Io(e,n.type,l,s,o);var c=n.activeRadius*t.pix;return n.customColor=ho(n.linearType,n.customColor,e,i),(e=e.map((function(e){return e._start_+=(n.offsetAngle||0)*Math.PI/180,e}))).forEach((function(e,i){t.tooltip&&t.tooltip.index==i&&(a.beginPath(),a.setFillStyle(io(e.color,n.activeOpacity||.5)),a.moveTo(r.x,r.y),a.arc(r.x,r.y,c+e._radius_,e._start_,e._start_+2*e._rose_proportion_*Math.PI),a.closePath(),a.fill()),a.beginPath(),a.setLineWidth(n.borderWidth*t.pix),a.lineJoin="round",a.setStrokeStyle(n.borderColor);var o,s=e.color;"custom"==n.linearType&&((o=a.createCircularGradient?a.createCircularGradient(r.x,r.y,e._radius_):a.createRadialGradient(r.x,r.y,0,r.x,r.y,e._radius_)).addColorStop(0,io(n.customColor[e.linearIndex],1)),o.addColorStop(1,io(e.color,1)),s=o),a.setFillStyle(s),a.moveTo(r.x,r.y),a.arc(r.x,r.y,e._radius_,e._start_,e._start_+2*e._rose_proportion_*Math.PI),a.closePath(),a.fill(),1==n.border&&a.stroke()})),!1!==t.dataLabel&&1===o&&cn(e,t,i,a,0,r),{center:r,radius:s,series:e}}(n,t,i,a,e),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"radar":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),t.chartData.radarData=function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,n=eo({},{gridColor:"#cccccc",gridType:"radar",gridEval:1,axisLabel:!1,axisLabelTofix:0,labelShow:!0,labelColor:"#666666",labelPointShow:!1,labelPointRadius:3,labelPointColor:"#cccccc",opacity:.2,gridCount:3,border:!1,borderWidth:2,linearType:"none",customColor:[]},t.extra.radar),r=yo(t.categories.length),s={x:t.area[3]+(t.width-t.area[1]-t.area[3])/2,y:t.area[0]+(t.height-t.area[0]-t.area[2])/2},l=(t.width-t.area[1]-t.area[3])/2,c=(t.height-t.area[0]-t.area[2])/2,h=Math.min(l-(mo(t.categories,i.fontSize,a)+i.radarLabelTextMargin),c-i.radarLabelTextMargin);h=(h-=i.radarLabelTextMargin*t.pix)<10?10:h,h=n.radius?n.radius:h,a.beginPath(),a.setLineWidth(1*t.pix),a.setStrokeStyle(n.gridColor),r.forEach((function(e,t){var i=so(h*Math.cos(e),h*Math.sin(e),s);a.moveTo(s.x,s.y),t%n.gridEval==0&&a.lineTo(i.x,i.y)})),a.stroke(),a.closePath();for(var d=function(e){var i={};if(a.beginPath(),a.setLineWidth(1*t.pix),a.setStrokeStyle(n.gridColor),"radar"==n.gridType)r.forEach((function(t,o){var r=so(h/n.gridCount*e*Math.cos(t),h/n.gridCount*e*Math.sin(t),s);0===o?(i=r,a.moveTo(r.x,r.y)):a.lineTo(r.x,r.y)})),a.lineTo(i.x,i.y);else{var o=so(h/n.gridCount*e*Math.cos(1.5),h/n.gridCount*e*Math.sin(1.5),s);a.arc(s.x,s.y,s.y-o.y,0,2*Math.PI,!1)}a.stroke(),a.closePath()},p=1;p<=n.gridCount;p++)d(p);n.customColor=ho(n.linearType,n.customColor,e,i);var u=ko(r,s,h,e,t,o);if(u.forEach((function(i,o){a.beginPath(),a.setLineWidth(n.borderWidth*t.pix),a.setStrokeStyle(i.color);var r,l=io(i.color,n.opacity);"custom"==n.linearType&&((r=a.createCircularGradient?a.createCircularGradient(s.x,s.y,h):a.createRadialGradient(s.x,s.y,0,s.x,s.y,h)).addColorStop(0,io(n.customColor[e[o].linearIndex],n.opacity)),r.addColorStop(1,io(i.color,n.opacity)),l=r),a.setFillStyle(l),i.data.forEach((function(e,t){0===t?a.moveTo(e.position.x,e.position.y):a.lineTo(e.position.x,e.position.y)})),a.closePath(),a.fill(),!0===n.border&&a.stroke(),a.closePath(),!1!==t.dataPointShape&&Zo(i.data.map((function(e){return e.position})),i.color,i.pointShape,a,t)})),!0===n.axisLabel){const i=Math.max(n.max,Math.max.apply(null,uo(e))),o=h/n.gridCount,r=t.fontSize*t.pix;for(a.setFontSize(r),a.setFillStyle(t.fontColor),a.setTextAlign("left"),p=0;p<n.gridCount+1;p++){let e=p*i/n.gridCount;e=e.toFixed(n.axisLabelTofix),a.fillText(String(e),s.x+3*t.pix,s.y-p*o+r/2)}}return ln(r,h,s,t,i,a),!1!==t.dataLabel&&1===o&&(u.forEach((function(e,o){a.beginPath();var n=e.textSize*t.pix||i.fontSize;a.setFontSize(n),a.setFillStyle(e.textColor||t.fontColor),e.data.forEach((function(e,t){Math.abs(e.position.x-s.x)<2?e.position.y<s.y?(a.setTextAlign("center"),a.fillText(e.value,e.position.x,e.position.y-4)):(a.setTextAlign("center"),a.fillText(e.value,e.position.x,e.position.y+n+2)):e.position.x<s.x?(a.setTextAlign("right"),a.fillText(e.value,e.position.x-4,e.position.y+n/2-2)):(a.setTextAlign("left"),a.fillText(e.value,e.position.x+4,e.position.y+n/2-2))})),a.closePath(),a.stroke()})),a.setTextAlign("left")),{center:s,radius:h,angleList:r}}(n,t,i,a,e),vn(t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"arcbar":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),t.chartData.arcbarData=function(e,t,i,a){var o,n,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=eo({},{startAngle:.75,endAngle:.25,type:"default",direction:"cw",lineCap:"round",width:12,gap:2,linearType:"none",customColor:[]},t.extra.arcbar);e=Mo(e,s,r),o=s.centerX||s.centerY?{x:s.centerX?s.centerX:t.width/2,y:s.centerY?s.centerY:t.height/2}:{x:t.width/2,y:t.height/2},s.radius?n=s.radius:(n=Math.min(o.x,o.y),n-=5*t.pix,n-=s.width/2),n=n<10?10:n,s.customColor=ho(s.linearType,s.customColor,e,i);for(let h=0;h<e.length;h++){let i=e[h];a.setLineWidth(s.width*t.pix),a.setStrokeStyle(s.backgroundColor||"#E9E9E9"),a.setLineCap(s.lineCap),a.beginPath(),"default"==s.type?a.arc(o.x,o.y,n-(s.width*t.pix+s.gap*t.pix)*h,s.startAngle*Math.PI,s.endAngle*Math.PI,"ccw"==s.direction):a.arc(o.x,o.y,n-(s.width*t.pix+s.gap*t.pix)*h,0,2*Math.PI,"ccw"==s.direction),a.stroke();var l=i.color;if("custom"==s.linearType){var c=a.createLinearGradient(o.x-n,o.y,o.x+n,o.y);c.addColorStop(1,io(s.customColor[i.linearIndex],1)),c.addColorStop(0,io(i.color,1)),l=c}a.setLineWidth(s.width*t.pix),a.setStrokeStyle(l),a.setLineCap(s.lineCap),a.beginPath(),a.arc(o.x,o.y,n-(s.width*t.pix+s.gap*t.pix)*h,s.startAngle*Math.PI,i._proportion_*Math.PI,"ccw"==s.direction),a.stroke()}return tn(t,i,a,o),{center:o,radius:n,series:e}}(n,t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"gauge":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),t.chartData.gaugeData=function(e,t,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=eo({},{type:"default",startAngle:.75,endAngle:.25,width:15,labelOffset:13,splitLine:{fixRadius:0,splitNumber:10,width:15,color:"#FFFFFF",childNumber:5,childWidth:5},pointer:{width:15,color:"auto"}},i.extra.gauge);null==r.oldAngle&&(r.oldAngle=r.startAngle),null==r.oldData&&(r.oldData=0),e=Oo(e,r.startAngle,r.endAngle);var s={x:i.width/2,y:i.height/2},l=Math.min(s.x,s.y);l-=5*i.pix;var c=(l=(l-=r.width/2)<10?10:l)-r.width,h=0;if("progress"==r.type){var d=l-3*r.width;o.beginPath();let e=o.createLinearGradient(s.x,s.y-d,s.x,s.y+d);e.addColorStop("0",io(t[0].color,.3)),e.addColorStop("1.0",io("#FFFFFF",.1)),o.setFillStyle(e),o.arc(s.x,s.y,d,0,2*Math.PI,!1),o.fill(),o.setLineWidth(r.width),o.setStrokeStyle(io(t[0].color,.3)),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,c,r.startAngle*Math.PI,r.endAngle*Math.PI,!1),o.stroke(),h=r.endAngle<r.startAngle?2+r.endAngle-r.startAngle:r.startAngle-r.endAngle,r.splitLine.splitNumber;let a=h/r.splitLine.splitNumber/r.splitLine.childNumber,p=-l-.5*r.width-r.splitLine.fixRadius,u=-l-r.width-r.splitLine.fixRadius+r.splitLine.width;o.save(),o.translate(s.x,s.y),o.rotate((r.startAngle-1)*Math.PI);let g=r.splitLine.splitNumber*r.splitLine.childNumber+1,f=t[0].data*n;for(let n=0;n<g;n++)o.beginPath(),f>n/g?o.setStrokeStyle(io(t[0].color,1)):o.setStrokeStyle(io(t[0].color,.3)),o.setLineWidth(3*i.pix),o.moveTo(p,0),o.lineTo(u,0),o.stroke(),o.rotate(a*Math.PI);o.restore(),t=Fo(t,r,n),o.setLineWidth(r.width),o.setStrokeStyle(t[0].color),o.setLineCap("round"),o.beginPath(),o.arc(s.x,s.y,c,r.startAngle*Math.PI,t[0]._proportion_*Math.PI,!1),o.stroke();let x=l-2.5*r.width;o.save(),o.translate(s.x,s.y),o.rotate((t[0]._proportion_-1)*Math.PI),o.beginPath(),o.setLineWidth(r.width/3);let m=o.createLinearGradient(0,.6*-x,0,.6*x);m.addColorStop("0",io("#FFFFFF",0)),m.addColorStop("0.5",io(t[0].color,1)),m.addColorStop("1.0",io("#FFFFFF",0)),o.setStrokeStyle(m),o.arc(0,0,x,.85*Math.PI,1.15*Math.PI,!1),o.stroke(),o.beginPath(),o.setLineWidth(1),o.setStrokeStyle(t[0].color),o.setFillStyle(t[0].color),o.moveTo(-x-r.width/3/2,-4),o.lineTo(-x-r.width/3/2-4,0),o.lineTo(-x-r.width/3/2,4),o.lineTo(-x-r.width/3/2,-4),o.stroke(),o.fill(),o.restore()}else{o.setLineWidth(r.width),o.setLineCap("butt");for(let t=0;t<e.length;t++){let i=e[t];o.beginPath(),o.setStrokeStyle(i.color),o.arc(s.x,s.y,l,i._startAngle_*Math.PI,i._endAngle_*Math.PI,!1),o.stroke()}o.save();let d=(h=r.endAngle<r.startAngle?2+r.endAngle-r.startAngle:r.startAngle-r.endAngle)/r.splitLine.splitNumber,p=h/r.splitLine.splitNumber/r.splitLine.childNumber,u=-l-.5*r.width-r.splitLine.fixRadius,g=-l-.5*r.width-r.splitLine.fixRadius+r.splitLine.width,f=-l-.5*r.width-r.splitLine.fixRadius+r.splitLine.childWidth;o.translate(s.x,s.y),o.rotate((r.startAngle-1)*Math.PI);for(let e=0;e<r.splitLine.splitNumber+1;e++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(2*i.pix),o.moveTo(u,0),o.lineTo(g,0),o.stroke(),o.rotate(d*Math.PI);o.restore(),o.save(),o.translate(s.x,s.y),o.rotate((r.startAngle-1)*Math.PI);for(let e=0;e<r.splitLine.splitNumber*r.splitLine.childNumber+1;e++)o.beginPath(),o.setStrokeStyle(r.splitLine.color),o.setLineWidth(1*i.pix),o.moveTo(u,0),o.lineTo(f,0),o.stroke(),o.rotate(p*Math.PI);o.restore(),t=Eo(t,e,r,n);for(let e=0;e<t.length;e++){let i=t[e];o.save(),o.translate(s.x,s.y),o.rotate((i._proportion_-1)*Math.PI),o.beginPath(),o.setFillStyle(i.color),o.moveTo(r.pointer.width,0),o.lineTo(0,-r.pointer.width/2),o.lineTo(-c,0),o.lineTo(0,r.pointer.width/2),o.lineTo(r.pointer.width,0),o.closePath(),o.fill(),o.beginPath(),o.setFillStyle("#FFFFFF"),o.arc(0,0,r.pointer.width/6,0,2*Math.PI,!1),o.fill(),o.restore()}!1!==i.dataLabel&&sn(r,l,s,i,a,o)}return tn(i,a,o,s),1===n&&"gauge"===i.type&&(i.extra.gauge.oldAngle=t[0]._proportion_,i.extra.gauge.oldData=t[0].data),{center:s,radius:l,innerRadius:c,categories:e,totalAngle:h}}(r,n,t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}});break;case"candle":this.animationInstance=new Mn({timing:t.timing,duration:s,onProcess:function(e){a.clearRect(0,0,t.width,t.height),t.rotate&&Qo(a,t),mn(0,t,0,a),xn(r,t,i,a);var o=function(e,t,i,a,o){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,r=eo({},{color:{},average:{}},i.extra.candle);r.color=eo({},{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},r.color),r.average=eo({},{show:!1,name:[],day:[],color:a.color},r.average),i.extra.candle=r;let s=i.chartData.xAxisData,l=s.xAxisPoints,c=s.eachSpacing,h=[];o.save();let d=-2,p=l.length+2,u=0,g=i.width+c;return i._scrollDistance_&&0!==i._scrollDistance_&&!0===i.enableScroll&&(o.translate(i._scrollDistance_,0),d=Math.floor(-i._scrollDistance_/c)-2,p=d+i.xAxis.itemCount+4,u=-i._scrollDistance_-2*c+i.area[3],g=u+(i.xAxis.itemCount+4)*c),(r.average.show||t)&&t.forEach((function(e,t){let r,s,h;r=[].concat(i.chartData.yAxisData.ranges[e.index]),s=r.pop(),h=r.shift();var d=Ao(qo(e.data,s,h,l,c,i,a,n),e);for(let i=0;i<d.length;i++){let t=d[i];if(o.beginPath(),o.setStrokeStyle(e.color),o.setLineWidth(1),1===t.length)o.moveTo(t[0].x,t[0].y),o.arc(t[0].x,t[0].y,1,0,2*Math.PI);else{o.moveTo(t[0].x,t[0].y);let e=0;for(let i=0;i<t.length;i++){let a=t[i];if(0==e&&a.x>u&&(o.moveTo(a.x,a.y),e=1),i>0&&a.x>u&&a.x<g){var p=ro(t,i-1);o.bezierCurveTo(p.ctrA.x,p.ctrA.y,p.ctrB.x,p.ctrB.y,a.x,a.y)}}o.moveTo(t[0].x,t[0].y)}o.closePath(),o.stroke()}})),e.forEach((function(e,t){let s,u,g;s=[].concat(i.chartData.yAxisData.ranges[e.index]),u=s.pop(),g=s.shift();var f=e.data,x=jo(f,u,g,l,c,i,a,n);h.push(x);var m=Ao(x,e);for(let a=0;a<m[0].length;a++)if(a>d&&a<p){let e=m[0][a];o.beginPath(),f[a][1]-f[a][0]>0?(o.setStrokeStyle(r.color.upLine),o.setFillStyle(r.color.upFill),o.setLineWidth(1*i.pix),o.moveTo(e[3].x,e[3].y),o.lineTo(e[1].x,e[1].y),o.lineTo(e[1].x-c/4,e[1].y),o.lineTo(e[0].x-c/4,e[0].y),o.lineTo(e[0].x,e[0].y),o.lineTo(e[2].x,e[2].y),o.lineTo(e[0].x,e[0].y),o.lineTo(e[0].x+c/4,e[0].y),o.lineTo(e[1].x+c/4,e[1].y),o.lineTo(e[1].x,e[1].y),o.moveTo(e[3].x,e[3].y)):(o.setStrokeStyle(r.color.downLine),o.setFillStyle(r.color.downFill),o.setLineWidth(1*i.pix),o.moveTo(e[3].x,e[3].y),o.lineTo(e[0].x,e[0].y),o.lineTo(e[0].x-c/4,e[0].y),o.lineTo(e[1].x-c/4,e[1].y),o.lineTo(e[1].x,e[1].y),o.lineTo(e[2].x,e[2].y),o.lineTo(e[1].x,e[1].y),o.lineTo(e[1].x+c/4,e[1].y),o.lineTo(e[0].x+c/4,e[0].y),o.lineTo(e[0].x,e[0].y),o.moveTo(e[3].x,e[3].y)),o.closePath(),o.fill(),o.stroke()}})),o.restore(),{xAxisPoints:l,calPoints:h,eachSpacing:c}}(n,l,t,i,a,e),s=o.xAxisPoints,c=o.calPoints,h=o.eachSpacing;t.chartData.xAxisPoints=s,t.chartData.calPoints=c,t.chartData.eachSpacing=h,yn(0,t,i,a),!1!==t.enableMarkLine&&1===e&&hn(t,0,a),vn(l?0:t.series,t,i,a,t.chartData),fn(t,i,a,e),Ln(0,a)},onAnimationFinish:function(){o.uevent.trigger("renderComplete")}})}}function On(){this.events={}}Mn.prototype.stop=function(){this.isStop=!0},On.prototype.addEventListener=function(e,t){this.events[e]=this.events[e]||[],this.events[e].push(t)},On.prototype.delEventListener=function(e){this.events[e]=[]},On.prototype.trigger=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var a=t[0],o=t.slice(1);this.events[a]&&this.events[a].forEach((function(e){try{e.apply(null,o)}catch(Ct){}}))};var En=function(e){e.pix=e.pixelRatio?e.pixelRatio:1,e.fontSize=e.fontSize?e.fontSize:13,e.fontColor=e.fontColor?e.fontColor:Za.fontColor,""!=e.background&&"none"!=e.background||(e.background="#FFFFFF"),e.title=eo({},e.title),e.subtitle=eo({},e.subtitle),e.duration=e.duration?e.duration:1e3,e.yAxis=eo({},{data:[],showTitle:!1,disabled:!1,disableGrid:!1,gridSet:"number",splitNumber:5,gridType:"solid",dashLength:4*e.pix,gridColor:"#cccccc",padding:10,fontColor:"#666666"},e.yAxis),e.xAxis=eo({},{rotateLabel:!1,rotateAngle:45,disabled:!1,disableGrid:!1,splitNumber:5,calibration:!1,fontColor:"#666666",fontSize:13,lineHeight:20,marginTop:0,gridType:"solid",dashLength:4,scrollAlign:"left",boundaryGap:"center",axisLine:!0,axisLineColor:"#cccccc",titleFontSize:13,titleOffsetY:0,titleOffsetX:0,titleFontColor:"#666666"},e.xAxis),e.xAxis.scrollPosition=e.xAxis.scrollAlign,e.legend=eo({},{show:!0,position:"bottom",float:"center",backgroundColor:"rgba(0,0,0,0)",borderColor:"rgba(0,0,0,0)",borderWidth:0,padding:5,margin:5,itemGap:10,fontSize:e.fontSize,lineHeight:e.fontSize,fontColor:e.fontColor,formatter:{},hiddenColor:"#CECECE"},e.legend),e.extra=eo({tooltip:{legendShape:"auto"}},e.extra),e.rotate=!!e.rotate,e.animation=!!e.animation,e.rotate=!!e.rotate,e.canvas2d=!!e.canvas2d;let t=eo({},Za);if(t.color=e.color?e.color:t.color,"pie"==e.type&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.pie.labelWidth*e.pix||t.pieChartLinePadding*e.pix),"ring"==e.type&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.ring.labelWidth*e.pix||t.pieChartLinePadding*e.pix),"rose"==e.type&&(t.pieChartLinePadding=!1===e.dataLabel?0:e.extra.rose.labelWidth*e.pix||t.pieChartLinePadding*e.pix),t.pieChartTextPadding=!1===e.dataLabel?0:t.pieChartTextPadding*e.pix,t.rotate=e.rotate,e.rotate){let t=e.width,i=e.height;e.width=i,e.height=t}if(e.padding=e.padding?e.padding:t.padding,t.yAxisWidth=Za.yAxisWidth*e.pix,t.fontSize=e.fontSize*e.pix,t.titleFontSize=Za.titleFontSize*e.pix,t.subtitleFontSize=Za.subtitleFontSize*e.pix,!e.context)throw new Error("[uCharts] 未获取到context！注意：v2.0版本后，需要自行获取canvas的绘图上下文并传入opts.context！");this.context=e.context,this.context.setTextAlign||(this.context.setStrokeStyle=function(e){return this.strokeStyle=e},this.context.setLineWidth=function(e){return this.lineWidth=e},this.context.setLineCap=function(e){return this.lineCap=e},this.context.setFontSize=function(e){return this.font=e+"px sans-serif"},this.context.setFillStyle=function(e){return this.fillStyle=e},this.context.setTextAlign=function(e){return this.textAlign=e},this.context.setTextBaseline=function(e){return this.textBaseline=e},this.context.setShadow=function(e,t,i,a){this.shadowColor=a,this.shadowOffsetX=e,this.shadowOffsetY=t,this.shadowBlur=i},this.context.draw=function(){}),this.context.setLineDash||(this.context.setLineDash=function(e){}),e.chartData={},this.uevent=new On,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0},this.opts=e,this.config=t,Fn.call(this,e.type,e,t,this.context)};En.prototype.updateData=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.opts=eo({},this.opts,e),this.opts.updateData=!0;let t=e.scrollPosition||"current";switch(t){case"current":this.opts._scrollDistance_=this.scrollOption.currentOffset;break;case"left":this.opts._scrollDistance_=0,this.scrollOption={currentOffset:0,startTouchX:0,distance:0,lastMoveTime:0};break;case"right":let e=Yo(this.opts.series,this.opts,this.config,this.context).yAxisWidth;this.config.yAxisWidth=e;let t=0,i=Wo(this.opts.categories,this.opts,this.config),a=i.xAxisPoints,o=i.startX;t=i.endX-o-i.eachSpacing*(a.length-1),this.scrollOption={currentOffset:t,startTouchX:t,distance:0,lastMoveTime:0},this.opts._scrollDistance_=t}Fn.call(this,this.opts.type,this.opts,this.config,this.context)},En.prototype.zoom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.opts.xAxis.itemCount;if(!0!==this.opts.enableScroll)return void console.log("[uCharts] 请启用滚动条后使用");let t=Math.round(Math.abs(this.scrollOption.currentOffset)/this.opts.chartData.eachSpacing)+Math.round(this.opts.xAxis.itemCount/2);this.opts.animation=!1,this.opts.xAxis.itemCount=e.itemCount;let i=Yo(this.opts.series,this.opts,this.config,this.context),a=i.yAxisWidth;this.config.yAxisWidth=a;let o=0,n=Wo(this.opts.categories,this.opts,this.config),r=n.xAxisPoints,s=n.startX,l=n.endX,c=n.eachSpacing,h=c*t,d=l-s,p=d-c*(r.length-1);o=d/2-h,o>0&&(o=0),o<p&&(o=p),this.scrollOption={currentOffset:o,startTouchX:0,distance:0,lastMoveTime:0},oo(this,o,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=o,Fn.call(this,this.opts.type,this.opts,this.config,this.context)},En.prototype.dobuleZoom=function(e){if(!0!==this.opts.enableScroll)return void console.log("[uCharts] 请启用滚动条后使用");const t=e.changedTouches;if(t.length<2)return;for(var i=0;i<t.length;i++)t[i].x=t[i].x?t[i].x:t[i].clientX,t[i].y=t[i].y?t[i].y:t[i].clientY;const a=[fo(t[0],this.opts,e),fo(t[1],this.opts,e)],o=Math.abs(a[0].x-a[1].x);if(!this.scrollOption.moveCount){let e={changedTouches:[{x:t[0].x,y:this.opts.area[0]/this.opts.pix+2}]},i={changedTouches:[{x:t[1].x,y:this.opts.area[0]/this.opts.pix+2}]};this.opts.rotate&&(e={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:t[0].y}]},i={changedTouches:[{x:this.opts.height/this.opts.pix-this.opts.area[0]/this.opts.pix-2,y:t[1].y}]});const a=this.getCurrentDataIndex(e).index,o=this.getCurrentDataIndex(i).index,n=Math.abs(a-o);return this.scrollOption.moveCount=n,this.scrollOption.moveCurrent1=Math.min(a,o),void(this.scrollOption.moveCurrent2=Math.max(a,o))}let n=o/this.scrollOption.moveCount,r=(this.opts.width-this.opts.area[1]-this.opts.area[3])/n;r=r<=2?2:r,r=r>=this.opts.categories.length?this.opts.categories.length:r,this.opts.animation=!1,this.opts.xAxis.itemCount=r;let s=0,l=Wo(this.opts.categories,this.opts,this.config),c=l.xAxisPoints,h=l.startX,d=l.endX,p=l.eachSpacing,u=p*this.scrollOption.moveCurrent1,g=d-h-p*(c.length-1);s=-u+Math.min(a[0].x,a[1].x)-this.opts.area[3]-p,s>0&&(s=0),s<g&&(s=g),this.scrollOption.currentOffset=s,this.scrollOption.startTouchX=0,this.scrollOption.distance=0,oo(this,s,this.opts.chartData,this.config,this.opts),this.opts._scrollDistance_=s,Fn.call(this,this.opts.type,this.opts,this.config,this.context)},En.prototype.stopAnimation=function(){this.animationInstance&&this.animationInstance.stop()},En.prototype.addEventListener=function(e,t){this.uevent.addEventListener(e,t)},En.prototype.delEventListener=function(e){this.uevent.delEventListener(e)},En.prototype.getCurrentDataIndex=function(e){var t=null;if(t=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0]){let i=fo(t,this.opts,e);return"pie"===this.opts.type||"ring"===this.opts.type?function(e,t,i){var a=-1,o=Do(t.series);if(t&&t.center&&_o(e,t.center,t.radius)){var n=Math.atan2(t.center.y-e.y,e.x-t.center.x);n=-n,i.extra.pie&&i.extra.pie.offsetAngle&&(n-=i.extra.pie.offsetAngle*Math.PI/180),i.extra.ring&&i.extra.ring.offsetAngle&&(n-=i.extra.ring.offsetAngle*Math.PI/180);for(var r=0,s=o.length;r<s;r++)if(no(n,o[r]._start_,o[r]._start_+2*o[r]._proportion_*Math.PI)){a=r;break}}return a}({x:i.x,y:i.y},this.opts.chartData.pieData,this.opts):"rose"===this.opts.type?function(e,t,i){var a=-1,o=Io(i._series_,i.extra.rose.type,t.radius,t.radius);if(t&&t.center&&_o(e,t.center,t.radius)){var n=Math.atan2(t.center.y-e.y,e.x-t.center.x);n=-n,i.extra.rose&&i.extra.rose.offsetAngle&&(n-=i.extra.rose.offsetAngle*Math.PI/180);for(var r=0,s=o.length;r<s;r++)if(no(n,o[r]._start_,o[r]._start_+2*o[r]._rose_proportion_*Math.PI)){a=r;break}}return a}({x:i.x,y:i.y},this.opts.chartData.pieData,this.opts):"radar"===this.opts.type?function(e,t,i){var a=2*Math.PI/i,o=-1;if(_o(e,t.center,t.radius)){var n=function(e){return e<0&&(e+=2*Math.PI),e>2*Math.PI&&(e-=2*Math.PI),e},r=Math.atan2(t.center.y-e.y,e.x-t.center.x);(r*=-1)<0&&(r+=2*Math.PI),t.angleList.map((function(e){return n(-1*e)})).forEach((function(e,t){var i=n(e-a/2),s=n(e+a/2);s<i&&(s+=2*Math.PI),(r>=i&&r<=s||r+2*Math.PI>=i&&r+2*Math.PI<=s)&&(o=t)}))}return o}({x:i.x,y:i.y},this.opts.chartData.radarData,this.opts.categories.length):"funnel"===this.opts.type?function(e,t){for(var i=-1,a=0,o=t.series.length;a<o;a++){var n=t.series[a];if(e.x>n.funnelArea[0]&&e.x<n.funnelArea[2]&&e.y>n.funnelArea[1]&&e.y<n.funnelArea[3]){i=a;break}}return i}({x:i.x,y:i.y},this.opts.chartData.funnelData):"map"===this.opts.type?function(e,t){for(var i,a,o,n,r,s,l=-1,c=t.chartData.mapData,h=t.series,d=(i=e.y,a=e.x,o=c.bounds,n=c.scale,r=c.xoffset,s=c.yoffset,{x:(a-r)/n+o.xMin,y:o.yMax-(i-s)/n}),p=[d.x,d.y],u=0,g=h.length;u<g;u++)if(_n(p,h[u].geometry.coordinates,t.chartData.mapData.mercator)){l=u;break}return l}({x:i.x,y:i.y},this.opts):"word"===this.opts.type?function(e,t){for(var i=-1,a=0,o=t.length;a<o;a++){var n=t[a];if(e.x>n.area[0]&&e.x<n.area[2]&&e.y>n.area[1]&&e.y<n.area[3]){i=a;break}}return i}({x:i.x,y:i.y},this.opts.chartData.wordCloudData):"bar"===this.opts.type?function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n={index:-1,group:[]},r=i.chartData.eachSpacing/2;let s=i.chartData.yAxisPoints;return t&&t.length>0&&To(e,i)&&s.forEach((function(t,i){e.y+o+r>t&&(n.index=i)})),n}({x:i.x,y:i.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset)):function(e,t,i,a){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n={index:-1,group:[]},r=i.chartData.eachSpacing/2;let s=[];if(t&&t.length>0){if(i.categories){for(let e=1;e<i.chartData.xAxisPoints.length;e++)s.push(i.chartData.xAxisPoints[e]-r);"line"!=i.type&&"area"!=i.type||"justify"!=i.xAxis.boundaryGap||(s=i.chartData.xAxisPoints)}else r=0;if(To(e,i))if(i.categories)s.forEach((function(t,i){e.x+o+r>t&&(n.index=i)}));else{let i=Array(t.length);for(let n=0;n<t.length;n++){i[n]=Array(t[n].length);for(let a=0;a<t[n].length;a++)i[n][a]=Math.abs(t[n][a].x-e.x)}let a=Array(i.length),o=Array(i.length);for(let e=0;e<i.length;e++)a[e]=Math.min.apply(null,i[e]),o[e]=i[e].indexOf(a[e]);let r=Math.min.apply(null,a);n.index=[];for(let e=0;e<a.length;e++)a[e]==r&&(n.group.push(e),n.index.push(o[e]))}}return n}({x:i.x,y:i.y},this.opts.chartData.calPoints,this.opts,this.config,Math.abs(this.scrollOption.currentOffset))}return-1},En.prototype.getLegendDataIndex=function(e){var t=null;if(t=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0]){let i=fo(t,this.opts,e);return function(e,t,i){let a=-1;if(function(e,t){return e.x>t.start.x&&e.x<t.end.x&&e.y>t.start.y&&e.y<t.end.y}(e,t.area)){let i=t.points,o=-1;for(let t=0,n=i.length;t<n;t++){let n=i[t];for(let t=0;t<n.length;t++){o+=1;let i=n[t].area;if(i&&e.x>i[0]-0&&e.x<i[2]+0&&e.y>i[1]-0&&e.y<i[3]+0){a=o;break}}}return a}return a}({x:i.x,y:i.y},this.opts.chartData.legendData)}return-1},En.prototype.touchLegend=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;if(i=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0]){fo(i,this.opts,e);var a=this.getLegendDataIndex(e);a>=0&&("candle"==this.opts.type?this.opts.seriesMA[a].show=!this.opts.seriesMA[a].show:this.opts.series[a].show=!this.opts.series[a].show,this.opts.animation=!!t.animation,this.opts._scrollDistance_=this.scrollOption.currentOffset,Fn.call(this,this.opts.type,this.opts,this.config,this.context))}},En.prototype.showToolTip=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=null;(i=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0])||console.log("[uCharts] 未获取到event坐标信息");var a=fo(i,this.opts,e),o=this.scrollOption.currentOffset,n=eo({},this.opts,{_scrollDistance_:o,animation:!1});if("line"===this.opts.type||"area"===this.opts.type||"column"===this.opts.type||"scatter"===this.opts.type||"bubble"===this.opts.type){var r=this.getCurrentDataIndex(e);if((p=null==t.index?r.index:t.index)>-1||p.length>0)if(0!==(l=xo(this.opts.series,p,r.group)).length){var s=(d=vo(l,this.opts,p,r.group,this.opts.categories,t)).textList;(c=d.offset).y=a.y,n.tooltip={textList:void 0!==t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p,group:r.group}}Fn.call(this,n.type,n,this.config,this.context)}if("mount"===this.opts.type){if((p=null==t.index?this.getCurrentDataIndex(e).index:t.index)>-1){n=eo({},this.opts,{animation:!1});var l=eo({},n._series_[p]),c=(s=[{text:t.formatter?t.formatter(l,void 0,p,n):l.name+": "+l.data,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],{x:n.chartData.calPoints[p].x,y:a.y});n.tooltip={textList:t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}Fn.call(this,n.type,n,this.config,this.context)}if("bar"===this.opts.type){r=this.getCurrentDataIndex(e);if((p=null==t.index?r.index:t.index)>-1||p.length>0)if(0!==(l=xo(this.opts.series,p,r.group)).length){s=(d=vo(l,this.opts,p,r.group,this.opts.categories,t)).textList;(c=d.offset).x=a.x,n.tooltip={textList:void 0!==t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}Fn.call(this,n.type,n,this.config,this.context)}if("mix"===this.opts.type){r=this.getCurrentDataIndex(e);if((p=null==t.index?r.index:t.index)>-1){o=this.scrollOption.currentOffset,n=eo({},this.opts,{_scrollDistance_:o,animation:!1});if(0!==(l=xo(this.opts.series,p)).length){var h=bo(l,this.opts,p,this.opts.categories,t);s=h.textList;(c=h.offset).y=a.y,n.tooltip={textList:t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}}Fn.call(this,n.type,n,this.config,this.context)}if("candle"===this.opts.type){r=this.getCurrentDataIndex(e);if((p=null==t.index?r.index:t.index)>-1){o=this.scrollOption.currentOffset,n=eo({},this.opts,{_scrollDistance_:o,animation:!1});if(0!==(l=xo(this.opts.series,p)).length){var d;s=(d=So(this.opts.series[0].data,l,this.opts,p,this.opts.categories,this.opts.extra.candle)).textList;(c=d.offset).y=a.y,n.tooltip={textList:t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}}Fn.call(this,n.type,n,this.config,this.context)}if("pie"===this.opts.type||"ring"===this.opts.type||"rose"===this.opts.type||"funnel"===this.opts.type){if((p=null==t.index?this.getCurrentDataIndex(e):t.index)>-1){n=eo({},this.opts,{animation:!1}),l=eo({},n._series_[p]),s=[{text:t.formatter?t.formatter(l,void 0,p,n):l.name+": "+l.data,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],c={x:a.x,y:a.y};n.tooltip={textList:t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}Fn.call(this,n.type,n,this.config,this.context)}if("map"===this.opts.type){if((p=null==t.index?this.getCurrentDataIndex(e):t.index)>-1){n=eo({},this.opts,{animation:!1});(l=eo({},this.opts.series[p])).name=l.properties.name;s=[{text:t.formatter?t.formatter(l,void 0,p,this.opts):l.name,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],c={x:a.x,y:a.y};n.tooltip={textList:t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}n.updateData=!1,Fn.call(this,n.type,n,this.config,this.context)}if("word"===this.opts.type){if((p=null==t.index?this.getCurrentDataIndex(e):t.index)>-1){n=eo({},this.opts,{animation:!1}),l=eo({},this.opts.series[p]),s=[{text:t.formatter?t.formatter(l,void 0,p,this.opts):l.name,color:l.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?l.legendShape:this.opts.extra.tooltip.legendShape}],c={x:a.x,y:a.y};n.tooltip={textList:t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}n.updateData=!1,Fn.call(this,n.type,n,this.config,this.context)}if("radar"===this.opts.type){var p;if((p=null==t.index?this.getCurrentDataIndex(e):t.index)>-1){n=eo({},this.opts,{animation:!1});if(0!==(l=xo(this.opts.series,p)).length){s=l.map((e=>({text:t.formatter?t.formatter(e,this.opts.categories[p],p,this.opts):e.name+": "+e.data,color:e.color,legendShape:"auto"==this.opts.extra.tooltip.legendShape?e.legendShape:this.opts.extra.tooltip.legendShape}))),c={x:a.x,y:a.y};n.tooltip={textList:t.textList?t.textList:s,offset:void 0!==t.offset?t.offset:c,option:t,index:p}}}Fn.call(this,n.type,n,this.config,this.context)}},En.prototype.translate=function(e){this.scrollOption={currentOffset:e,startTouchX:e,distance:0,lastMoveTime:0};let t=eo({},this.opts,{_scrollDistance_:e,animation:!1});Fn.call(this,this.opts.type,t,this.config,this.context)},En.prototype.scrollStart=function(e){var t=null,i=fo(t=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0],this.opts,e);t&&!0===this.opts.enableScroll&&(this.scrollOption.startTouchX=i.x)},En.prototype.scroll=function(e){0===this.scrollOption.lastMoveTime&&(this.scrollOption.lastMoveTime=Date.now());let t=this.opts.touchMoveLimit||60,i=Date.now();if(!(i-this.scrollOption.lastMoveTime<Math.floor(1e3/t))&&0!=this.scrollOption.startTouchX){this.scrollOption.lastMoveTime=i;var a=null;if((a=e.changedTouches?e.changedTouches[0]:e.mp.changedTouches[0])&&!0===this.opts.enableScroll){var o;o=fo(a,this.opts,e).x-this.scrollOption.startTouchX;var n=this.scrollOption.currentOffset,r=oo(this,n+o,this.opts.chartData,this.config,this.opts);this.scrollOption.distance=o=r-n;var s=eo({},this.opts,{_scrollDistance_:n+o,animation:!1});return this.opts=s,Fn.call(this,s.type,s,this.config,this.context),n+o}}},En.prototype.scrollEnd=function(e){if(!0===this.opts.enableScroll){var t=this.scrollOption,i=t.currentOffset,a=t.distance;this.scrollOption.currentOffset=i+a,this.scrollOption.distance=0,this.scrollOption.moveCount=0}};const Rn=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],zn={type:["pie","ring","rose","word","funnel","map","arcbar","line","column","mount","bar","area","radar","gauge","candle","mix","tline","tarea","scatter","bubble","demotype"],range:["饼状图","圆环图","玫瑰图","词云图","漏斗图","地图","圆弧进度条","折线图","柱状图","山峰图","条状图","区域图","雷达图","仪表盘","K线图","混合图","时间轴折线","时间轴区域","散点图","气泡图","自定义类型"],categories:["line","column","mount","bar","area","radar","gauge","candle","mix","demotype"],instance:{},option:{},formatter:{yAxisDemo1:function(e,t,i){return e+"元"},yAxisDemo2:function(e,t,i){return e.toFixed(2)},xAxisDemo1:function(e,t,i){return e+"年"},xAxisDemo2:function(e,t,i){return((e,t)=>{var i=new Date;i.setTime(1e3*e);var a=i.getFullYear(),o=i.getMonth()+1;o=o<10?"0"+o:o;var n=i.getDate();n=n<10?"0"+n:n;var r=i.getHours();r=r<10?"0"+r:r;var s=i.getMinutes(),l=i.getSeconds();return s=s<10?"0"+s:s,l=l<10?"0"+l:l,"full"==t?a+"-"+o+"-"+n+" "+r+":"+s+":"+l:"y-m-d"==t?a+"-"+o+"-"+n:"h:m"==t?r+":"+s:"h:m:s"==t?r+":"+s+":"+l:[a,o,n,r,s,l]})(e,"h:m")},seriesDemo1:function(e,t,i,a){return e+"元"},tooltipDemo1:function(e,t,i,a){return 0==i?"随便用"+e.data+"年":"其他我没改"+e.data+"天"},pieDemo:function(e,t,i,a){if(void 0!==t)return i[t].name+"："+i[t].data+"元"}},demotype:{type:"line",color:Rn,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"curve",width:2}}},pie:{type:"pie",color:Rn,padding:[5,5,5,5],extra:{pie:{activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},ring:{type:"ring",color:Rn,padding:[5,5,5,5],rotate:!1,dataLabel:!0,legend:{show:!0,position:"right",lineHeight:25},title:{name:"收益率",fontSize:15,color:"#666666"},subtitle:{name:"70%",fontSize:25,color:"#7cb5ec"},extra:{ring:{ringWidth:30,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!0,borderWidth:3,borderColor:"#FFFFFF"}}},rose:{type:"rose",color:Rn,padding:[5,5,5,5],legend:{show:!0,position:"left",lineHeight:25},extra:{rose:{type:"area",minRadius:50,activeOpacity:.5,activeRadius:10,offsetAngle:0,labelWidth:15,border:!1,borderWidth:2,borderColor:"#FFFFFF"}}},word:{type:"word",color:Rn,extra:{word:{type:"normal",autoColors:!1}}},funnel:{type:"funnel",color:Rn,padding:[15,15,0,15],extra:{funnel:{activeOpacity:.3,activeWidth:10,border:!0,borderWidth:2,borderColor:"#FFFFFF",fillOpacity:1,labelAlign:"right"}}},map:{type:"map",color:Rn,padding:[0,0,0,0],dataLabel:!0,extra:{map:{border:!0,borderWidth:1,borderColor:"#666666",fillOpacity:.6,activeBorderColor:"#F04864",activeFillColor:"#FACC14",activeFillOpacity:1}}},arcbar:{type:"arcbar",color:Rn,title:{name:"百分比",fontSize:25,color:"#00FF00"},subtitle:{name:"默认标题",fontSize:15,color:"#666666"},extra:{arcbar:{type:"default",width:12,backgroundColor:"#E9E9E9",startAngle:.75,endAngle:.25,gap:2}}},line:{type:"line",color:Rn,padding:[15,10,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{line:{type:"straight",width:2,activeType:"hollow"}}},tline:{type:"line",color:Rn,padding:[15,10,0,15],xAxis:{disableGrid:!1,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{line:{type:"curve",width:2,activeType:"hollow"}}},tarea:{type:"area",color:Rn,padding:[15,10,0,15],xAxis:{disableGrid:!0,boundaryGap:"justify"},yAxis:{gridType:"dash",dashLength:2,data:[{min:0,max:80}]},legend:{},extra:{area:{type:"curve",opacity:.2,addLine:!0,width:2,gradient:!0,activeType:"hollow"}}},column:{type:"column",color:Rn,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}},mount:{type:"mount",color:Rn,padding:[15,15,0,5],xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},legend:{},extra:{mount:{type:"mount",widthRatio:1.5}}},bar:{type:"bar",color:Rn,padding:[15,30,0,5],xAxis:{boundaryGap:"justify",disableGrid:!1,min:0,axisLine:!1},yAxis:{},legend:{},extra:{bar:{type:"group",width:30,meterBorde:1,meterFillColor:"#FFFFFF",activeBgColor:"#000000",activeBgOpacity:.08}}},area:{type:"area",color:Rn,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{gridType:"dash",dashLength:2},legend:{},extra:{area:{type:"straight",opacity:.2,addLine:!0,width:2,gradient:!1,activeType:"hollow"}}},radar:{type:"radar",color:Rn,padding:[5,5,5,5],dataLabel:!1,legend:{show:!0,position:"right",lineHeight:25},extra:{radar:{gridType:"radar",gridColor:"#CCCCCC",gridCount:3,opacity:.2,max:200,labelShow:!0}}},gauge:{type:"gauge",color:Rn,title:{name:"66Km/H",fontSize:25,color:"#2fc25b",offsetY:50},subtitle:{name:"实时速度",fontSize:15,color:"#1890ff",offsetY:-50},extra:{gauge:{type:"default",width:30,labelColor:"#666666",startAngle:.75,endAngle:.25,startNumber:0,endNumber:100,labelFormat:"",splitLine:{fixRadius:0,splitNumber:10,width:30,color:"#FFFFFF",childNumber:5,childWidth:12},pointer:{width:24,color:"auto"}}}},candle:{type:"candle",color:Rn,padding:[15,15,0,15],enableScroll:!0,enableMarkLine:!0,dataLabel:!1,xAxis:{labelCount:4,itemCount:40,disableGrid:!0,gridColor:"#CCCCCC",gridType:"solid",dashLength:4,scrollShow:!0,scrollAlign:"left",scrollColor:"#A6A6A6",scrollBackgroundColor:"#EFEBEF"},yAxis:{},legend:{},extra:{candle:{color:{upLine:"#f04864",upFill:"#f04864",downLine:"#2fc25b",downFill:"#2fc25b"},average:{show:!0,name:["MA5","MA10","MA30"],day:[5,10,20],color:["#1890ff","#2fc25b","#facc14"]}},markLine:{type:"dash",dashLength:5,data:[{value:2150,lineColor:"#f04864",showLabel:!0},{value:2350,lineColor:"#f04864",showLabel:!0}]}}},mix:{type:"mix",color:Rn,padding:[15,15,0,15],xAxis:{disableGrid:!0},yAxis:{disabled:!1,disableGrid:!1,splitNumber:5,gridType:"dash",dashLength:4,gridColor:"#CCCCCC",padding:10,showTitle:!0,data:[]},legend:{},extra:{mix:{column:{width:20}}}},scatter:{type:"scatter",color:Rn,padding:[15,15,0,15],dataLabel:!1,xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0},yAxis:{disableGrid:!1,gridType:"dash"},legend:{},extra:{scatter:{}}},bubble:{type:"bubble",color:Rn,padding:[15,15,0,15],xAxis:{disableGrid:!1,gridType:"dash",splitNumber:5,boundaryGap:"justify",min:0,max:250},yAxis:{disableGrid:!1,gridType:"dash",data:[{min:0,max:150}]},legend:{},extra:{bubble:{border:2,opacity:.5}}}},Bn=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],Nn={type:["pie","ring","rose","funnel","line","column","area","radar","gauge","candle","demotype"],categories:["line","column","area","radar","gauge","candle","demotype"],instance:{},option:{},formatter:{tooltipDemo1:function(e){let t="";for(let i in e){0==i&&(t+=e[i].axisValueLabel+"年销售额");let a="--";null!==e[i].data&&(a=e[i].data),t+="\n"+e[i].seriesName+"："+a+" 万元"}return t},legendFormat:function(e){return"自定义图例+"+e},yAxisFormatDemo:function(e,t){return e+"元"},seriesFormatDemo:function(e){return e.name+"年"+e.value+"元"}},demotype:{color:Bn},column:{color:Bn,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"bar",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},line:{color:Bn,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],barwidth:20,label:{show:!0,color:"#666666",position:"top"}}},area:{color:Bn,title:{text:""},tooltip:{trigger:"axis"},grid:{top:30,bottom:50,right:15,left:40},legend:{bottom:"left"},toolbox:{show:!1},xAxis:{type:"category",axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}},boundaryGap:!0,data:[]},yAxis:{type:"value",axisTick:{show:!1},axisLabel:{color:"#666666"},axisLine:{lineStyle:{color:"#CCCCCC"}}},seriesTemplate:{name:"",type:"line",data:[],areaStyle:{},label:{show:!0,color:"#666666",position:"top"}}},pie:{color:Bn,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:"50%",label:{show:!0,color:"#666666",position:"top"}}},ring:{color:Bn,title:{text:""},tooltip:{trigger:"item"},grid:{top:40,bottom:30,right:15,left:15},legend:{bottom:"left"},seriesTemplate:{name:"",type:"pie",data:[],radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!0,color:"#666666",position:"top"},labelLine:{show:!0}}},rose:{color:Bn,title:{text:""},tooltip:{trigger:"item"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"pie",data:[],radius:"55%",center:["50%","50%"],roseType:"area"}},funnel:{color:Bn,title:{text:""},tooltip:{trigger:"item",formatter:"{b} : {c}%"},legend:{top:"bottom"},seriesTemplate:{name:"",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{bordercolor:"#fff",borderwidth:1},emphasis:{label:{fontSize:20}},data:[]}},gauge:{color:Bn,tooltip:{formatter:"{a} <br/>{b} : {c}%"},seriesTemplate:{name:"业务指标",type:"gauge",detail:{formatter:"{value}%"},data:[{value:50,name:"完成率"}]}},candle:{xAxis:{data:[]},yAxis:{},color:Bn,title:{text:""},dataZoom:[{type:"inside",xAxisIndex:[0,1],start:10,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:10,start:10,end:100}],seriesTemplate:{name:"",type:"k",data:[]}}};var Un={},Wn=null;function jn(e={},...t){for(let i in t)for(let a in t[i])t[i].hasOwnProperty(a)&&(e[a]=t[i][a]&&"object"==typeof t[i][a]?jn(Array.isArray(t[i][a])?[]:{},e[a],t[i][a]):t[i][a]);return e}function qn(e,t){for(let i in e)e.hasOwnProperty(i)&&null!==e[i]&&"object"==typeof e[i]?qn(e[i],t):"format"===i&&"string"==typeof e[i]&&(e.formatter=t[e[i]]?t[e[i]]:void 0);return e}const $n={data:()=>({rid:null}),mounted(){Wn={top:0,left:0};let e=document.querySelectorAll("uni-main")[0];void 0===e&&(e=document.querySelectorAll("uni-page-wrapper")[0]),Wn={top:e.offsetTop,left:e.offsetLeft},setTimeout((()=>{null===this.rid&&this.$ownerInstance&&this.$ownerInstance.callMethod("getRenderType")}),200)},destroyed(){delete zn.option[this.rid],delete zn.instance[this.rid],delete Nn.option[this.rid],delete Nn.instance[this.rid]},methods:{ecinit(e,t,i,a){let o=JSON.stringify(e.id);this.rid=o,Un[o]=this.$ownerInstance||a;let n=JSON.parse(JSON.stringify(e)),r=n.type;r&&Nn.type.includes(r)?Nn.option[o]=jn({},Nn[r],n):Nn.option[o]=jn({},n);let s=n.chartData;if(s){Nn.option[o].xAxis&&Nn.option[o].xAxis.type&&"category"===Nn.option[o].xAxis.type&&(Nn.option[o].xAxis.data=s.categories),Nn.option[o].yAxis&&Nn.option[o].yAxis.type&&"category"===Nn.option[o].yAxis.type&&(Nn.option[o].yAxis.data=s.categories),Nn.option[o].series=[];for(var l=0;l<s.series.length;l++){Nn.option[o].seriesTemplate=Nn.option[o].seriesTemplate?Nn.option[o].seriesTemplate:{};let e=jn({},Nn.option[o].seriesTemplate,s.series[l]);Nn.option[o].series.push(e)}}if("object"==typeof window.echarts)this.newEChart();else{const e=document.createElement("script"),t=window.location.origin,i=a.getDataset().directory;e.src=t+i+"uni_modules/qiun-data-charts/static/h5/echarts.min.js",e.onload=this.newEChart,document.head.appendChild(e)}},ecresize(e,t,i,a){Nn.instance[this.rid]&&Nn.instance[this.rid].resize()},newEChart(){let e=this.rid;void 0===Nn.instance[e]?(Nn.instance[e]=echarts.init(Un[e].$el.children[0]),!0===Nn.option[e].ontap&&(Nn.instance[e].on("click",(t=>{let i=JSON.parse(JSON.stringify({x:t.event.offsetX,y:t.event.offsetY}));Un[e].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:i,currentIndex:t.dataIndex,value:t.data,seriesName:t.seriesName,id:e}})})),Nn.instance[e].on("highlight",(t=>{Un[e].callMethod("emitMsg",{name:"getHighlight",params:{type:"highlight",res:t,id:e}})}))),this.updataEChart(e,Nn.option[e])):this.updataEChart(e,Nn.option[e])},updataEChart(e,t){if((t=qn(t,Nn.formatter)).tooltip&&(t.tooltip.show=!!t.tooltipShow,t.tooltip.position=this.tooltipPosition(),"string"==typeof t.tooltipFormat&&Nn.formatter[t.tooltipFormat]&&(t.tooltip.formatter=t.tooltip.formatter?t.tooltip.formatter:Nn.formatter[t.tooltipFormat])),t.series)for(let i in t.series){let e=t.series[i].linearGradient;e&&(t.series[i].color=new echarts.graphic.LinearGradient(e[0],e[1],e[2],e[3],e[4]))}Nn.instance[e].setOption(t,t.notMerge),Nn.instance[e].on("finished",(function(){Un[e].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:e}}),Nn.instance[e]&&Nn.instance[e].off("finished")})),void 0!==Un[e].$el.children[0].clientWidth&&(Math.abs(Un[e].$el.children[0].clientWidth-Nn.instance[e].getWidth())>3||Math.abs(Un[e].$el.children[0].clientHeight-Nn.instance[e].getHeight())>3)&&this.ecresize()},tooltipPosition:()=>(e,t,i,a,o)=>{let n=e[0],r=e[1],s=o.viewSize[0],l=o.viewSize[1],c=o.contentSize[0],h=o.contentSize[1],d=n+30,p=r+30;return d+c>s&&(d=n-c-30),p+h>l&&(p=r-h-30),[d,p]},ucinit(e,t,i,a){if(JSON.stringify(e)==JSON.stringify(t))return;if(!e.canvasId)return;let o=JSON.parse(JSON.stringify(e.canvasId));this.rid=o,Un[o]=this.$ownerInstance||a,zn.option[o]=JSON.parse(JSON.stringify(e)),zn.option[o]=qn(zn.option[o],zn.formatter);let n=document.getElementById(o);n&&n.children[0]&&(zn.option[o].context=n.children[0].getContext("2d"),zn.instance[o]&&zn.option[o]&&!0===zn.option[o].update?this.updataUChart():setTimeout((()=>{zn.option[o].context.restore(),zn.option[o].context.save(),this.newUChart()}),100))},newUChart(){let e=this.rid;zn.instance[e]=new En(zn.option[e]),zn.instance[e].addEventListener("renderComplete",(()=>{Un[e].callMethod("emitMsg",{name:"complete",params:{type:"complete",complete:!0,id:e,opts:zn.instance[e].opts}}),zn.instance[e].delEventListener("renderComplete")})),zn.instance[e].addEventListener("scrollLeft",(()=>{Un[e].callMethod("emitMsg",{name:"scrollLeft",params:{type:"scrollLeft",scrollLeft:!0,id:e,opts:zn.instance[e].opts}})})),zn.instance[e].addEventListener("scrollRight",(()=>{Un[e].callMethod("emitMsg",{name:"scrollRight",params:{type:"scrollRight",scrollRight:!0,id:e,opts:zn.instance[e].opts}})}))},updataUChart(){let e=this.rid;zn.instance[e].updateData(zn.option[e])},tooltipDefault(e,t,i,a){if(t){let i=e.data;return"object"==typeof e.data&&(i=e.data.value),t+" "+e.name+":"+i}return e.properties&&e.properties.name?e.properties.name:e.name+":"+e.data},showTooltip(e,t){let i=zn.option[t].tooltipCustom;if(i&&null!=i){let a;i.x>=0&&i.y>=0&&(a={x:i.x,y:i.y+10}),zn.instance[t].showToolTip(e,{index:i.index,offset:a,textList:i.textList,formatter:(e,i,a,o)=>"string"==typeof zn.option[t].tooltipFormat&&zn.formatter[zn.option[t].tooltipFormat]?zn.formatter[zn.option[t].tooltipFormat](e,i,a,o):this.tooltipDefault(e,i,a,o)})}else zn.instance[t].showToolTip(e,{formatter:(e,i,a,o)=>"string"==typeof zn.option[t].tooltipFormat&&zn.formatter[zn.option[t].tooltipFormat]?zn.formatter[zn.option[t].tooltipFormat](e,i,a,o):this.tooltipDefault(e,i,a,o)})},tap(e){let t=this.rid,i=zn.option[t].ontap,a=zn.option[t].tooltipShow,o=zn.option[t].tapLegend;if(0==i)return;let n=null,r=null,s=document.getElementById("UC"+t).getBoundingClientRect(),l={};l=e.detail.x?{x:e.detail.x-s.left,y:e.detail.y-s.top+Wn.top}:{x:e.clientX-s.left,y:e.clientY-s.top+Wn.top},e.changedTouches=[],e.changedTouches.unshift(l),n=zn.instance[t].getCurrentDataIndex(e),r=zn.instance[t].getLegendDataIndex(e),!0===o&&zn.instance[t].touchLegend(e),1==a&&this.showTooltip(e,t),Un[t].callMethod("emitMsg",{name:"getIndex",params:{type:"getIndex",event:l,currentIndex:n,legendIndex:r,id:t,opts:zn.instance[t].opts}})},touchStart(e){let t=this.rid;0!=zn.option[t].ontouch&&(!0===zn.option[t].enableScroll&&1==e.touches.length&&zn.instance[t].scrollStart(e),Un[t].callMethod("emitMsg",{name:"getTouchStart",params:{type:"touchStart",event:e.changedTouches[0],id:t,opts:zn.instance[t].opts}}))},touchMove(e){let t=this.rid,i=zn.option[t].ontouch;if(0!=i){if(!0===zn.option[t].enableScroll&&1==e.changedTouches.length&&zn.instance[t].scroll(e),!0===zn.option[t].ontap&&!1===zn.option[t].enableScroll&&!0===zn.option[t].onmovetip){let i=document.getElementById("UC"+t).getBoundingClientRect(),a={x:e.changedTouches[0].clientX-i.left,y:e.changedTouches[0].clientY-i.top+Wn.top};e.changedTouches.unshift(a),!0===zn.option[t].tooltipShow&&this.showTooltip(e,t)}!0===i&&!0===zn.option[t].enableScroll&&!0===zn.option[t].onzoom&&2==e.changedTouches.length&&zn.instance[t].dobuleZoom(e),Un[t].callMethod("emitMsg",{name:"getTouchMove",params:{type:"touchMove",event:e.changedTouches[0],id:t,opts:zn.instance[t].opts}})}},touchEnd(e){let t=this.rid;0!=zn.option[t].ontouch&&(!0===zn.option[t].enableScroll&&0==e.touches.length&&zn.instance[t].scrollEnd(e),Un[t].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"touchEnd",event:e.changedTouches[0],id:t,opts:zn.instance[t].opts}}))},mouseDown(e){let t=this.rid;if(0==zn.option[t].onmouse)return;let i=document.getElementById("UC"+t).getBoundingClientRect(),a={};a={x:e.clientX-i.left,y:e.clientY-i.top+Wn.top},e.changedTouches=[],e.changedTouches.unshift(a),zn.instance[t].scrollStart(e),zn.option[t].mousedown=!0,Un[t].callMethod("emitMsg",{name:"getTouchStart",params:{type:"mouseDown",event:a,id:t,opts:zn.instance[t].opts}})},mouseMove(e){let t=this.rid,i=zn.option[t].onmouse,a=zn.option[t].tooltipShow;if(0==i)return;let o=document.getElementById("UC"+t).getBoundingClientRect(),n={};n={x:e.clientX-o.left,y:e.clientY-o.top+Wn.top},e.changedTouches=[],e.changedTouches.unshift(n),zn.option[t].mousedown?(zn.instance[t].scroll(e),Un[t].callMethod("emitMsg",{name:"getTouchMove",params:{type:"mouseMove",event:n,id:t,opts:zn.instance[t].opts}})):zn.instance[t]&&1==a&&this.showTooltip(e,t)},mouseUp(e){let t=this.rid;if(0==zn.option[t].onmouse)return;let i=document.getElementById("UC"+t).getBoundingClientRect(),a={};a={x:e.clientX-i.left,y:e.clientY-i.top+Wn.top},e.changedTouches=[],e.changedTouches.unshift(a),zn.instance[t].scrollEnd(e),zn.option[t].mousedown=!1,Un[t].callMethod("emitMsg",{name:"getTouchEnd",params:{type:"mouseUp",event:a,id:t,opts:zn.instance[t].opts}})}}},Hn=e=>{e.$renderjs||(e.$renderjs=[]),e.$renderjs.push("rdcharts"),e.mixins||(e.mixins=[]),e.mixins.push({beforeCreate(){this.rdcharts=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),e.mixins.push($n)};function Gn(e={},...t){for(let i in t)for(let a in t[i])t[i].hasOwnProperty(a)&&(e[a]=t[i][a]&&"object"==typeof t[i][a]?Gn(Array.isArray(t[i][a])?[]:{},e[a],t[i][a]):t[i][a]);return e}function Kn(e,t){for(let i in e)e.hasOwnProperty(i)&&null!==e[i]&&"object"==typeof e[i]?Kn(e[i],t):"format"===i&&"string"==typeof e[i]&&(e.formatter=t[e[i]]?t[e[i]]:void 0);return e}const Jn={name:"qiun-data-charts",mixins:[Va.mixinDatacom],props:{type:{type:String,default:null},canvasId:{type:String,default:"uchartsid"},canvas2d:{type:Boolean,default:!1},background:{type:String,default:"rgba(0,0,0,0)"},animation:{type:Boolean,default:!0},chartData:{type:Object,default:()=>({categories:[],series:[]})},opts:{type:Object,default:()=>({})},eopts:{type:Object,default:()=>({})},loadingType:{type:Number,default:2},errorShow:{type:Boolean,default:!0},errorReload:{type:Boolean,default:!0},errorMessage:{type:String,default:null},inScrollView:{type:Boolean,default:!1},reshow:{type:Boolean,default:!1},reload:{type:Boolean,default:!1},disableScroll:{type:Boolean,default:!1},optsWatch:{type:Boolean,default:!0},onzoom:{type:Boolean,default:!1},ontap:{type:Boolean,default:!0},ontouch:{type:Boolean,default:!1},onmouse:{type:Boolean,default:!0},onmovetip:{type:Boolean,default:!1},echartsH5:{type:Boolean,default:!1},echartsApp:{type:Boolean,default:!1},tooltipShow:{type:Boolean,default:!0},tooltipFormat:{type:String,default:void 0},tooltipCustom:{type:Object,default:void 0},startDate:{type:String,default:void 0},endDate:{type:String,default:void 0},textEnum:{type:Array,default:()=>[]},groupEnum:{type:Array,default:()=>[]},pageScrollTop:{type:Number,default:0},directory:{type:String,default:"/"},tapLegend:{type:Boolean,default:!0},menus:{type:Array,default:()=>[]}},data:()=>({cid:"uchartsid",inWx:!1,inAli:!1,inTt:!1,inBd:!1,inH5:!1,inApp:!1,inWin:!1,type2d:!0,disScroll:!1,openmouse:!1,pixel:1,cWidth:375,cHeight:250,showchart:!1,echarts:!1,echartsResize:{state:!1},uchartsOpts:{},echartsOpts:{},drawData:{},lastDrawTime:null}),created(){if(this.cid=this.canvasId,"uchartsid"==this.canvasId||""==this.canvasId){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",t=e.length,i="";for(let a=0;a<32;a++)i+=e.charAt(Math.floor(Math.random()*t));this.cid=i}const e=F();"windows"!==e.platform&&"mac"!==e.platform||(this.inWin=!0),this.type2d=!1,this.disScroll=this.disableScroll},mounted(){this.inH5=!0,!0===this.inWin&&(this.openmouse=this.onmouse),!0===this.echartsH5&&(this.echarts=!0),this.$nextTick((()=>{this.beforeInit()}));const e=this.inH5?500:200,t=this;q(function(e,t){let i=!1;return function(){clearTimeout(i),i&&clearTimeout(i),i=setTimeout((()=>{i=!1,e.apply(this,arguments)}),t)}}((function(e){if(1==t.mixinDatacomLoading)return;let i=t.mixinDatacomErrorMessage;null!==i&&"null"!==i&&""!==i||(t.echarts?t.echartsResize.state=!t.echartsResize.state:t.resizeHandler())}),e))},destroyed(){!0===this.echarts?(delete Nn.option[this.cid],delete Nn.instance[this.cid]):(delete zn.option[this.cid],delete zn.instance[this.cid]),$((()=>{}))},watch:{chartDataProps:{handler(e,t){"object"==typeof e?JSON.stringify(e)!==JSON.stringify(t)&&(this._clearChart(),e.series&&e.series.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this.showchart=!1,this.mixinDatacomErrorMessage=null)):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：chartData数据类型错误")},immediate:!1,deep:!0},localdata:{handler(e,t){JSON.stringify(e)!==JSON.stringify(t)&&(e.length>0?this.beforeInit():(this.mixinDatacomLoading=!0,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage=null))},immediate:!1,deep:!0},optsProps:{handler(e,t){"object"==typeof e?JSON.stringify(e)!==JSON.stringify(t)&&!1===this.echarts&&1==this.optsWatch&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this._clearChart(),this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：opts数据类型错误")},immediate:!1,deep:!0},eoptsProps:{handler(e,t){"object"==typeof e?JSON.stringify(e)!==JSON.stringify(t)&&!0===this.echarts&&this.checkData(this.drawData):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：eopts数据类型错误")},immediate:!1,deep:!0},reshow(e,t){!0===e&&!1===this.mixinDatacomLoading&&setTimeout((()=>{this.mixinDatacomErrorMessage=null,this.echartsResize.state=!this.echartsResize.state,this.checkData(this.drawData)}),200)},reload(e,t){!0===e&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())},mixinDatacomErrorMessage(e,t){e&&(this.emitMsg({name:"error",params:{type:"error",errorShow:this.errorShow,msg:e,id:this.cid}}),this.errorShow&&console.log("[秋云图表组件]"+e))},errorMessage(e,t){e&&this.errorShow&&null!==e&&"null"!==e&&""!==e?(this.showchart=!1,this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e):(this.showchart=!1,this.mixinDatacomErrorMessage=null,this.reloading())}},computed:{optsProps(){return JSON.parse(JSON.stringify(this.opts))},eoptsProps(){return JSON.parse(JSON.stringify(this.eopts))},chartDataProps(){return JSON.parse(JSON.stringify(this.chartData))}},methods:{beforeInit(){this.mixinDatacomErrorMessage=null,"object"==typeof this.chartData&&null!=this.chartData&&void 0!==this.chartData.series&&this.chartData.series.length>0?(this.drawData=Gn({},this.chartData),this.mixinDatacomLoading=!1,this.showchart=!0,this.checkData(this.chartData)):this.localdata.length>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.localdataInit(this.localdata)):""!==this.collection?(this.mixinDatacomLoading=!1,this.getCloudData()):this.mixinDatacomLoading=!0},localdataInit(e){if(this.groupEnum.length>0)for(let h=0;h<e.length;h++)for(let t=0;t<this.groupEnum.length;t++)e[h].group===this.groupEnum[t].value&&(e[h].group=this.groupEnum[t].text);if(this.textEnum.length>0)for(let h=0;h<e.length;h++)for(let t=0;t<this.textEnum.length;t++)e[h].text===this.textEnum[t].value&&(e[h].text=this.textEnum[t].text);let t=!1,i={categories:[],series:[]},a=[],o=[];if(t=!0===this.echarts?Nn.categories.includes(this.type):zn.categories.includes(this.type),!0===t){if(this.chartData&&this.chartData.categories&&this.chartData.categories.length>0)a=this.chartData.categories;else if(this.startDate&&this.endDate){let e=new Date(this.startDate),t=new Date(this.endDate);for(;e<=t;)a.push((r=void 0,s=void 0,l=void 0,r=(n=e).getFullYear(),s=n.getMonth()+1,l=n.getDate(),s>=1&&s<=9&&(s="0"+s),l>=0&&l<=9&&(l="0"+l),r+"-"+s+"-"+l)),e=e.setDate(e.getDate()+1),e=new Date(e)}else{let t={};e.map((function(e,i){null==e.text||t[e.text]||(a.push(e.text),t[e.text]=!0)}))}i.categories=a}var n,r,s,l;let c={};if(e.map((function(e,t){null==e.group||c[e.group]||(o.push({name:e.group,data:[]}),c[e.group]=!0)})),0==o.length)if(o=[{name:"默认分组",data:[]}],!0===t)for(let h=0;h<a.length;h++){let t=0;for(let i=0;i<e.length;i++)e[i].text==a[h]&&(t=e[i].value);o[0].data.push(t)}else for(let h=0;h<e.length;h++)o[0].data.push({name:e[h].text,value:e[h].value});else for(let h=0;h<o.length;h++)if(a.length>0)for(let t=0;t<a.length;t++){let i=0;for(let n=0;n<e.length;n++)o[h].name==e[n].group&&e[n].text==a[t]&&(i=e[n].value);o[h].data.push(i)}else for(let t=0;t<e.length;t++)o[h].name==e[t].group&&o[h].data.push(e[t].value);i.series=o,this.drawData=Gn({},i),this.checkData(i)},reloading(){!1!==this.errorReload&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,""!==this.collection?(this.mixinDatacomLoading=!1,this.onMixinDatacomPropsChange(!0)):this.beforeInit())},checkData(e){let t=this.cid;!0===this.echarts?(Nn.option[t]=Gn({},this.eopts),Nn.option[t].id=t,Nn.option[t].type=this.type):this.type&&zn.type.includes(this.type)?(zn.option[t]=Gn({},zn[this.type],this.opts),zn.option[t].canvasId=t):(this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：props参数中type类型不正确");let i=Gn({},e);void 0!==i.series&&i.series.length>0&&(this.mixinDatacomErrorMessage=null,!0===this.echarts?(Nn.option[t].chartData=i,this.$nextTick((()=>{this.init()}))):(zn.option[t].categories=i.categories,zn.option[t].series=i.series,this.$nextTick((()=>{this.init()}))))},resizeHandler(){let e=Date.now();e-(this.lastDrawTime?this.lastDrawTime:e-3e3)<1e3||H().in(this).select("#ChartBoxId"+this.cid).boundingClientRect((e=>{this.showchart=!0,e.width>0&&e.height>0&&(e.width===this.cWidth&&e.height===this.cHeight||this.checkData(this.drawData))})).exec()},getCloudData(){1!=this.mixinDatacomLoading&&(this.mixinDatacomLoading=!0,this.mixinDatacomGet().then((e=>{this.mixinDatacomResData=e.result.data,this.localdataInit(this.mixinDatacomResData)})).catch((e=>{this.mixinDatacomLoading=!1,this.showchart=!1,this.mixinDatacomErrorMessage="请求错误："+e})))},onMixinDatacomPropsChange(e,t){1==e&&""!==this.collection&&(this.showchart=!1,this.mixinDatacomErrorMessage=null,this._clearChart(),this.getCloudData())},_clearChart(){let e=this.cid;if(!0!==this.echarts&&zn.option[e]&&zn.option[e].context){const t=zn.option[e].context;"object"!=typeof t||zn.option[e].update||(t.clearRect(0,0,this.cWidth*this.pixel,this.cHeight*this.pixel),t.draw())}},init(){let e=this.cid;H().in(this).select("#ChartBoxId"+e).boundingClientRect((t=>{t.width>0&&t.height>0?(this.mixinDatacomLoading=!1,this.showchart=!0,this.lastDrawTime=Date.now(),this.cWidth=t.width,this.cHeight=t.height,!0!==this.echarts&&(zn.option[e].background="rgba(0,0,0,0)"==this.background?"#FFFFFF":this.background,zn.option[e].canvas2d=this.type2d,zn.option[e].pixelRatio=this.pixel,zn.option[e].animation=this.animation,zn.option[e].width=t.width*this.pixel,zn.option[e].height=t.height*this.pixel,zn.option[e].onzoom=this.onzoom,zn.option[e].ontap=this.ontap,zn.option[e].ontouch=this.ontouch,zn.option[e].onmouse=this.openmouse,zn.option[e].onmovetip=this.onmovetip,zn.option[e].tooltipShow=this.tooltipShow,zn.option[e].tooltipFormat=this.tooltipFormat,zn.option[e].tooltipCustom=this.tooltipCustom,zn.option[e].inScrollView=this.inScrollView,zn.option[e].lastDrawTime=this.lastDrawTime,zn.option[e].tapLegend=this.tapLegend),this.inH5||this.inApp?1==this.echarts?(Nn.option[e].ontap=this.ontap,Nn.option[e].onmouse=this.openmouse,Nn.option[e].tooltipShow=this.tooltipShow,Nn.option[e].tooltipFormat=this.tooltipFormat,Nn.option[e].tooltipCustom=this.tooltipCustom,Nn.option[e].lastDrawTime=this.lastDrawTime,this.echartsOpts=Gn({},Nn.option[e])):(zn.option[e].rotateLock=zn.option[e].rotate,this.uchartsOpts=Gn({},zn.option[e])):(zn.option[e]=Kn(zn.option[e],zn.formatter),this.mixinDatacomErrorMessage=null,this.mixinDatacomLoading=!1,this.showchart=!0,this.$nextTick((()=>{if(!0===this.type2d){H().in(this).select("#"+e).fields({node:!0,size:!0}).exec((i=>{if(i[0]){const a=i[0].node,o=a.getContext("2d");zn.option[e].context=o,zn.option[e].rotateLock=zn.option[e].rotate,zn.instance[e]&&zn.option[e]&&!0===zn.option[e].update?this._updataUChart(e):(a.width=t.width*this.pixel,a.height=t.height*this.pixel,a._width=t.width*this.pixel,a._height=t.height*this.pixel,setTimeout((()=>{zn.option[e].context.restore(),zn.option[e].context.save(),this._newChart(e)}),100))}else this.showchart=!1,this.mixinDatacomErrorMessage="参数错误：开启2d模式后，未获取到dom节点，canvas-id:"+e}))}else this.inAli&&(zn.option[e].rotateLock=zn.option[e].rotate),zn.option[e].context=G(e,this),zn.instance[e]&&zn.option[e]&&!0===zn.option[e].update?this._updataUChart(e):setTimeout((()=>{zn.option[e].context.restore(),zn.option[e].context.save(),this._newChart(e)}),100)})))):(this.mixinDatacomLoading=!1,this.showchart=!1,1==this.reshow&&(this.mixinDatacomErrorMessage="布局错误：未获取到父元素宽高尺寸！canvas-id:"+e))})).exec()},saveImage(){K({canvasId:this.cid,success:e=>{var t=document.createElement("a");t.href=e.tempFilePath,t.download=this.cid,t.target="_blank",t.click()}},this)},getImage(){if(0==this.type2d)K({canvasId:this.cid,success:e=>{this.emitMsg({name:"getImage",params:{type:"getImage",base64:e.tempFilePath}})}},this);else{H().in(this).select("#"+this.cid).fields({node:!0,size:!0}).exec((e=>{if(e[0]){const t=e[0].node;this.emitMsg({name:"getImage",params:{type:"getImage",base64:t.toDataURL("image/png")}})}}))}},_error(e){this.mixinDatacomErrorMessage=e.detail.errMsg},emitMsg(e){this.$emit(e.name,e.params)},getRenderType(){!0===this.echarts&&!1===this.mixinDatacomLoading&&this.beforeInit()},toJSON(){return this}}};Hn(Jn);const Xn=se(Jn,[["render",function(e,t,i,d,p,u){const f=a(o("qiun-loading"),Ya),x=g,m=a(o("qiun-error"),Qa),y=V;return n(),r(x,{class:"chartsview",id:"ChartBoxId"+p.cid},{default:s((()=>[e.mixinDatacomLoading?(n(),r(x,{key:0},{default:s((()=>[l(f,{loadingType:i.loadingType},null,8,["loadingType"])])),_:1})):h("",!0),e.mixinDatacomErrorMessage&&i.errorShow?(n(),r(x,{key:1,onClick:u.reloading},{default:s((()=>[l(m,{errorMessage:i.errorMessage},null,8,["errorMessage"])])),_:1},8,["onClick"])):h("",!0),p.echarts?J((n(),r(x,{key:2,style:c([{background:i.background},{width:"100%",height:"100%"}]),"data-directory":i.directory,id:"EC"+p.cid,prop:p.echartsOpts,"change:prop":e.rdcharts.ecinit,resize:p.echartsResize,"change:resize":e.rdcharts.ecresize},null,8,["style","data-directory","id","prop","change:prop","resize","change:resize"])),[[X,p.showchart]]):(n(),r(x,{key:3,onClick:e.rdcharts.tap,onMousemove:e.rdcharts.mouseMove,onMousedown:e.rdcharts.mouseDown,onMouseup:e.rdcharts.mouseUp,onTouchstart:e.rdcharts.touchStart,onTouchmove:e.rdcharts.touchMove,onTouchend:e.rdcharts.touchEnd,id:"UC"+p.cid,prop:p.uchartsOpts,"change:prop":e.rdcharts.ucinit},{default:s((()=>[J(l(y,{id:p.cid,canvasId:p.cid,style:c({width:p.cWidth+"px",height:p.cHeight+"px",background:i.background}),"disable-scroll":i.disableScroll,onError:u._error},null,8,["id","canvasId","style","disable-scroll","onError"]),[[X,p.showchart]])])),_:1},8,["onClick","onMousemove","onMousedown","onMouseup","onTouchstart","onTouchmove","onTouchend","id","prop","change:prop"]))])),_:1},8,["id"])}],["__scopeId","data-v-29a3556e"]]),Vn={__name:"home-chart",setup(e){const t=Y({}),i=Y({color:["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],padding:[15,15,0,5],enableScroll:!1,legend:{},xAxis:{disableGrid:!0},yAxis:{data:[{min:0}]},extra:{column:{type:"group",width:30,activeBgColor:"#000000",activeBgOpacity:.08}}});return setTimeout((()=>{t.value=JSON.parse(JSON.stringify({categories:["2018","2019","2020","2021","2022","2023"],series:[{name:"目标值",data:[35,36,31,33,13,34]},{name:"完成量",data:[18,27,21,24,6,28]}]}))}),500),(e,c)=>{const h=a(o("tui-section"),ye),d=a(o("qiun-data-charts"),Xn),p=g;return n(),r(p,null,{default:s((()=>[l(h,{padding:"20rpx 50rpx",title:"图表（示例）","is-line":"","line-cap":"square","line-right":20,background:"#fff",size:28}),l(d,{type:"column",opts:i.value,chartData:t.value},null,8,["opts","chartData"]),l(d,{type:"line",opts:i.value,chartData:t.value},null,8,["opts","chartData"])])),_:1})}}};const Yn={__name:"add-pop",emits:{ok:null},setup(t,{expose:i,emit:c}){const h=Y(),d=Y(),f=Y({scheduleTime:"",scheduleContent:""}),x=e({scheduleTime:[{type:"string",required:!0,message:"请选择时间"}],scheduleContent:[{type:"string",required:!0,message:"请输入日程描述"}]}),m=()=>{d.value.validate().then((e=>{var t;(t=f.value,Q({url:"/sys/index/schedule/add",method:"post",data:t})).then((e=>{c("ok",e),h.value.close(),f.value={}}))}))},y=()=>{h.value.close()};return i({onOpen:e=>{f.value.scheduleDate=e,h.value.open()}}),(e,t)=>{const i=Z,c=g,v=ee,b=a(o("uv-form-item"),pe),S=a(o("uv-input"),ue),w=a(o("uv-form"),ge),T=a(o("tui-button"),fe),_=a(o("uv-popup"),xe);return n(),r(_,{ref_key:"popRef",ref:h,mode:"bottom","bg-color":"null","z-index":"99"},{default:s((()=>[l(c,{class:"container"},{default:s((()=>[l(c,{class:"close"},{default:s((()=>[l(i,{type:"clear",size:20,color:"#5677fc",onClick:y})])),_:1}),l(w,{ref_key:"formRef",ref:d,model:f.value,rules:x,"label-position":"top",labelWidth:"auto",labelStyle:{marginBottom:"25rpx",fontSize:"27rpx",color:"#606266"}},{default:s((()=>[l(b,{label:"时间",prop:"scheduleTime",required:!0},{default:s((()=>[l(v,{style:{width:"100%"},mode:"time",value:f.value.scheduleTime,onChange:t[0]||(t[0]=e=>{f.value.scheduleTime=e.detail.value})},{default:s((()=>[l(c,{class:"uni-input input-value-border"},{default:s((()=>[p(u(f.value.scheduleTime?f.value.scheduleTime:"请选择时间"),1)])),_:1})])),_:1},8,["value"])])),_:1}),l(b,{label:"日程描述",prop:"scheduleContent",required:!0},{default:s((()=>[l(S,{type:"textarea",modelValue:f.value.scheduleContent,"onUpdate:modelValue":t[1]||(t[1]=e=>f.value.scheduleContent=e),placeholder:"请输入日程描述"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),l(T,{height:"90rpx",type:"primary",onClick:m},{default:s((()=>[p("确认")])),_:1})])),_:1})])),_:1},512)}}},Qn=se(Yn,[["__scopeId","data-v-1e3b493e"]]),Zn={__name:"index",setup(e){const t=Y([]),i=Y(uni.$xeu.toDateString(new Date,"yyyy-MM-dd")),c=Y(),h=()=>{var e;(e={scheduleDate:i.value},Q({url:"/sys/index/schedule/list",method:"get",data:e})).then((e=>{t.value=e.data}))};h();const d=e=>{i.value=e.fulldate,h()},f=()=>{c.value.onOpen(i)},x=e=>{const t=[{id:e.id}];var i;(i=t,Q({url:"/sys/index/schedule/deleteSchedule",method:"post",data:i})).then((()=>{h()}))};return(e,i)=>{const m=a(o("tui-section"),ye),y=a(o("uv-calendars"),le),v=g,b=a(o("uv-icon"),ce),S=a(o("uv-col"),he),w=a(o("uv-row"),de);return n(),r(v,null,{default:s((()=>[l(m,{padding:"20rpx 50rpx",title:"日程","is-line":"","line-cap":"square","line-right":20,background:"#fff",size:28}),l(y,{insert:!0,lunar:!0,onChange:d,showMonth:!1}),l(v,{class:"add-schedule snowy-bold",onClick:f},{default:s((()=>[p(" 新增 ")])),_:1}),(n(!0),te(ie,null,ae(t.value,(e=>(n(),r(v,{class:"item",key:e.id},{default:s((()=>[l(w,null,{default:s((()=>[l(S,{span:"1"},{default:s((()=>[l(b,{size:"18",name:"clock-fill",color:"#5677fc",onClick:t=>x(e)},null,8,["onClick"])])),_:2},1024),l(S,{span:"8"},{default:s((()=>[l(v,{class:"item-left snowy-bold snowy-ellipsis"},{default:s((()=>[p(u(e.scheduleContent),1)])),_:2},1024)])),_:2},1024),l(S,{span:"2",textAlign:"right"},{default:s((()=>[l(v,{class:"item-right snowy-bold snowy-ellipsis"},{default:s((()=>[p(u(e.scheduleTime),1)])),_:2},1024)])),_:2},1024),l(S,{span:"1"},{default:s((()=>[l(v,{class:"snowy-flex-end"},{default:s((()=>[l(b,{size:"18",name:"trash-fill",color:"#e43d33",onClick:t=>x(e)},null,8,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),l(Qn,{ref_key:"addPopRef",ref:c,onOk:i[0]||(i[0]=e=>h())},null,512)])),_:1})}}},er=se(Zn,[["__scopeId","data-v-53fd4b3b"]]),tr=se({__name:"item",props:{code:{type:String,default:"",required:!0},isShow:{type:Boolean,default:!1,required:!0}},setup:e=>(t,i)=>{const a=g;return n(),r(a,null,{default:s((()=>[["swiper"].includes(e.code)&&e.isShow?(n(),r(a,{key:0},{default:s((()=>[l(me)])),_:1})):h("",!0),["chart"].includes(e.code)&&e.isShow?(n(),r(a,{key:1,class:"item snowy-shadow"},{default:s((()=>[l(Vn)])),_:1})):h("",!0),["schedule"].includes(e.code)&&e.isShow?(n(),r(a,{key:2,class:"item snowy-shadow"},{default:s((()=>[l(er)])),_:1})):h("",!0)])),_:1})}},[["__scopeId","data-v-00906aba"]]),ir=se({__name:"index",setup(e){const i=oe((()=>t.getters.homeConfigs));return(e,t)=>{const a=g;return n(),r(a,{class:"home-container"},{default:s((()=>[(n(!0),te(ie,null,ae(ne(i),((e,t)=>(n(),r(tr,{index:t,key:t,code:e.code,isShow:e.isShow},null,8,["index","code","isShow"])))),128))])),_:1})}}},[["__scopeId","data-v-7348094d"]]);export{ir as default};
