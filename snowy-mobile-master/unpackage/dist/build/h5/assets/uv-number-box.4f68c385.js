var t,e;import{ab as s,ac as i,l as n,m as u,o as l,b as o,w as a,ap as r,v as h,ae as c,q as d,e as m,h as p,i as b,I as g}from"./index-58695647.js";import{_ as f}from"./uv-icon.019f93fe.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";const S=y({name:"uv-number-box",mixins:[s,i,{props:{value:{type:[String,Number],default:0},modelValue:{type:[String,Number],default:0},name:{type:[String,Number],default:""},min:{type:[String,Number],default:1},max:{type:[String,Number],default:Number.MAX_SAFE_INTEGER},step:{type:[String,Number],default:1},integer:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},disabledInput:{type:Boolean,default:!1},asyncChange:{type:Boolean,default:!1},inputWidth:{type:[String,Number],default:35},showMinus:{type:Boolean,default:!0},showPlus:{type:Boolean,default:!0},decimalLength:{type:[String,Number,null],default:null},longPress:{type:Boolean,default:!0},color:{type:String,default:"#323233"},buttonSize:{type:[String,Number],default:30},bgColor:{type:String,default:"#EBECEE"},cursorSpacing:{type:[String,Number],default:100},disablePlus:{type:Boolean,default:!1},disableMinus:{type:Boolean,default:!1},iconStyle:{type:[Object,String],default:""},...null==(e=null==(t=uni.$uv)?void 0:t.props)?void 0:e.numberBox}}],data:()=>({currentValue:"",longPressTimer:null}),watch:{watchChange(t){this.check()},value(t){t!==this.currentValue&&(this.currentValue=this.format(this.value))},modelValue(t){t!==this.currentValue&&(this.currentValue=this.format(this.modelValue))}},computed:{getCursorSpacing(){return this.$uv.getPx(this.cursorSpacing)},buttonStyle(){return t=>{const e={backgroundColor:this.bgColor,height:this.$uv.addUnit(this.buttonSize),color:this.color};return this.isDisabled(t)&&(e.backgroundColor="#f7f8fa"),e}},inputStyle(){this.disabled||this.disabledInput;return{color:this.color,backgroundColor:this.bgColor,height:this.$uv.addUnit(this.buttonSize),width:this.$uv.addUnit(this.inputWidth)}},watchChange(){return[this.integer,this.decimalLength,this.min,this.max]},isDisabled(){return t=>"plus"===t?this.disabled||this.disablePlus||this.currentValue>=this.max:this.disabled||this.disableMinus||this.currentValue<=this.min}},created(){this.init()},methods:{init(){const t=this.value||this.modelValue;this.currentValue=this.format(t)},format(t){return t=""===(t=this.filter(t))?0:+t,t=Math.max(Math.min(this.max,t),this.min),null!==this.decimalLength&&(t=t.toFixed(this.decimalLength)),t},filter(t){return t=String(t).replace(/[^0-9.-]/g,""),this.integer&&-1!==t.indexOf(".")&&(t=t.split(".")[0]),t},check(){const t=this.format(this.currentValue);t!==this.currentValue&&(this.currentValue=t)},onFocus(t){this.$emit("focus",{...t.detail,name:this.name})},onBlur(t){this.format(t.detail.value),this.$emit("blur",{...t.detail,name:this.name})},onInput(t){const{value:e=""}=t.detail||{};if(""===e)return;let s=this.filter(e);if(null!==this.decimalLength&&-1!==s.indexOf(".")){const t=s.split(".");s=`${t[0]}.${t[1].slice(0,this.decimalLength)}`}s=this.format(s),this.emitChange(s)},emitChange(t){this.asyncChange||this.$nextTick((()=>{this.$emit("input",t),this.$emit("update:modelValue",t),this.currentValue=t,this.$forceUpdate()})),this.$emit("change",{value:t,name:this.name})},onChange(){const{type:t}=this;if(this.isDisabled(t))return this.$emit("overlimit",t);const e="minus"===t?-this.step:+this.step,s=this.format(this.add(+this.currentValue,e));this.emitChange(s),this.$emit(t)},add(t,e){const s=Math.pow(10,10);return Math.round((t+e)*s)/s},clickHandler(t){this.type=t,this.onChange()},longPressStep(){this.clearTimeout(),this.longPressTimer=setTimeout((()=>{this.onChange(),this.longPressStep()}),250)},onTouchStart(t){this.longPress&&(this.clearTimeout(),this.type=t,this.longPressTimer=setTimeout((()=>{this.onChange(),this.longPressStep()}),600))},onTouchEnd(){this.longPress&&this.clearTimeout()},clearTimeout(){clearTimeout(this.longPressTimer),this.longPressTimer=null}}},[["render",function(t,e,s,i,y,S){const v=b,T=n(u("uv-icon"),f),_=g;return l(),o(v,{class:"uv-number-box"},{default:a((()=>[t.showMinus&&t.$slots.minus?(l(),o(v,{key:0,class:"uv-number-box__slot",onClick:e[0]||(e[0]=r((t=>S.clickHandler("minus")),["stop"])),onTouchstart:e[1]||(e[1]=t=>S.onTouchStart("minus")),onTouchend:r(S.clearTimeout,["stop"])},{default:a((()=>[h(t.$slots,"minus",{},void 0,!0)])),_:3},8,["onTouchend"])):t.showMinus?(l(),o(v,{key:1,class:c(["uv-number-box__minus",{"uv-number-box__minus--disabled":S.isDisabled("minus")}]),onClick:e[2]||(e[2]=r((t=>S.clickHandler("minus")),["stop"])),onTouchstart:e[3]||(e[3]=t=>S.onTouchStart("minus")),onTouchend:r(S.clearTimeout,["stop"]),"hover-class":"uv-number-box__minus--hover","hover-stay-time":"150",style:d([S.buttonStyle("minus")])},{default:a((()=>[m(T,{name:"minus",color:S.isDisabled("minus")?"#c8c9cc":"#323233",size:"15",bold:"",customStyle:t.iconStyle},null,8,["color","customStyle"])])),_:1},8,["onTouchend","class","style"])):p("",!0),h(t.$slots,"input",{},(()=>[m(_,{disabled:t.disabledInput||t.disabled,"cursor-spacing":S.getCursorSpacing,class:c([{"uv-number-box__input--disabled":t.disabled||t.disabledInput},"uv-number-box__input"]),modelValue:y.currentValue,"onUpdate:modelValue":e[4]||(e[4]=t=>y.currentValue=t),onBlur:S.onBlur,onFocus:S.onFocus,onInput:S.onInput,type:"number",style:d([S.inputStyle])},null,8,["disabled","cursor-spacing","class","modelValue","onBlur","onFocus","onInput","style"])]),!0),t.showPlus&&t.$slots.plus?(l(),o(v,{key:2,class:"uv-number-box__slot",onClick:e[5]||(e[5]=r((t=>S.clickHandler("plus")),["stop"])),onTouchstart:e[6]||(e[6]=t=>S.onTouchStart("plus")),onTouchend:r(S.clearTimeout,["stop"])},{default:a((()=>[h(t.$slots,"plus",{},void 0,!0)])),_:3},8,["onTouchend"])):t.showPlus?(l(),o(v,{key:3,class:c(["uv-number-box__plus",{"uv-number-box__minus--disabled":S.isDisabled("plus")}]),onClick:e[7]||(e[7]=r((t=>S.clickHandler("plus")),["stop"])),onTouchstart:e[8]||(e[8]=t=>S.onTouchStart("plus")),onTouchend:r(S.clearTimeout,["stop"]),"hover-class":"uv-number-box__plus--hover","hover-stay-time":"150",style:d([S.buttonStyle("plus")])},{default:a((()=>[m(T,{name:"plus",color:S.isDisabled("plus")?"#c8c9cc":"#323233",size:"15",bold:"",customStyle:t.iconStyle},null,8,["color","customStyle"])])),_:1},8,["onTouchend","class","style"])):p("",!0)])),_:3})}],["__scopeId","data-v-bc43264f"]]);export{S as _};
