/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-b7a6dd5d], uni-scroll-view[data-v-b7a6dd5d], uni-swiper-item[data-v-b7a6dd5d] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
@font-face {
  font-family: "uvicon-iconfont";
  src: url("../../../assets/uvicons.04d281cc.ttf") format("truetype");
}
.uv-icon[data-v-b7a6dd5d] {
  display: flex;
  align-items: center;
}
.uv-icon--left[data-v-b7a6dd5d] {
  flex-direction: row-reverse;
  align-items: center;
}
.uv-icon--right[data-v-b7a6dd5d] {
  flex-direction: row;
  align-items: center;
}
.uv-icon--top[data-v-b7a6dd5d] {
  flex-direction: column-reverse;
  justify-content: center;
}
.uv-icon--bottom[data-v-b7a6dd5d] {
  flex-direction: column;
  justify-content: center;
}
.uv-icon__icon[data-v-b7a6dd5d] {
  font-family: uvicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-icon__icon--primary[data-v-b7a6dd5d] {
  color: #3c9cff;
}
.uv-icon__icon--success[data-v-b7a6dd5d] {
  color: #5ac725;
}
.uv-icon__icon--error[data-v-b7a6dd5d] {
  color: #f56c6c;
}
.uv-icon__icon--warning[data-v-b7a6dd5d] {
  color: #f9ae3d;
}
.uv-icon__icon--info[data-v-b7a6dd5d] {
  color: #909399;
}
.uv-icon__img[data-v-b7a6dd5d] {
  height: auto;
  will-change: transform;
}
.uv-icon__label[data-v-b7a6dd5d] {
  line-height: 1;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-border[data-v-651602aa] {
  border-width: 0.5px !important;
  border-color: #dadbde !important;
  border-style: solid;
}
.uv-border-bottom[data-v-651602aa] {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
uni-view[data-v-651602aa], uni-scroll-view[data-v-651602aa], uni-swiper-item[data-v-651602aa] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-input[data-v-651602aa] {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.uv-input--radius[data-v-651602aa], .uv-input--square[data-v-651602aa] {
  border-radius: 4px;
}
.uv-input--no-radius[data-v-651602aa] {
  border-radius: 0;
}
.uv-input--circle[data-v-651602aa] {
  border-radius: 100px;
}
.uv-input__content[data-v-651602aa] {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.uv-input__content__field-wrapper[data-v-651602aa] {
  position: relative;
  display: flex;
  flex-direction: row;
  margin: 0;
  flex: 1;
}
.uv-input__content__field-wrapper__field[data-v-651602aa] {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
}
.uv-input__content__clear[data-v-651602aa] {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  background-color: #c6c7cb;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transform: scale(0.82);
  margin-left: 15px;
}
.uv-input__content__subfix-icon[data-v-651602aa] {
  margin-left: 4px;
}
.uv-input__content__prefix-icon[data-v-651602aa] {
  margin-right: 4px;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.uv-line[data-v-dcf8cb8f] {
  vertical-align: middle;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
uni-view[data-v-d1e73275], uni-scroll-view[data-v-d1e73275], uni-swiper-item[data-v-d1e73275] {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.uv-form-item[data-v-d1e73275] {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #303133;
}
.uv-form-item__body[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  padding: 10px 0;
}
.uv-form-item__body__left[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-form-item__body__left__content[data-v-d1e73275] {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-right: 0.3125rem;
  flex: 1;
}
.uv-form-item__body__left__content__icon[data-v-d1e73275] {
  margin-right: 0.25rem;
}
.uv-form-item__body__left__content__required[data-v-d1e73275] {
  position: absolute;
  left: -9px;
  color: #f56c6c;
  line-height: 20px;
  font-size: 20px;
  top: 3px;
}
.uv-form-item__body__left__content__label[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  color: #303133;
  font-size: 15px;
}
.uv-form-item__body__right[data-v-d1e73275] {
  flex: 1;
}
.uv-form-item__body__right__content[data-v-d1e73275] {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.uv-form-item__body__right__content__slot[data-v-d1e73275] {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uv-form-item__body__right__content__icon[data-v-d1e73275] {
  margin-left: 0.3125rem;
  color: #c0c4cc;
  font-size: 0.9375rem;
}
.uv-form-item__body__right__message__box[data-v-d1e73275] {
  height: 16px;
  line-height: 16px;
}
.uv-form-item__body__right__message[data-v-d1e73275] {
  margin-top: -6px;
  line-height: 24px;
  font-size: 12px;
  color: #f56c6c;
}

.tui-button__wrap[data-v-835d2781] {
		position: relative;
}
.tui-button__hover[data-v-835d2781]:active::after {
		content: '';
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.15);
		border-radius: 0.1875rem;
		pointer-events: none;
}

	/* button start*/
.tui-btn[data-v-835d2781] {
		width: 100%;
		position: relative;
		border: 0 !important;
		box-sizing: border-box;
		border-radius: 0.1875rem;
		padding-left: 0;
		padding-right: 0;
		overflow: visible;
		display: flex;
		align-items: center;
		justify-content: center;
}
.tui-btn[data-v-835d2781]::after {
		border: 0;
}
.tui-btn__flex-1[data-v-835d2781] {
		flex: 1;
}
.tui-button__border[data-v-835d2781] {
		position: absolute;
		width: 200%;
		height: 200%;
		transform-origin: 0 0;
		transform: scale(0.5, 0.5) translateZ(0);
		box-sizing: border-box;
		left: 0;
		top: 0;
		border-radius: 0.375rem;
		border: 1px solid transparent;
		pointer-events: none;
}
.tui-text-bold[data-v-835d2781] {
		font-weight: bold;
}
.tui-dark-disabled[data-v-835d2781] {
		opacity: 0.6 !important;
		color: #fafbfc !important;
}
.tui-dark-disabled-outline[data-v-835d2781] {
		opacity: 0.5 !important;
}
.tui-gray-disabled[data-v-835d2781] {
		background: #f3f3f3 !important;
		color: #919191 !important;
		box-shadow: none;
}

	/*圆角 */
.tui-fillet[data-v-835d2781] {
		border-radius: 6.875rem !important;
}
.tui-fillet[data-v-835d2781]::after {
		border-radius: 6.875rem !important;
}
.tui-outline-fillet[data-v-835d2781] {
		border-radius: 6.875rem !important;
}
.tui-outline-fillet[data-v-835d2781]::after {
		border-radius: 6.875rem !important;
}

	/*平角*/
.tui-rightAngle[data-v-835d2781] {
		border-radius: 0 !important;
}
.tui-rightAngle[data-v-835d2781]::after {
		border-radius: 0 !important;
}
.tui-outline-rightAngle[data-v-835d2781] {
		border-radius: 0 !important;
}
.tui-outline-rightAngle[data-v-835d2781]::after {
		border-radius: 0 !important;
}
.tui-btn__link[data-v-835d2781]::after {
		border: 0 !important;
}

/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
/* 水平间距 */
.pwd-container[data-v-6338975c] {
  padding: 0.9375rem;
}