package vip.xiaonuo.choiceway.modular.process.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.choiceway.modular.process.entity.MesProcess;
import vip.xiaonuo.choiceway.modular.process.param.*;

import java.util.List;

/**
 * MES流程Service接口
 *
 * <AUTHOR>
 * @date 2025/07/23
 **/
public interface MesProcessService extends IService<MesProcess> {

    /**
     * 获取流程分页
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    Page<MesProcess> page(MesProcessPageParam mesProcessPageParam);

    /**
     * 添加流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void add(MesProcessAddParam mesProcessAddParam);

    /**
     * 编辑流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void edit(MesProcessEditParam mesProcessEditParam);

    /**
     * 删除流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void delete(List<MesProcessIdParam> mesProcessIdParamList);

    /**
     * 获取流程详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    MesProcess detail(MesProcessIdParam mesProcessIdParam);

    /**
     * 获取流程详情
     *
     * <AUTHOR>
     * @date 2025/07/23
     **/
    MesProcess queryEntity(String id);

    /**
     * 创建新版本流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void createNewVersion(MesProcessVersionParam mesProcessVersionParam);

    /**
     * 启用流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void enableProcess(MesProcessIdParam mesProcessIdParam);

    /**
     * 禁用流程
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void disableProcess(MesProcessIdParam mesProcessIdParam);

    /**
     * 获取启用的流程列表
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    List<MesProcess> getEnabledProcessList();

    /**
     * 保存流程配置（包含节点信息）
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void saveProcessWithNodes(MesProcessAddParam mesProcessAddParam, List<MesProcessListAddParam> nodeList);

    /**
     * 更新流程配置（包含节点信息）
     *
     * <AUTHOR>
     * @date 2025/07/23
     */
    void updateProcessWithNodes(MesProcessEditParam mesProcessEditParam, List<MesProcessListAddParam> nodeList);
}
