# MES管理系统实现文档

## 项目概述

基于Snowy框架实现的MES（制造执行系统）管理系统，包含流程设计、工单管理、投料、进出站、入库和事务历史等核心功能。

## 实现的功能模块

### 1. 流程设计模块
- **功能**：可视化流程图绘制、流程版本管理、站点动态配置
- **核心实体**：`MesProcess`
- **主要接口**：
  - 流程CRUD操作
  - 流程版本创建
  - 流程启用/禁用
  - 获取启用流程列表

### 2. 工单管理模块
- **功能**：工单创建编辑、SN码管理、物料绑定
- **核心实体**：`MesWorkOrder`、`MesMaterial`
- **主要接口**：
  - 工单CRUD操作
  - 物料绑定/解绑
  - 获取工单物料列表

### 3. 投料管理模块
- **功能**：批次生成、投料操作、状态初始化
- **核心实体**：`MesBatch`
- **主要接口**：
  - 投料生成批次
  - 批次信息查询

### 4. 进出站管理模块
- **功能**：批次进站/出站操作、状态流转控制
- **核心实体**：`MesBatch`
- **主要接口**：
  - 批次进站
  - 批次出站
  - 获取站点批次列表

### 5. 入库管理模块
- **功能**：批量入库操作、状态变更管理
- **核心实体**：`MesBatch`
- **主要接口**：
  - 批次入库
  - 批次状态查询

### 6. 事务历史模块
- **功能**：完整操作记录、历史查询、操作追踪
- **核心实体**：`MesTransaction`
- **主要接口**：
  - 事务记录
  - 历史查询（按批次/工单）

## 技术架构

### 后端架构
- **框架**：Spring Boot 3.2.1 + MyBatis Plus
- **数据库**：MySQL 8.0+
- **权限**：Sa-Token
- **API文档**：Knife4j

### 前端架构
- **框架**：Vue 3 + Ant Design Vue
- **构建工具**：Vite
- **状态管理**：Pinia

## 数据库设计

### 核心表结构
1. **mes_process** - 流程表
2. **mes_work_order** - 工单表
3. **mes_material** - 物料表
4. **mes_batch** - 批次表
5. **mes_transaction** - 事务记录表

### 状态流转
- **批次状态**：WAIT（待进站）→ RUN（运行中）→ WAIT（待进站）→ SHIP（已入库）
- **操作类型**：FEED（投料）、ENTER（进站）、EXIT（出站）、WAREHOUSE（入库）

## 项目结构

```
snowy-plugin-api/snowy-plugin-choiceway-api/     # API接口定义
├── entity/                                      # 实体类
├── service/                                     # 服务接口
└── param/                                       # 参数类

snowy-plugin/snowy-plugin-choiceway/             # 具体实现
├── controller/                                  # 控制器
├── service/impl/                               # 服务实现
├── mapper/                                     # Mapper接口
└── resources/db/                               # 数据库脚本

snowy-admin-web/src/                            # 前端代码
├── api/choiceway/                              # API接口
└── views/choiceway/                            # 页面组件
```

## 部署说明

### 1. 数据库初始化
执行 `snowy-plugin/snowy-plugin-choiceway/src/main/resources/db/mes_tables.sql` 创建表结构

### 2. 后端部署
1. 确保已添加choiceway插件依赖到主项目
2. 启动Spring Boot应用
3. 访问 http://localhost:82/doc.html 查看API文档

### 3. 前端部署
1. 前端API接口已创建在 `snowy-admin-web/src/api/choiceway/`
2. 示例页面在 `snowy-admin-web/src/views/choiceway/`
3. 需要配置路由和菜单权限

## API接口说明

### 流程管理
- `GET /choiceway/process/page` - 获取流程分页
- `POST /choiceway/process/add` - 添加流程
- `POST /choiceway/process/edit` - 编辑流程
- `POST /choiceway/process/delete` - 删除流程
- `POST /choiceway/process/createNewVersion` - 创建新版本

### 工单管理
- `GET /choiceway/workOrder/page` - 获取工单分页
- `POST /choiceway/workOrder/add` - 添加工单
- `POST /choiceway/workOrder/bindMaterial` - 绑定物料

### 批次管理
- `POST /choiceway/batch/feed` - 投料生成批次
- `POST /choiceway/batch/enter` - 批次进站
- `POST /choiceway/batch/exit` - 批次出站
- `POST /choiceway/batch/warehouse` - 批次入库

### 事务历史
- `GET /choiceway/transaction/historyByBatch` - 根据批次ID获取历史
- `GET /choiceway/transaction/historyByWorkOrder` - 根据工单ID获取历史

## 使用流程

1. **流程设计**：创建生产流程，定义站点和流转路径
2. **工单创建**：输入SN码、产品名称，选择流程，绑定物料
3. **投料操作**：选择工单进行投料，生成批次ID
4. **进出站**：使用批次ID进行进站/出站操作
5. **入库**：批次完成后进行入库操作
6. **历史查询**：通过批次ID查询完整操作历史

## 扩展建议

1. **流程设计器**：集成可视化流程设计器（如bpmn.js）
2. **实时监控**：添加生产状态实时监控大屏
3. **报表统计**：增加生产效率、质量统计报表
4. **移动端**：开发移动端扫码进出站功能
5. **设备集成**：集成生产设备数据采集
6. **质量管控**：添加质量检验和不良品处理流程

## 注意事项

1. 确保数据库连接配置正确
2. 需要配置相应的权限和菜单
3. 生产环境需要优化数据库索引
4. 建议添加数据备份和恢复机制
5. 考虑高并发场景下的性能优化
